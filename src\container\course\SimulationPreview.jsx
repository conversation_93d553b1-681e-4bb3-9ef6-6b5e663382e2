/* eslint-disable arrow-body-style */
/* eslint-disable no-const-assign */
/* eslint-disable react/jsx-key */
/* eslint-disable react/no-danger */
/* eslint-disable consistent-return */
/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-alert */
/* eslint no-else-return: "error" */
/* eslint no-var: off */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Box, IconButton, Grid, Typography, Switch } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { styled, useTheme } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MobileStepper from '@mui/material/MobileStepper';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import clsx from 'clsx';
import moment from 'moment';
import Draggable from 'react-draggable';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/mode-python';
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/ext-language_tools';
import 'ace-builds/src-noconflict/snippets/python';
import DOMPurify from "dompurify";
import CircularProgress from '@mui/material/CircularProgress';

import Button from '@mui/material/Button';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import { useSearchParams, useLocation, useNavigate } from 'react-router-dom';
import swal from 'sweetalert';
import { useTranslation } from 'react-i18next';
import PlayCircleIcon from '@mui/icons-material/PlayCircle';

import { useDispatch, useSelector } from 'react-redux';

import { setKeyIntelligence } from '../../store/reducer';
import LottieLoading from '../../components/LottieLoading';
import Page from '../../components/Page';
import SubModuleProgress from '../../components/progress/SubModuleProgress';
import Snackbar from '../../components/snackbar/snackbar';
import TopicList from './TopicsList';
import VideoTopicList from './VideoTopicList';
import palette from '../../theme/palette';
import simulationApi from '../../services/simulation';
import VideoPlayer from './VideoPlayer';
import helper from '../../utils/helper';
import customeKeys from './customeKeys';
import ArrowIcon from './ArrowIcon';
import ReferenceScreen from './ReferenceScreen';
import ClientAdminServices from '../../services/clientAdmin/course/clientAdminServices';
import '../admin/courseDetails/styles.css';


const SimulationPreview = () => {

  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const containerRef = useRef(null);
  const userRole = useSelector((state) => state.userInfo && state.userInfo.role);
  const reduxDetails = useSelector((state) => state);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const [activeTopic, setActiveTopic] = useState(null);
  const [videoURL, setVideoURL] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [topics, setTopics] = useState([]);
  console.log(topics,"topics");
  
  const [screens, setScreens] = useState([]);
  const [activeKeyIndex, setActiveKeyIndex] = useState(0);
  const [searchParams] = useSearchParams();
  const [subModuleData, setSubModuleData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [enableForwardArrow, setenableForwardArrow] = useState(false);
  const [enableBackArrow, setEnableBackArrow] = useState(false);
  console.log(subModuleData,"subModuleData");

  // eslint-disable-next-line no-unused-vars
  const [refData, setRefData] = useState('');
  const [id, setid] = useState(0);
  const [name, setnmae] = useState('');
  const [modulename, setmodulename] = useState('')
  const [showReference, setShowReference] = useState(false);
  const [isWrongKeyPressed, setIsWrongKeyPressed] = useState(false);
  const [mic, setMic] = useState(true);
  const [keyboard, setKeyboard] = useState('');
  const [isLoadingScreens, setLoadingScreens] = useState(false)
  const [count, setCount] = useState(0);
  const [autoAudioPlay, setAutoAudioPlay] = useState(true);
  let actionType = '';
  const [status, setStatus] = useState(true);
  const [actionKeyType, setActionKeyType] = useState('');
  const dispatch = useDispatch();
  const nextButtonRef = useRef(null);
  const backButtonRef = useRef(null);
  const [resumeChecked, setResumeChecked] = useState(false);
  const [details, setDetails] = useState('');
  const time = new Date().toLocaleTimeString();
  const now = moment(new Date()).format('YYYY-MM-DD H:mm:ss')
  const currentDate = now.substring(0, 10)

  const dateString = ['SELECT CURDATE()'];
  const timeString = ['SELECT CURTIME()'];
  const nowString = ['SELECT NOW()']
  const formatdateString = [`DATE_FORMAT(NOW(),'%d-%m-%Y')`]

  const classes = useStyles();
  function checkUserInput(inputLines) {

    var inputPrompt
    var userInputs = []
    var beforePrompt
    var inputText
    var i = 0
    var regex = /input\(["']([^"']*)["']\);?/;
    var inputTextRegex = new RegExp(regex);
    var inputTextOnly

    if (inputLines.includes("input(")) {

      inputPrompt = inputLines.match(/input\(["']([^"']*)["']\);?/g);

      while (inputPrompt.length > 0 && (i < inputPrompt.length)) {

        inputTextOnly = inputTextRegex.exec(inputPrompt[i])[1];
        inputText = prompt(inputTextOnly);

        if (inputText !== null) {
          userInputs.push(inputText);
          // eslint-disable-next-line prefer-template       
          beforePrompt = inputLines.replace(inputPrompt[i], "'" + userInputs[i] + "'");
          inputLines = beforePrompt;
        }
        i += 1

      }
    }
    return inputLines
  }

  function getOutputText(codeValue, result) {

    const isDateFunction = codeValue.includes(dateString)
    const isTimeFunction = codeValue.includes(timeString)
    const isNowFunction = codeValue.includes(nowString)
    const isFormatFunction = codeValue.includes(formatdateString)
    let output

    if (isDateFunction) {
      output = currentDate
    }
    else if (isTimeFunction) {
      output = time.substring(0, 8)
    } else if (isNowFunction) {
      output = now
    }
    else if (isFormatFunction) {
      output = moment(new Date()).format('DD-MM-YYYY')
    }
    else {
      output = result
    }
    return output
  }

  // below states are used for code editor

  // get topics
  useEffect(() => {
    getOS();
  }, []);

  useEffect(() => {
    let videoURL = "";
    if (screens[activeStep]?.videoUrl || screens[activeStep]?.video) {
      if (screens[activeStep]?.videoUrl) {
        videoURL = screens[activeStep].videoUrl;
      } else if (screens[activeStep]?.video) {
        videoURL = screens[activeStep].video;
      }
      setVideoURL(videoURL);
    }
    else {
      setVideoURL('');

    }
  }, [screens, activeStep])


  useEffect(() => {

    if (keyboard !== '') {
      if (count > 1) {
        console.log('keyboard in useEffect count', count);
        // addBase64(keyboard);
      } else {
        getTopicsFromApi();
      }
    }
    setCount(count + 1);
  }, [keyboard]);

  const videoClickHandle = (item, type) => {
    var selectedTopic
    if (type === 'video') {
      selectedTopic = item
      if (selectedTopic.type === 'VIDEO') {
        actionType = 'VIDEO';
      }
      setActionKeyType(actionType);
      setIsLoading(true);
      setActiveStep(0);
      setActiveKeyIndex(0);
      setActiveTopic(topics[0]);
      getScreens(topics[0].id, actionType);

    } else {
      selectedTopic = topics[0];
      if (selectedTopic.actionType?.code === 'VIDEO') {
        actionType = 'VIDEO';

      }

      setActionKeyType(actionType);
      setIsLoading(true);
      setActiveStep(0);
      setActiveKeyIndex(0);

      setActiveTopic(selectedTopic);
      getScreens(selectedTopic.id, actionType);
    }

  };

  const { t } = useTranslation('translation');

  const postVideoResult = async () => {
    // if (screens[activeStep].isCompleted === false) {
    //   const payload = {
    //     topicId: topics[0].id,
    //     screenId: screens[activeStep].id,
    //   }

    //   try {
    //     const response = await adminServices.updateisCompleteResult(payload)
    //     if (response.ok) {
    //       console.log(response,"marked video as complete")

    //       setScreens(prevScreens => 
    //         prevScreens.map((screen, index) => index === activeStep ? { ...screen, isCompleted: true } : screen
    //       )
    //       );

    CallBackDetails(activeStep)

    //     } else {
    //       console.log("error marking video as complete")
    //     }
    //   } catch (error) {
    //     console.log(error);
    //   }
    // }
  }

  const CallBackDetails = (activeStep) => {
    setDetails(activeStep)
  }

  // useEffect(() => {
  //   window.addEventListener('popstate', (event) => {
  //     navigate('/app/course-content',{
  //         state: location.state?.id,
  //       }
  //     );

  //   });
  // }, [])


  const handleNextVideo = (event) => {
    const currentscreen = screens[activeStep]
    if (currentscreen && currentscreen.type === "VIDEO") {
      // if(currentscreen.isCompleted === true){
      if (activeStep === screens?.length - 1) {
        // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
        const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
        const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
        if (topics?.length > nextTopicIndex) {
          topicClickHandle(nextTopicIndex);
        } else {
          navigateToNextSubmodule();
        }
      } else {
        setVideoURL(screens[activeStep + 1].video)
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        setIsLoading(false);
      }
      // }
    }
    else if (currentscreen && currentscreen.type !== "VIDEO") {
      if (activeStep === screens?.length - 1) {
        // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
        const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
        const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
        if (topics?.length > nextTopicIndex) {
          topicClickHandle(nextTopicIndex);
        } else {
          navigateToNextSubmodule();
        }
      } else {
        setVideoURL(screens[activeStep + 1].video)
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        setIsLoading(false);
      }
    }

  };
  useEffect(() => {
    if (location.state?.type === 'video') {
      const handleKeyDown = (event) => {
        if (event.key === 'Enter') {
          console.log(activeStep, "activestepYYYY", screens?.length, "screens?.length YYYY");
          event.preventDefault();
          if (activeStep < screens?.length - 1) {
            handleNextVideo();
          }
        }
      };
      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [location.state?.type, activeStep, screens, handleNextVideo]);

  // const handleBackVideo = (event) => {
  //   const currentscreen = screens[activeStep]
  //   if (currentscreen && currentscreen.type === "VIDEO") {
  //     if (activeStep === 0) {
  //       setIsLoading(false);
  //       const prevTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) - 1;
  //       if (prevTopicIndex >= 0) {
  //         topicClickHandle(prevTopicIndex);
  //       } else {
  //         navigateToNextSubmodule();
  //       }
  //     } else {
  //       setIsLoading(false);
  //       alert("11111111111")
  //       setActiveStep((prevActiveStep) => prevActiveStep - 1);
  //     }
  //     // setVideoURL(screens[activeStep - 1]?.video)

  //     // setActiveStep((prevActiveStep) => prevActiveStep - 1);
  //     // setIsLoading(false);

  //   }
  //   else if (currentscreen && currentscreen.type !== "VIDEO") {
  //     setVideoURL(screens[activeStep - 1]?.video)
  //     setActiveStep((prevActiveStep) => prevActiveStep === 0 ? prevActiveStep:prevActiveStep - 1);   
  //     setIsLoading(false);
  //   }
  // };



  const handleBackVideo = (event) => {

    const currentScreen = screens[activeStep]

    if (currentScreen && currentScreen?.type === "VIDEO") {
      if (activeStep === 0) {
        setIsLoading(false);
        const prevTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) - 1;
        if (prevTopicIndex >= 0) {
          topicClickHandle(prevTopicIndex);
        } else {
          navigateToNextSubmodule();
        }
      } else {
        setIsLoading(false);
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        setVideoURL(screens[activeStep - 1]?.video)
      }



    }

    else {
      // hasClickedBack.current = true;
      event.target.blur();
      if (activeStep === 0) {
        const prevTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) - 1;
        if (prevTopicIndex >= 0) {
          topicClickHandle(prevTopicIndex);
        } else {
          navigateToNextSubmodule();
        }
      } else {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        setVideoURL(screens[activeStep - 1]?.video)

      }
    }
  };

  const getBase64FromUrl = async (url = 'https://ik.imagekit.io/k38yuwpb2/windows1654795623.jpeg') => {
    const data = await fetch(url);
    const blob = await data.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => {
        const base64data = reader.result;
        resolve(base64data);
      };
    });
  };

  async function getOS() {
    const { userAgent } = window.navigator;
    const platform = window.navigator?.userAgentData?.platform || window.navigator.platform;
    const macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K', 'macOS'];
    const windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'];
    const iosPlatforms = ['iPhone', 'iPad', 'iPod'];
    let os = 'mac';

    if (macosPlatforms.indexOf(platform) !== -1) {
      os = 'mac';
    } else if (iosPlatforms.indexOf(platform) !== -1) {
      os = 'mac';
    } else if (windowsPlatforms.indexOf(platform) !== -1) {
      os = 'windows';
    } else if (/Android/.test(userAgent)) {
      os = 'windows';
    } else if (/Linux/.test(platform)) {
      os = 'windows';
    }
    setKeyboard(os);

    return os;
  }

  const getTopicsFromApi = async (subModuleId = searchParams.get('id')) => {
    setIsLoading(true);
    simulationApi
      .getTopics(subModuleId)
      .then((res) => {
        if (res.ok) {

          const submModulevideoUrl = res.data?.videoUrl || '';
          const subModuleId = res.data?.id || '';
          const subModuleName = res.data?.courseSubmoduleName || '';
          console.log("res.data....", res.data);
          const subModuleRefText = res.data?.referenceText ? res.data?.referenceText : res.data?.referenceData
          setRefData(subModuleRefText);

          setVideoURL(submModulevideoUrl);
          setid(subModuleId);
          setnmae(subModuleName);
          const topics = res.data.subModuleTopics;

          // Separate video item from topics and add it at the beginning of the list
          const videoItem = {
            id: 'video',
            courseSubmoduleTopics: 'Video',
            isCompleted: false,
            actionType: { code: 'VIDEO' },
          };
          let sortedTopics = topics;

          if (submModulevideoUrl && subModuleId && subModuleName) {
            sortedTopics = [videoItem, ...topics];
          }
          if (subModuleRefText != null) {
            const ReferenceItem = {
              id: 'reference',
              courseSubmoduleTopics: 'Reference',
              isCompleted: false,
              actionType: { code: 'REFERENCE' },
            };
            // Add referenceItem at the end
            sortedTopics.push(ReferenceItem);
          }

          // Assuming setTopics is a state setter for topics
          const SkillSet = {
            id: 'skillset',
            courseSubmoduleTopics: `${t('SkillSet IQ')}`,
            isCompleted: false,
            actionType: { code: 'SkillSet' },
            type: 'SkillSet',
          };

          sortedTopics.push(SkillSet);
          setTopics(sortedTopics);

          console.log("sortedTopics....", sortedTopics);


          if (res.data?.subModuleTopics[0]?.actionType?.code === 'CODE') {
            actionType = 'CODE';
          } else if (res.data?.subModuleTopics[0]?.actionType?.code === 'CLICK') {
            actionType = 'CLICK';
          } else {
            actionType = 'HOTKEYS';
          }
          setActionKeyType(actionType);

          const dataSort = sortedTopics.sort(helper.sortByPositionIndex);
          setSubModuleData({
            name: res.data.courseSubmoduleName,
            progress: res.data.completed,
            skillsetIq: res.data.skillsetIq,
          });
          // setReferenceContent(ReferenceItem);
          setmodulename(res.data.courseSubmoduleName);
          setTopics(dataSort);



          if (!resumeChecked && searchParams.get('topic')) {
            const qpTopic = searchParams.get('topic');
            const resumeTopicIndex = dataSort.findIndex((item) => item.id.toString() === qpTopic);
            setActiveTopic(dataSort[resumeTopicIndex]);
            getScreens(dataSort[resumeTopicIndex].id, actionType, submModulevideoUrl, subModuleId, subModuleName, subModuleRefText);
          } else {
            setActiveTopic(dataSort[0]);
            getScreens(dataSort[0].id, actionType, submModulevideoUrl, subModuleId, subModuleName, subModuleRefText);
          }
        } else if (res.status === 400) {
          if (res.data.message === 'Subscription_Required') {
            if (reduxDetails.isLogin) {
              alert(t('Access denied. You need to subscribe to get access to this submodule.'));

              if (userRole === 'USER_DTC') {
                navigate('/auth/my-courses');
              } else {
                navigate('/app/course');
              }
            } else {
              alert(t('Access denied. You need to subscribe to get access to this submodule.'));
              navigate('/login');
            }
          }
        }
      })
      .catch((error) => {
        console.log('error data getting from api', error);
        setIsLoading(false);
      });
  };

  const updateTopics = (data) => {
    const completedTopics = data.subModuleTopics.filter(topic => topic.completed);
    const allTopicsCompleted = completedTopics.length === data.subModuleTopics.length;

    // Get the video URL
    const videoUrl = data.videoUrl || '';

    // Create the video item
    const videoItem = {
      id: 'video',
      courseSubmoduleTopics: 'Video',
      isCompleted: false,
      actionType: { code: 'VIDEO' },
    };

    const id = data.id || '';
    const name = data.courseSubmoduleName || '';
    const referenceText = data.referenceText || '';

    const ReferenceItem = {
      id: 'Reference',
      courseSubmoduleTopics: 'Reference',
      isCompleted: false,
      actionType: { code: 'REFERENCE' },
    };
    const SkillSet = {
      id: 'skillset',
      courseSubmoduleTopics: `${t('SkillSet IQ')}`,
      isCompleted: false,
      actionType: { code: 'SkillSet' },
      type: 'SkillSet',
    };
    // Sort the topics
    const sortedTopics = data.subModuleTopics.sort(helper.sortByPositionIndex);
    sortedTopics.push(SkillSet);

    // Initialize the dataSort array with sorted topics
    let dataSort = sortedTopics;

    // Prepend the video item if videoUrl, id, and name exist
    if (videoUrl && id && name) {

      dataSort = [videoItem, ...dataSort];
    } if (referenceText) {

      dataSort = [...dataSort, ReferenceItem];
    }
    // Assuming setTopics is a state setter for topics
    setTopics(dataSort);

    console.log("dataSort....", dataSort);

    setSubModuleData({
      name: data.courseSubmoduleName,
      progress: data.completed,
      skillsetIq: data.skillsetIq,
    });
    setTopics(dataSort);



    if (allTopicsCompleted && data.completed === '50' && data.skillsetIq === false) {
      swal({
        text: 'You have successfully completed all the topics. You can now take the skillset IQ.',
        icon: 'success',
        buttons: {
          confirm: {
            text: 'Take SkillSet IQ',
            value: true,
            visible: true,
            className: '',
            closeModal: true,
          },
          cancel: {
            text: 'Continue Learning',
            value: null,
            visible: true,
            className: '',
            closeModal: true,
          },
        },
        closeOnClickOutside: false,
      }).then((res) => {
        if (res) {
          const keyType = { actionType: actionKeyType };
          if (userRole === 'USER_DTC') {
            navigate(`/auth/skilliq-test?subModuleId=${searchParams.get('id')}`, {
              state: { ...location.state, ...keyType },
            });
          } else {
            navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, {
              state: { ...location.state, ...keyType },
            });
          }
        }
      });
    }
  };



  const topicClickHandle = (index) => {
    
    const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
    let selectedTopic = ''
    if(filteredIds?.length === index){
      selectedTopic = topics[0];
    }
    else{
      selectedTopic = topics[index];
    }


    
    
    // if(index < filteredIds?.length){
    //   topic = filteredIds;
    //   selectedTopic = topic[0];
    // }
    // else{
    //   topic = filteredIds;
    //    selectedTopic = topic[index];
    // }
  
    setShowReference(false);

    if (selectedTopic.actionType?.code === 'CODE') {
      actionType = 'CODE';
    } else if (selectedTopic.actionType?.code === 'CLICK') {
      actionType = 'CLICK';
    } else {
      actionType = 'HOTKEYS';
    }
    setActionKeyType(actionType);
    setIsLoading(true);
    setActiveStep(0);
    setActiveKeyIndex(0);
    setActiveTopic(selectedTopic);
    getScreens(selectedTopic.id, actionType);


    const buttonElement = document.getElementById("nextbutton");
    buttonElement?.focus();

  };

  console.log(screens[activeStep],"screens[activeStep]");
  console.log(location.state,"location.statelocation.state");


  const referenceButtonClick = (event) => {
    const selectedTopic = topics[topics.length - 1];
    if (selectedTopic.actionType?.code === 'REFERENCE') {
      actionType = 'REFERENCE';

    }
    setActionKeyType(actionType);
    setIsLoading(true);
    setActiveStep(0);
    setActiveKeyIndex(0);
    const ReferenceItem = {
      id: 'reference',
      courseSubmoduleTopics: 'Reference',
      isCompleted: false,
      actionType: { code: 'REFERENCE' },
      type: 'REFERENCE'
    };

    setActiveTopic(ReferenceItem);    
    getScreens('reference', 'REFERENCE');
  };

  // const clickHandle = (item, step) => {
  //   console.log("inside click handle", item, step)
  //   setIsLoading(true);    
  //   setActiveStep(step);
  //   setActiveKeyIndex(0);
  //   setActiveTopic(topics[0]);
  //   setIsLoading(false);

  //   if(item.type === 'VIDEO'){
  //    console.log('screens[activeStep].video',item)
  //    setVideoURL(item.video)
  //    console.log('video url',videoURL)
  //   }

  // }

  const clickHandle = async (item, step, screenlist, activeStep) => {

    // setActiveScreen(item.id)
    setIsLoading(true);
    if (step === screenlist?.length) {
      console.log("1111111111111");

    }
    else {
      if (screens.length === 1 && screens[0]?.type === 'reference') {
        setScreens(screenlist)
      }

      // setActiveStep(step)
      // setActiveKeyIndex(0);
      // const selectedTopic = topics[step];
      // setActiveTopic(selectedTopic);
      // if (item && item?.type === 'VIDEO') {
      //   setVideoURL(item.video)
      // }

      const selectedTopic = topics[step];
      setActiveStep(0);
      setActiveKeyIndex(0);
      setActiveTopic(selectedTopic);
      getScreens(selectedTopic.id, actionType);
      const buttonElement = document.getElementById("nextbutton");
      buttonElement?.focus();

    }
    setIsLoading(false)

  }

  const addBase64 = async (keyboard) => {
    const sortedData = screens;
    setIsLoading(true);
    await Promise.all(
      sortedData.map(async (item, index) => {
        if (item.type === 'SUCCESS' || item.type === 'INTRO') {
          if (item?.backgroundImg !== null) {
            const base64Img = await getBase64FromUrl(item.backgroundImg);
            sortedData[index] = {
              ...item,
              backgroundBase64: base64Img,
            };
          }
          else if (item?.templates) {
            const base64Img = await getBase64FromUrl(item.templates);

            sortedData[index] = {
              ...item,
              backgroundBase64: base64Img,
            };
          }
        }

        // if the screen type is action and key is combined
        if (item.type === 'ACTION' && item.keyType === 'COMBINED_HOT_KEYS') {
          const url = item.keyObj[0][keyboard].backgroundImg;
          if (url) {
            const base64Img = await getBase64FromUrl(url);
            console.log(`Action Combined url ${url}`);
            sortedData[index].keyObj[0][keyboard] = {
              ...item.keyObj[0][keyboard],
              backgroundBase64: base64Img,
            };
          }
        }
        if (item.type === 'ACTION' && item.keyType === 'INDIVIDUAL_HOT_KEYS') {
          await Promise.all(
            item.keyObj.map(async (keys, _index) => {
              const url = keys[keyboard].backgroundImg;

              if (url) {
                const base64Img = await getBase64FromUrl(url);
                console.log('Action and individual', sortedData, 'index', index, sortedData[index].keyObj);
                sortedData[index].keyObj[_index][keyboard] = { ...keys[keyboard], backgroundBase64: base64Img };
              }
            })
          );
        }
        if (item.type === 'REFERENCE') {
          // Handle REFERENCE type here, if it requires base64 processing
          // Assuming it has a `backgroundImg` like other types
          const base64Img = await getBase64FromUrl(item.backgroundImg);
          console.log('Reference');
          sortedData[index] = {
            ...item,
            backgroundBase64: base64Img,
          };
        }
      }
      )
    );
    console.log('sortedData =>', sortedData)
    setScreens(sortedData);
    setIsLoading(false);
  };





  const getScreens = async (submoduleTopicId, actionType, videoURL, id, name, subModuleRefText) => {
    console.log('getScreens...', submoduleTopicId, actionType, videoURL, id, name)
    setIsLoading(true);
    setLoadingScreens(true);

    if (submoduleTopicId === 'video') {
      simulationApi
        .getTopics(searchParams.get('id'))
        .then((res) => {
          if (res.ok) {
            console.log(res.data, "Video Details");
            const videoURL = res.data.videoUrl;
            setScreens([{ videoUrl: videoURL, type: 'VIDEO', title: '' }]);
            setIsLoading(false);
            setLoadingScreens(false);
          } else {
            console.error('Failed to fetch video details:', res.error);
          }
        })
      // setScreens([{ videoUrl: videoURL, type: 'VIDEO', title: '' }]);

      return;
    }
    console.log(submoduleTopicId, 'submoduleTopicId');

    if (submoduleTopicId === 'reference' || submoduleTopicId === 'Reference') {
      console.log('Inside getScreens reference');
      setScreens([{ id, name, type: 'REFERENCE', title: 'Reference' }]);
      setIsLoading(false);
      setLoadingScreens(false);
      return;
    }
    console.log('Before calling API...', submoduleTopicId, actionType)
    simulationApi
      .getScreens(submoduleTopicId, actionType)
      .then(async (res) => {
        console.log('res..', res.data);
        try {
          if (res.ok) {
            // data order by asc from backend is done
            // const sortedData = res.data.sort(helper.sortByPositionIndex);

            let sortedData = res.data;
            console.log('First sortedData=>', sortedData);
            if (videoURL) {
              sortedData = [{ videoUrl: videoURL, type: 'VIDEO', title: '' }, ...res.data];
              console.log('Modified sortedData=>', sortedData);
            }
            // if(subModuleRefText){
            //   sortedData = [...res.data,{ id,name, type: 'REFERENCE', title: 'Reference' }];
            //    console.log('Modified  ref sortedData=>',sortedData);
            // }
            await Promise.all(
              sortedData.map(async (item, index) => {
                if (item?.type === 'SUCCESS' || item?.type === 'INTRO') {
                  console.log(item, "item?.backgroundImgitem?.backgroundImg");
                  if (item?.backgroundImg && item?.backgroundImg !== null) {
                    // const base64Img = await getBase64FromUrl(item?.backgroundImg);
                    sortedData[index] = {
                      ...item,
                      backgroundBase64: item?.backgroundImg,
                      // backgroundBase64: base64Img,
                    };
                  }

                }

                // if the screen type is action and key is combined
                if (item.type === 'ACTION' && item.keyType === 'COMBINED_HOT_KEYS') {
                  const url = item.keyObj[0][keyboard].backgroundImg;
                  if (url) {
                    // const base64Img = await getBase64FromUrl(url);
                    console.log(`Action Combined url ${url}`);
                    sortedData[index].keyObj[0][keyboard] = {
                      ...item.keyObj[0][keyboard],
                      // backgroundBase64: base64Img,
                      backgroundBase64: url,
                    };
                  }
                }
                if (item.type === 'ACTION' && item.keyType === 'INDIVIDUAL_HOT_KEYS') {
                  await Promise.all(
                    item.keyObj.map(async (keys, _index) => {
                      const url = keys[keyboard].windowspresignedurl;
                      if (url) {
                        // const base64Img = await getBase64FromUrl(url);
                        console.log('Action and individual', sortedData, 'index', index, sortedData[index].keyObj);
                        sortedData[index].keyObj[_index][keyboard] = {
                          ...keys[keyboard],
                          backgroundBase64: url,
                          //  base64Img
                        };
                      }
                    })
                  );
                }

                if (item.type === 'ACTION' && item.keyType === 'CLICK') {
                  const url = item.rangeObj[0].range.backgroundImg;
                  if (url) {
                    // const base64Img = await getBase64FromUrl(url);

                    sortedData[index].rangeObj[0].range.ranges = {
                      ...item.rangeObj[0].range.ranges,
                      backgroundBase64: url
                      //  base64Img,
                    };
                  }
                }
              })
            );
            console.log(sortedData, "sortedData latest");

            setScreens(sortedData);
            setIsLoading(false);
            setLoadingScreens(false);
            if (!resumeChecked && searchParams.get('screen')) {
              setResumeChecked(true);
              const qpScreenId = searchParams.get('screen');
              const screenIndex = sortedData.findIndex((item) => item.id.toString() === qpScreenId);
              setActiveStep(screenIndex);
            }
          }
        } catch (error) {
          console.log('error after data received from getScreens api', error);
          setIsLoading(false);
        }
      })
      .catch((error) => {
        console.log('error getting screens from api ', error);
        setIsLoading(false);
      });
  };

  useEffect(() => {

    if (screens.length > 0 && screens[activeStep]?.type) {
      const currentScreen = screens[activeStep];
      const isActionScreen = currentScreen.type === 'ACTION';
      const keyboardKeysGiven = isActionScreen && currentScreen.isCompleted;
      const isLastScreen = activeStep === screens.length - 1;
      // Enable forward arrow if keyboard keys are given in an action screen or if it's an intro, success, or last screen of the topic
      if ((currentScreen.type !== 'SUCCESS' && keyboardKeysGiven && isActionScreen && !isLastScreen) || currentScreen.type === 'INTRO' || currentScreen.keyType === 'CLICK') {
        setenableForwardArrow(true);
      } else {
        setenableForwardArrow(false);
      }

      const activeTopicIndex = topics.findIndex(topic => topic?.id === activeTopic?.id);
      setEnableBackArrow(activeTopicIndex > 0 || activeStep > 0);
      const isFirstScreen = activeStep === 0
      if (isFirstScreen) {
        setEnableBackArrow(false)
      }
      else {
        setEnableBackArrow(true)
      }
      console.log('ARROWS:', enableForwardArrow, enableBackArrow)
    }
    if (showReference) {
      console.log('Inside showReference', showReference)
    }
    if (screens[activeStep]) {
      let videoURL = "";

      if (screens[activeStep]?.videoUrl) {
        videoURL = screens[activeStep].videoUrl;
      } else if (screens[activeStep]?.video) {
        videoURL = screens[activeStep].video;
      }

      setVideoURL(videoURL);
    }
  }, [activeStep, screens, showReference]);

  const navigateToNextSubmodule = async () => {
    const subModuleId = parseInt(searchParams.get('id'), 10);
    const moduleName = searchParams.get('module'); // Use const instead of let

    const modules = await ClientAdminServices.getCourseModule(location.state.id);

    if (modules.ok) {
      const sortedModules = modules.data.sort((a, b) => a.positionIndex - b.positionIndex);

      const currentModuleIndex = sortedModules.findIndex((module) => module.courseModuleName === moduleName);

      if (currentModuleIndex !== -1 && currentModuleIndex < sortedModules.length - 1) {
        // There is another module after the current one
        const currentModule = sortedModules[currentModuleIndex];
        const currentSubmoduleIndex = currentModule.subModule.findIndex((submodule) => submodule.id === subModuleId);

        if (currentSubmoduleIndex !== -1 && currentSubmoduleIndex < currentModule.subModule.length - 1) {
          // There is another submodule after the current one in the current module
          const nextSubmodule = currentModule.subModule[currentSubmoduleIndex + 1];
          const nextSubmoduleId = nextSubmodule.id;

          if (userRole === 'USER_DTC') {
            navigate(`/auth/course-simulation?id=${nextSubmoduleId}&module=${moduleName}`, {
              replace: true,
              state: location.state,
            });
          } else {
            navigate(`/app/course-simulation?id=${nextSubmoduleId}&module=${moduleName}`, {
              replace: true,
              state: location.state,
            });
          }

          // setReload(1);
        } else {
          // Handle the case where there are no more submodules in the current module
          console.error('No more submodules found in the current module.');
        }
      } else {
        // Handle the case where there are no more modules
        console.error('No more modules found.');
      }
    } else {
      // Handle the case where fetching modules fails
      console.error('Failed to fetch modules.');
    }
  };

  console.log(screens,"screens");
  
  const handleArrowNext = () => {
    console.log('handle arrow next is being called..................')
    // topics
    const nextStep = activeStep + 1;
    const nextScreen = screens[nextStep];
    const currentScreen = screens[activeStep];

    if (nextScreen && nextScreen.type) {
      // Check if the current screen is an action screen and keyboard keys have been given
      if ((currentScreen.type === 'ACTION' && currentScreen.isCompleted) || currentScreen.type === 'INTRO' || currentScreen.keyType === 'CLICK') {
        setActiveStep(nextStep);

      }
    }
  };



  const handleArrowBack = () => {
    if (activeStep > 0) {
      setActiveStep(prevActiveStep => prevActiveStep - 1);
    } else {
      setEnableBackArrow(false);

    }
  };


  const handleNext = () => {
    setActiveKeyIndex(0);
    setIsLoading(true);
    if (activeStep === screens.length - 1) {
      // get next topic data and moving to next step
      const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
      const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
      // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;

      // if user in last topic completes the navigating to first topic
      if (topics.length > nextTopicIndex) {
        console.log('can go to next topic from skip');
        topicClickHandle(nextTopicIndex);
      } else {
        console.log('can not go to next topic from skip');
        topicClickHandle(0);
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setIsLoading(false);
    }
  };

  const handleBack = () => {

    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    const prevTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) - 1;
    topicClickHandle(prevTopicIndex)

  };

  console.log(screens[activeStep], "screens[activeStep]");
  console.log(activeStep, "activeStep");

  const introBtnClickHandle = () => {
    if (screens[activeStep].type === 'INTRO' || screens[activeStep].type === 'SUCCESS') {
      if (activeStep === screens.length - 1) {
        console.log('active');

        // get next topic data and moving to next step
        // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
        const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
        const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
        console.log(nextTopicIndex, 'nextTopicIndex');

        // if user in last topic completes the navigating to first topic
        if (topics.length > nextTopicIndex) {
          console.log('can go to next topic from skip');
          topicClickHandle(nextTopicIndex);
        } else {
          setAutoAudioPlay(!autoAudioPlay)
          console.log('can not go to next topic from skip');
          swal({
            // title: 'Good job!',
            text: t('You have reached the end of the this submodule'),
            buttons: {
              confirm: {
                text: 'Okay',
                value: true,
                visible: true,
                className: '',
                closeModal: true,
              },
            },
            closeOnClickOutside: false,
          }).then((res) => {
            console.log(res);
          });
          topicClickHandle(0);
        }
      } else {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }
    }
  };

  const handleMicToggle = () => {
    setMic(!mic);
  };

  const IntroScreen = () => {
    const horizontal = screens[activeStep].horizontalAlign.code === 'MIDDLE' ? 'center' : 'flex-start';
    const vertical = screens[activeStep].verticalAlign.code === 'CENTER' ? 'center' : 'start';

    const screen = screens[activeStep];
useEffect(() => {
    const container = containerRef.current;
    if (container) {      
      const elements = container.querySelectorAll('.editor-element');
      const baseWidth = 756;
      const baseHeight = 450;
      const effectiveWidth = container.offsetWidth
      const effectiveHeight = container.offsetHeight;
      const scaleFactor = effectiveWidth / baseWidth;
      const scaleFactorHeight = effectiveHeight / baseHeight;
      const heightDifference = effectiveHeight - baseHeight;
      const decimalHeightValue = parseFloat(`${heightDifference.toString()[0]}.${heightDifference.toString()[1]}`) || 0;

      let variableValue = 0;
      if (effectiveWidth < 800) {
        variableValue = -1;
      } 
      
      else if (effectiveWidth >= 800 && effectiveWidth < 850) {
        variableValue = -2;
      }
       else if (effectiveWidth >= 850 && effectiveWidth < 900) {
        variableValue = -3;
      } else if (effectiveWidth >= 900 && effectiveWidth < 950) {
        variableValue = -4;
      }
     else if (effectiveWidth >= 950 && effectiveWidth < 1000) {
        variableValue = -5;
      }
     
      else if (effectiveWidth >=  1000 && effectiveWidth < 1050) {
        variableValue = -3
      }
      else if (effectiveWidth >=  1050 && effectiveWidth < 1100) {
        variableValue = -5
      }
       else if (effectiveWidth >=  1100 && effectiveWidth < 1150) {
        variableValue = -6
      } else if (effectiveWidth >=  1150 && effectiveWidth < 1200) {
        variableValue = -7
      }
      else if (effectiveWidth >=1200 && effectiveWidth < 1400) {
        variableValue = -8
      }

      elements.forEach((el) => {
        const computedStyle = getComputedStyle(el);
        if (!el.dataset.originalWidth) {
          el.dataset.originalWidth = computedStyle.width;
          el.dataset.originalHeight = computedStyle.height;
          el.dataset.originalLeft = computedStyle.left;
          el.dataset.originalTop = computedStyle.top;
          el.dataset.originalFontSize = computedStyle.fontSize;
        }

        const originalWidth = parseFloat(el.dataset.originalWidth);
        const originalHeight = parseFloat(el.dataset.originalHeight);
        const originalLeft = parseFloat(el.dataset.originalLeft);
        const originalTop = parseFloat(el.dataset.originalTop);
        const originalFontSize = parseFloat(el.dataset.originalFontSize);

        // Apply scaled styles based on original values
        el.style.width = `${(originalWidth * scaleFactor)-10}px`;
        el.style.height = `${(originalHeight * scaleFactorHeight) - decimalHeightValue}px`;
        el.style.left = `${originalLeft * scaleFactor}px`;
        el.style.top = `${(originalTop * scaleFactorHeight) - decimalHeightValue + 10}px`;
        const scaledFontSize = (originalFontSize / baseWidth * effectiveWidth)+variableValue ;
        el.style.setProperty('font-size', `${scaledFontSize}px`, 'important');
      });
    }
  }, [activeStep]);

    let positionGet = '';
    try {
      if (screen && screen?.html_template) {
        const htmlTemplate = JSON.parse(screen?.html_template);

        if (htmlTemplate && htmlTemplate.position) {
          positionGet = htmlTemplate.position;
        }
      }
    } catch (error) {
      console.error("Error parsing html_template:", error);

    }
    const getAnimationClass = (position) => {
      console.log(position);
      switch (position) {
        case 'right-to-left':
          return 'scroll-text-right';
        case 'left-to-right':
          return 'scroll-text-left';
        case 'pop-up':
          return 'pop-up-animation';
        case 'delay-effect':
          return 'delay-effect';
        default:
          return '';
      }
    };

    if (screens && screens[activeStep]) {
      const jsonString = `{"design":{"counters":{"u_column":1,"u_row":1,"u_content_text":1},"body":{},"positionIs":"","position":"left-to-right"}}`;
      const parsedData = JSON.parse(jsonString);
      const { position } = parsedData.design;

    }

    const nextBtnElement = useRef();
    const [sfxPlayed, setsfxPlayed] = useState(false);

    useEffect(() => {
      setTimeout(() => {
        setsfxPlayed(true);
      }, 5000);
    }, []);

    useEffect(() => {
      window.addEventListener('keydown', handleKeyDownIntro);

      // removing EventListener when comp unmount
      return () => {
        window.removeEventListener('keydown', handleKeyDownIntro);
      };
    }, []);

    const handleKeyDownIntro = (event) => {


      if (event.repeat) {
        console.log('repeated');
        return false;
      }
      if (nextBtnElement && nextBtnElement.current)
        nextBtnElement.current.click();


      // }
    };

    const removeDollarSigns = (html) => {
      return html.replace(/\$(?=\S)/, '');
    };
    const formatOptionnew = (option) => {
      const sanitizedOption = DOMPurify.sanitize(option, {
        ALLOWED_TAGS: ['span', 'div', 'p', 'img', 'a', 'br'],
        ALLOWED_ATTR: ['href', 'src', 'alt', 'style'],
      });
      return sanitizedOption;
    };
    const formattedHtml = formatOptionnew(screens[activeStep]?.html_template !== null && screens[activeStep]?.html_template !== "undefined" && screens[activeStep]?.html_template !== undefined ? JSON.parse(screens[activeStep]?.html_template)?.html : "");
    const FinalHtml = removeDollarSigns(formattedHtml);
    useEffect(() => {
      if (window.MathJax) {
        window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub]);
      }
    }, [FinalHtml]);

   return (
  <Box
    className={`${classes.gridContainer} ${getAnimationClass(positionGet)}`}
    sx={{
      justifyContent: screens[activeStep].horizontalAlign.code === 'RIGHT' ? 'flex-end' : horizontal,
      alignItems: screens[activeStep].verticalAlign.code === 'BOTTOM' ? 'end' : vertical,
      margin: 0,
      ...(screens[activeStep]?.backgroundBase64
        ? {
            backgroundImage: screens.length > 0 ? `url(${screens[activeStep]?.backgroundBase64})` : '',
          }
        : {}),
    }}
  >
    <div ref={containerRef} style={{ width: '100%', height: '100%' }}>
      {screens[activeStep]?.html_template !== null &&
      screens[activeStep]?.html_template !== "undefined" &&
      screens[activeStep]?.html_template !== undefined ? (
        <div dangerouslySetInnerHTML={{ __html: JSON.parse(screens[activeStep]?.html_template)?.html }} />
      ) : (
        <>
          {(location?.state?.subCategory?.code !== 'PYTHON' &&
            location?.state?.subCategory?.code !== 'SEQUEL') &&
            screens.length > 0 &&
            screens[activeStep < screens.length ? activeStep : 0].description !== '' &&
            screens[activeStep < screens.length ? activeStep : 0].description.trim().replace(/<[^>]*>/g, '').length > 0 && (
              <Box className={classes.introContainer}>
                <div
                  style={{
                    minHeight: 60,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    sx={{ fontSize: '1.125rem' }}
                    dangerouslySetInnerHTML={{
                      __html:
                        screens.length > 0
                          ? screens[activeStep < screens.length ? activeStep : 0].description
                          : '&nbsp;',
                    }}
                  />
                </div>
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Button ref={nextBtnElement} sx={{marginBottom: '10px'}} variant="outlined" onClick={introBtnClickHandle}>
                    {screens[activeStep].buttonLabel || 'okay'}
                  </Button>
                </div>
              </Box>
            )}
          {location.state?.type !== 'video' && (
            <Button
              ref={nextBtnElement}
              variant="contained"
              color="primary"
              onClick={introBtnClickHandle}
              className={classes.extroTxt}
              sx={{
                color: 'orange',
                backgroundColor: 'rgba(255, 0, 0, 0)',
                fontSize: '0.675rem',
                position: 'absolute',
                bottom: '100px',
                visibility: 'hidden',
              }}
            >
              {t("Press any key to continue")}
            </Button>
          )}
        </>
      )}
    </div>
  </Box>
);

  };

  // eslint-disable-next-line react/prop-types
  const ActionScreen = ({ screenKeyType }) => {
    // extracting screen of active step or first step if derectly comes for clickin topics or start sub module in previous page
    const screenInfo = screens.length > 0 ? screens[activeStep < screens.length ? activeStep : 0] : null;
    let isAltKeyInclude = null;
    let keys = null;
    console.log('screenInfo', screenInfo);
    // extracting key types INDIVIDUAL_HOT_KEYS || COMBAINED_HOT_KEYS
    const { keyType } = screenInfo;

    // setting up keyBoard here WINDOWS || mac
    const keyBoard = keyboard.toLowerCase();

    // if screens exist extracting keyObj into keys
    if (screenInfo) {
      keys = screenInfo.keyObj;
    }
    if (keyType === 'COMBINED_HOT_KEYS') {
      isAltKeyInclude = keys[0][keyBoard].keyName?.includes('Alt');
    }

    else if (keyType === 'INDIVIDUAL_HOT_KEYS') {
      isAltKeyInclude = keys[activeKeyIndex][keyBoard]?.keyName?.includes('Alt');
    }
    // adding and removing keyboard event listener
    useEffect(() => {
      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);
      // removing EventListener when comp unmount
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
      };
    }, []);

    // resetting to focus on first key is keyup not completing
    const handleKeyUp = (event) => {
      event.preventDefault();
      if (keyType === 'COMBINED_HOT_KEYS') {
        const keyArray = keys[0][keyBoard].keyCode;
        if (event.keyCode.toString() !== keyArray[activeKeyIndex]) {
          if (isWrongKeyPressed) {
            setActiveKeyIndex(activeKeyIndex);
          } else {
            setActiveKeyIndex(0);
          }
        }
      }
    };

    const { multipleKeyCode } = customeKeys;
    const { codeAndNameDifferent } = customeKeys;

    const handleKeyDown = (event) => {
      event.preventDefault();
      // alert(`A key was pressed ${event.keyCode} activeIndex ${activeKeyIndex}`);
      setIsWrongKeyPressed(false);
      if (event.repeat) {
        return false;
      }
      const selectedKey = multipleKeyCode.filter((item) => item.key === event.key.toString());
      const isMultipleKey = selectedKey.length > 0 && selectedKey[0].keyCode.includes(event.keyCode);

      const { sameCodeForMultipleKeys } = customeKeys;
      const { ignoreNameCheckKeys } = customeKeys;

      let checkOnlyName = false;
      let checkOnlyCode = true; // default
      let checkCodeAndName = false;
      let checkCodeorName = false;
      let nameAndKeyValidated = false;

      if (sameCodeForMultipleKeys.includes(event.keyCode.toString())) {
        checkCodeAndName = true;
        checkOnlyName = false;
        checkOnlyCode = false;
      }

      if (isMultipleKey) {
        checkOnlyName = true;
        checkOnlyCode = false;
        checkCodeAndName = false;
      }

      if (keyType === 'INDIVIDUAL_HOT_KEYS') {
        const isKeyHasDifferantName = ignoreNameCheckKeys.filter((item) => item.key === event.key.toString());

        console.log('isKeyHasDifferantName', isKeyHasDifferantName);
        const differentKeyName =
          isKeyHasDifferantName.length > 0 &&
          isKeyHasDifferantName[0].keyCode.includes(keys[activeKeyIndex][keyBoard].keyCode[0]);

        if (differentKeyName && isMultipleKey) {
          nameAndKeyValidated = true;
        }

        const isKeyName =
          event.key.toString().toLowerCase() === keys[activeKeyIndex][keyBoard].keyName[0].toLowerCase();
        const isKeyCode = event.keyCode.toString() === keys[activeKeyIndex][keyBoard].keyCode[0];

        // statement for checking valid/right key is pressed
        if (
          (checkOnlyCode && checkOnlyName === false && checkCodeAndName === false && isKeyCode) ||
          (checkCodeAndName && checkOnlyName === false && checkOnlyCode === false && isKeyCode && isKeyName) ||
          (checkOnlyName && checkOnlyCode === false && checkCodeAndName === false && isKeyName) ||
          nameAndKeyValidated
        ) {
          // checking for last key if it has multiple keys
          if (activeKeyIndex === keys.length - 1) {
            // this statement is to check for last screen and moving to next topic
            if (activeStep === screens.length - 1) {
              console.log('screens end');
              // post result
              postResult(screenInfo);

              // clearing screens
              // setScreens([]);

              // resetting active screen to zero
              // setActiveStep(0);

              // get next topic data and moving to next step
              // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
              const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
              const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
              // const copyTopics = topics.map((topic, index) => ({ ...topic, selected: index === nextTopicIndex }));
              // console.log('selected : ', copyTopics);
              // setTopics(copyTopics);

              // if user in last topic completes the navigating to first topic
              if (topics.length > nextTopicIndex) {
                console.log('can go to next topic');
                topicClickHandle(nextTopicIndex);
              } else {
                console.log('can not go to next topic');
                // topicClickHandle(0);

                swal({
                  // title: 'Good job!',
                  text: 'You have reached the end of the this submodule. You can take the skillset IQ or go to next submodule.',
                  buttons: {
                    confirm: {
                      text: 'Take SkillSet IQ',
                      value: true,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                    cancel: {
                      text: 'Cancel',
                      value: null,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                  },
                  closeOnClickOutside: false,
                }).then((res) => {
                  if (res) {
                    const keyType = { actionType: actionKeyType };
                    if (userRole === 'USER_DTC') {
                      navigate(`/auth/skilliq-test?subModuleId=${searchParams.get('id')}`, {
                        state: { ...location.state, ...keyType },
                      });
                    } else {
                      navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, {
                        state: { ...location.state, ...keyType },
                      });
                    }
                  }
                });
              }

              // resetting active key index if has multiple keys
              setActiveKeyIndex(0);
            } else {
              setActiveKeyIndex(0);
              postResult(screenInfo);
              handleNext();
            }
          } else {
            // if is not last key the key index increase
            setActiveKeyIndex(activeKeyIndex + 1);
          }
        } else if (event.shiftKey && event.keyCode === 16) {
          console.log('shift key');
        } else {
          // wrong key is pressed
          setIsWrongKeyPressed(true);
          toggleClassName();
          console.log('wrong key entered : ', event.keyCode);
        }
      }

      if (keyType === 'COMBINED_HOT_KEYS') {
        console.log('Combined keys', keys[0][keyBoard]);
        const keyArray = keys[0][keyBoard].keyCode;
        const keyArrayName = keys[0][keyBoard].keyName;
        // custome condition to zoom in and out for XL sheet
        let isPlusOrMinus = false;
        if (event.ctrlKey && event.altKey && event.keyCode === 187) {
          isPlusOrMinus = true;
        } else if (event.altKey && event.metaKey && event.keyCode === 189) {
          isPlusOrMinus = true;
        } else if (event.altKey && event.metaKey && event.keyCode === 187) {
          isPlusOrMinus = true;
        } else {
          isPlusOrMinus = false;
        }

        const isKeyHasDifferantName = ignoreNameCheckKeys.filter((item) => item.key === event.key.toString());

        const differentKeyName =
          isKeyHasDifferantName.length > 0 && isKeyHasDifferantName[0].keyCode.includes(keyArray[activeKeyIndex]);

        if (differentKeyName && isMultipleKey) {
          nameAndKeyValidated = true;
        }

        // ignore name check in some of the case if alt key is pressed getting keyName as Dead insted actual key Name
        if (event.altKey || event.key.toString().toLowerCase() === 'dead' || event.shiftKey) {
          checkOnlyCode = true;
          checkOnlyName = false;
          checkCodeAndName = false;
        }
        if (event.altKey && isMultipleKey) {
          checkCodeorName = true;
        }

        const isKeyName = event.key.toString().toLowerCase() === keyArrayName[activeKeyIndex].toLowerCase();
        const isKeyCode = event.keyCode.toString() === keyArray[activeKeyIndex];

        if (
          (checkOnlyCode &&
            checkOnlyName === false &&
            checkCodeAndName === false &&
            (isKeyCode || codeAndNameDifferent[keyArray[activeKeyIndex]] === event.keyCode)) ||
          (checkCodeAndName && checkOnlyName === false && checkOnlyCode === false && isKeyCode && isKeyName) ||
          (checkOnlyName && checkOnlyCode === false && checkCodeAndName === false && isKeyName) ||
          (checkCodeorName && (isKeyCode || isKeyName)) ||
          nameAndKeyValidated
        ) {
          // console.log('inside true');
          if (activeKeyIndex === keyArray.length - 1) {
            // console.log('last key press from Combined Screen');
            postResult(screenInfo);

            // this statement is to check for last screen and moving to next topic
            if (activeStep === screens.length - 1) {
              // get next topic data and moving to next step
              // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
              const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
              const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;

              // if user in last topic completes the navigating to first topic
              if (topics.length > nextTopicIndex) {
                // clearing screens
                // setScreens([]);
                console.log('can go to next topic from Combined Screen');
                topicClickHandle(nextTopicIndex);
              } else {
                console.log('can not go to next topic from Combined Screen');
                // topicClickHandle(0);
                swal({
                  // title: 'Good job!',
                  text: 'You have reached the end of the this submodule. You can take the skillset IQ or go to next submodule.',
                  buttons: {
                    confirm: {
                      text: 'Take SkillSet IQ',
                      value: true,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                    cancel: {
                      text: 'Cancel',
                      value: null,
                      visible: true,
                      className: '',
                      closeModal: true,
                    },
                  },
                  closeOnClickOutside: false,
                }).then((res) => {
                  if (res) {
                    const keyType = { actionType: actionKeyType };
                    if (userRole === 'USER_DTC') {
                      navigate(`/auth/skilliq-test?subModuleId=${searchParams.get('id')}`, {
                        state: { ...location.state, ...keyType },
                      });
                    } else {
                      navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, {
                        state: { ...location.state, ...keyType },
                      });
                    }
                  }
                });
              }
            } else {
              console.log('can got to next screen from Combined Screen');
              handleNext();
              setActiveKeyIndex(0);
            }
          } else {
            console.log(`Match Key Index ${keyArray[activeKeyIndex]} and key`);
            setActiveKeyIndex(activeKeyIndex + 1);
          }
        } else if (event.shiftKey && event.keyCode === 16) {
          console.log('shift key');
        } else {
          setIsWrongKeyPressed(true);
          toggleClassName();
        }

        // keys[0][keyBoard].keyCode.map((itemKey, index, oriArray) => {
        //   console.log('current key', itemKey);
        //   if (event.keyCode.toString() === itemKey) {
        //     if (activeKeyIndex === oriArray.length - 1) {
        //       console.log('match last key', itemKey);
        //       setActiveKeyIndex(0);
        //       handleNext();
        //     } else {
        //       console.log(`Match Key Index ${index} and key`, itemKey);
        //       setActiveKeyIndex(activeKeyIndex + 1);
        //     }
        //   } else {
        //     console.log(`key not match active index ${activeKeyIndex} and index ${index}`);
        //     setIsWrongKeyPressed(true);
        //     toggleClassName();
        //   }
        //   return false;
        // });
      }
    };

    const toggleClassName = () => {
      setTimeout(() => {
        if (keyType === 'COMBINED_HOT_KEYS') {
          setActiveKeyIndex(0);
        }
        setIsWrongKeyPressed(false);
      }, 500);
    };

    const postResult = (screen) => {
      console.log('post result', screen);
      if (screen.isCompleted) return;

      const payload = {
        screenId: screen.id,
        actionType: screens[activeStep]?.keyType,
        keyboard: keyBoard.toUpperCase(),
        keyObj: screen.keyObj.map((keyItems) => ({
          id: keyItems.id,
          keyCode: keyItems[keyBoard].keyCode,
        })),
      };

      simulationApi.postResult(payload).then((res) => {
        if (res.ok) {
          console.log('post result res : ', res.data);
          // reload topics this is to set is completed check
          updateTopics(res.data);
        }
      });
    };

    const horizontal = screens[activeStep].horizontalAlign.code === 'MIDDLE' ? 'center' : 'flex-start';
    const vertical = screens[activeStep].verticalAlign.code === 'CENTER' ? 'center' : 'start';

    return (
      <>
        <Box
          className={classes.gridContainer}
          sx={{
            justifyContent: screens[activeStep].horizontalAlign.code === 'RIGHT' ? 'flex-end' : horizontal,
            alignItems: screens[activeStep].verticalAlign.code === 'BOTTOM' ? 'end' : vertical,
            backgroundImage:
              keys &&
              `url(${keyType === 'INDIVIDUAL_HOT_KEYS'
                ? keys[activeKeyIndex][keyBoard].backgroundImg
                : keyType === 'COMBINED_HOT_KEYS' && keys[0][keyBoard].backgroundImg
              })`,
          }}
        >
          <Draggable
            // axis="x"
            bounds="parent"
            handle=".handle"
            defaultPosition={{ x: 0, y: 0 }}
            position={null}
            grid={[10, 10]}
            scale={1}
          >
            <div className="handle">
              <Box className={classes.introContainer} sx={{ cursor: 'move' }}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ minHeight: 50 }}>
                    <Typography
                      variant="body1"
                      className={classes.actionDescription}
                      dangerouslySetInnerHTML={{
                        __html: screenInfo ? screenInfo.description : '&nbsp;',
                      }}
                    />
                  </div>
                  <div className={classes.keyName}>
                    {keyType === 'INDIVIDUAL_HOT_KEYS' &&
                      keys &&
                      keys.map((item, index) => (
                        <Typography
                          className={[
                            classes.activeKey,
                            index === activeKeyIndex && classes.buttoneight,
                            isWrongKeyPressed === true && index === activeKeyIndex && classes.keyWrong,
                          ]}
                          style={{
                            marginRight: 10,
                            // backgroundColor: activeKeyIndex === index ? '#ddd' : 'none',
                          }}
                        >
                          {customeKeys.arrowIcons[item[keyBoard].keyCode.toString()]
                            ? customeKeys.arrowIcons[item[keyBoard].keyCode.toString()]
                            : item[keyBoard].keyName}
                        </Typography>
                      ))}

                    {keyType === 'COMBINED_HOT_KEYS' &&
                      keys &&
                      keys[0][keyBoard].keyName.map((item, index, oriArray) => (
                        <>
                          <Typography
                            className={[
                              classes.activeKey,
                              index === activeKeyIndex && classes.buttoneight,
                              isWrongKeyPressed === true && index === activeKeyIndex && classes.keyWrong,
                            ]}
                            style={{
                              marginRight: 10,
                              // backgroundColor: activeKeyIndex === index ? '#ddd' : 'none',
                            }}
                          >
                            {customeKeys.arrowIcons[keys[0][keyboard].keyCode[index].toString()]
                              ? customeKeys.arrowIcons[keys[0][keyboard].keyCode[index].toString()]
                              : item}
                          </Typography>
                          {index < oriArray.length - 1 && (
                            <Typography style={{ fontSize: '1.5rem', color: '#5d5d5d' }}>+</Typography>
                          )}
                        </>
                      ))}

                  </div>
                </div>
                {isAltKeyInclude && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <WarningAmberIcon
                      className={classes.alertAnimationone}
                      sx={{ color: '#ed6c02', fontSize: '24px' }}
                    />
                    &nbsp;
                    <Typography
                      variant="subtitle1"
                      sx={{
                        marginTop: '6px ',
                        lineHeight: '1.5',
                        fontSize: '0.875rem',
                        color: 'rgb(102, 90, 42)',
                        fontWeight: '400',
                      }}
                    >
                      Please use the <b>"Alt"</b> key on the left side of the spacebar.
                    </Typography>
                  </Box>
                )}
              </Box>
            </div>
          </Draggable>
        </Box>
      </>
    );
  };

  const ClickScreen = () => {
    // extracting screen of active step or first step if derectly comes for clickin topics or start sub module in previous page
    const screenInfo = screens.length > 0 ? screens[activeStep < screens.length ? activeStep : 0] : null;

    let range = null;
    let rangeImage = null;

    // needed
    const { keyType } = screenInfo;

    // setting up keyBoard here WINDOWS || mac
    const keyBoard = keyboard.toLowerCase();

    // if screens exist extracting keyObj into keys
    if (screenInfo) {
      // console.log("your click screeninfo is ",screenInfo);
      range = screenInfo.rangeObj[0].range.ranges;
      rangeImage = `${screenInfo.rangeObj[0].range?.backgroundImg}`;
      // console.log("Your keys for range are ",range);
      // console.log("Backgroundbase64 is ",range.backgroundBase64);
    }


    const horizontal = screens[activeStep].horizontalAlign.code === 'MIDDLE' ? 'center' : 'flex-start';
    const vertical = screens[activeStep].verticalAlign.code === 'CENTER' ? 'center' : 'start';

    const boxRef = useRef(null);
    const [backgroundWidth, setBackgroundWidth] = useState(null);
    const [backgroundHeight, setBackgroundHeight] = useState(null);

    const [screenWidth, setScreenWidth] = useState(window.innerWidth);

    useEffect(() => {
      console.log('going inside useeffect.......4');
      const handleResize = () => {
        setScreenWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }, []);

    useEffect(() => {
      if (boxRef.current) {
        setBackgroundWidth(boxRef.current.clientWidth);
        setBackgroundHeight(boxRef.current.clientHeight);
      }
    }, [boxRef, screenWidth]);

    const widthCalculator = {
      '12x12': 36,
      '24x24': 60,
      '36x36': 72,
    };

    const [numColumns, setNumColumns] = useState(0);
    const [numRows, setNumRows] = useState(0);
    const [allCells, setAllCells] = useState({});

    const [shouldRenderGrid, setShouldRenderGrid] = useState(false);
    const [selectedGridSize, setSelectedGridSize] = useState('12x12');
    // const [selectedGridSize, setSelectedGridSize] = useState(range ? range.selectedGridSize : '12x12');

    const [cellDimensions, setCellDimensions] = useState({ width: '100px', height: '100px' });

    const classes = useStyles();
    const [cellHState, setCellHState] = useState(0);

    const [positionGrid, setPositionGrid] = useState({});
    const [targetColarray, setTargetColarray] = useState([]);
    const [targetRowarray, setTargetRowarray] = useState([]);

    useEffect(() => {

      const cellWidth = 960 / widthCalculator[selectedGridSize] - 2;
      const cellHeight = cellWidth;

      const estColumns = backgroundWidth / cellWidth;
      const estRows = estColumns / 1.86956521739;
      const numColumns = Math.ceil(estColumns);
      const numRows = Math.ceil(estRows);

      setCellHState(cellHeight);
      setCellDimensions({ width: `${cellWidth}px`, height: `${cellHeight}px` });
      setNumColumns(numColumns);
      setNumRows(numRows);

      setAllCells({});

      const newAllCells = {};
      for (let i = 0; i < numRows * numColumns; i += 1) {
        const row = Math.floor(i / numColumns);
        const col = i % numColumns;

        if (typeof newAllCells[row] === "undefined") {
          newAllCells[row] = {};
        }

        newAllCells[row][col] = i;
      }

      setAllCells(newAllCells);
      setShouldRenderGrid(true);
    }, [selectedGridSize, backgroundWidth]);

    const [chosenCells, setChosenCells] = useState([]);
    const [targetRow, setTargetRow] = useState(-1);
    const [targetCol, setTargetCol] = useState(-1);
    const [arrow, setArrow] = useState(false);

    useEffect(() => {
      handleCellClick(chosenCells[0]);
    }, [arrow]);

    useEffect(() => {
      if (numRows > 0 && numColumns > 0) {
        let targetRow;
        let targetCol;
        const chosenCells = [];
        if (range.highlightedCellRatios?.length > 0) {
          range.highlightedCellRatios.forEach((cellRatio) => {
            const { rowRatio } = cellRatio;
            const { colRatio } = cellRatio;

            targetRow = Math.round(rowRatio * numRows);
            targetCol = Math.round(colRatio * numColumns);
            setTargetColarray((prev) => {
              const newValue = Math.round(cellRatio.colRatio * numColumns);
              return prev.includes(newValue) ? prev : [...prev, newValue];
            });

            setTargetRowarray((prev) => {
              const newValue = Math.round(cellRatio.rowRatio * numRows);
              return prev.includes(newValue) ? prev : [...prev, newValue];
            });

            chosenCells.push(allCells[targetRow][targetCol]);
          })
        }

        setTargetRow(targetRow);
        setTargetCol(targetCol);
        setChosenCells(chosenCells);


        const nearval = numColumns * numRows / 2

        if (chosenCells) {
          const comparisonResults = chosenCells?.map(value => {
            if (nearval > value) {
              return true;
            }
            if (nearval < value) {
              return false;
            }
            return null;
          });

          const position = comparisonResults.includes(true) ? "above" : "below";

          setPositionGrid(position);
        }

      }
    }, [numRows, numColumns, range]);

    function adjustChosenCells(chosenCells) {
      chosenCells.forEach((cell, i) => {
        if (i > 0) {
          const diff = chosenCells[i] - (chosenCells[i - 1] + 1);
          if (diff > 0) {
            chosenCells[i] -= diff;
          }
        }
      });
      return chosenCells;
    }



    console.log(numRows, "row*", numColumns, "colis")
    const postResult = (screen) => {
      console.log('post result', screen);
      if (screen.isCompleted) return;

      const payload = {
        screenId: screen.id,
        actionType: screens[activeStep]?.keyType,
      };

      simulationApi.postResult(payload).then((res) => {
        if (res.ok) {
          console.log('post result res : ', res.data);
          // reload topics this is to set is completed check
          updateTopics(res.data);
        }
      });
    };


    useMemo(() => {
      if (targetColarray?.length > 0 && targetRowarray?.length > 0) {
        const tarrowarray = adjustChosenCells(targetRowarray);
        const tarcolarray = adjustChosenCells(targetColarray);

        const chosenCells = tarrowarray && tarrowarray?.length > 0 && tarrowarray.flatMap(row =>
          tarcolarray.map(col => allCells[row][col])
        );

        setChosenCells(chosenCells);
        const nearval = numColumns * numRows / 2
        if (chosenCells) {
          const comparisonResults = chosenCells?.map(value => {
            if (nearval > value) {
              return true;
            }
            if (nearval < value) {
              return false;
            }
            return null;
          });

          const position = comparisonResults.includes(true) ? "above" : "below";

          setPositionGrid(position);
        }
      }
    }, [targetColarray, targetRowarray])

    const handleCellClick = (index) => {
      if (chosenCells.includes(index)) {
        postResult(screenInfo);
        if (activeStep === screens.length - 1) {

          // get next topic data and moving to next step
          const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
          const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
          // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;

          // if user in last topic completes the navigating to first topic
          if (topics.length > nextTopicIndex) {
            // clearing screens
            // setScreens([]);
            console.log('can go to next topic from Combined Screen');
            topicClickHandle(nextTopicIndex);
          } else {
            console.log('can not go to next topic from Combined Screen');
            // topicClickHandle(0);
            swal({
              // title: 'Good job!',
              text: 'You have reached the end of the this submodule. You can take the skillset IQ or go to next submodule.',
              buttons: {
                confirm: {
                  text: 'Take SkillSet IQ',
                  value: true,
                  visible: true,
                  className: '',
                  closeModal: true,
                },
                cancel: {
                  text: 'Cancel',
                  value: null,
                  visible: true,
                  className: '',
                  closeModal: true,
                },
              },
              closeOnClickOutside: false,
            }).then((res) => {
              if (res) {
                const keyType = { actionType: actionKeyType };
                if (userRole === 'USER_DTC') {
                  navigate(`/auth/skilliq-test?subModuleId=${searchParams.get('id')}`, {
                    state: { ...location.state, ...keyType },
                  });
                } else {
                  navigate(`/app/skilliq-test?subModuleId=${searchParams.get('id')}`, {
                    state: { ...location.state, ...keyType },
                  });
                }
              }
            });
          }
        } else {
          console.log('can got to next screen from Combined Screen');
          handleNext();
          setActiveKeyIndex(0);
        }
      }
    }

    const getSingleCellPx = (cellNumber) => {

      console.log("cellNumber is ", cellNumber);
      // Assuming you have a reference to the cell element

      const cellElement = document.getElementById(`cell-${cellNumber}`);
      const cellZero = document.getElementById(`cell-0`);

      // if (cellElement === null) {
      //   return {};
      // }
      // Get the position and dimensions of the cell
      const cellRect = cellElement.getBoundingClientRect();
      const cellZeroRect = cellZero.getBoundingClientRect();

      // Extract the pixel coordinates
      const cellX = cellRect.left - cellZeroRect.left;
      const cellY = cellRect.top - cellZeroRect.top + 0.5 * cellHState;
      // const cellXplusWidth = cellX+cellRect.width;
      // const cellYplusHeight = cellY+cellRect.height;

      // return {"index":cellNumber,"A":{"X":cellX,"Y":cellY},"B":{"X":cellXplusWidth,"Y":cellY},"C":{"X":cellX,"Y":cellYplusHeight},"D":{"X":cellXplusWidth,"Y":cellYplusHeight}}
      return { cellX, cellY };
    }

    const [startingX, setStartingX] = useState(0);
    const [startingY, setStartingY] = useState(0);
    const gridRef = useRef(null);

    useEffect(() => {
      if (gridRef.current && targetRow > -1 && targetCol > -1) {
        console.log(`targetRow is ${targetRow} and targetCol is ${targetCol}`);
        console.log("allcells target is ", allCells);
        const startingCoordinates = getSingleCellPx(allCells[targetRow][targetCol]);
        setStartingX(startingCoordinates.cellX);
        setStartingY(startingCoordinates.cellY);
      }
    }, [targetRow, targetCol]);
    console.log(rangeImage, "rangeImage");


    return (
      <>
        <Box
          ref={boxRef}
          className={classes.clickContainer}
          sx={{
            justifyContent: screens[activeStep].horizontalAlign.code === 'RIGHT' ? 'flex-end' : horizontal,
            alignItems: screens[activeStep].verticalAlign.code === 'BOTTOM' ? 'end' : vertical,
            backgroundImage: rangeImage && `url(${rangeImage})`,
          }}
        >
          <div className={classes.body}>
            <div className={classes.container}>
              {shouldRenderGrid && (
                <div
                  className={classes.content}
                  style={{
                    gridTemplateColumns: `repeat(${numColumns}, 1fr)`,
                  }}
                  ref={(ref) => {
                    gridRef.current = ref;  // Set the gridRef when the div ref changes
                  }}
                >
                  {[...Array(numRows * numColumns)].map((_, index) => (
                    <div
                      key={`cell-${index}`}
                      id={`cell-${index}`}
                      className={`${classes.gridCell} ${index >= (numRows - 1) * numColumns ? classes.gutter : ''}`}
                      style={{
                        ...cellDimensions,
                        backgroundColor: chosenCells.includes(index) ? 'rgba(255, 255, 0, 0.5)' : 'transparent',
                        // opacity:'0',
                        zIndex: '2'
                      }}
                      onClick={() => { handleCellClick(index) }}
                      role="button"
                      onKeyDown={() => { }}
                      tabIndex={0}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

        </Box>
      </>
    );
  };

  const CodeScreen = () => {
    var temp = '';
    useEffect(() => {

      window.addEventListener('keydown', handleKeyDown);

      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };

    }, []);

    const handleKeyDown = (event) => {
      const focusedElement = document.activeElement;
      const isCodeEditor = focusedElement && focusedElement.tagName.toLowerCase() === 'textarea' && focusedElement.classList.contains('ace_text-input');
      if (event.key === 'Enter' || event.key === " ") {

        if ((screens[activeStep].type === 'ACTION') && !isCodeEditor) {
          event.preventDefault(); // Prevent the default behavior of the Enter key
        }

      }
    };

    // extracting screen of active step or first step if derectly comes for clickin topics or start sub module in previous page
    const screenInfo = screens.length > 0 ? screens[activeStep < screens.length ? activeStep : 0] : null;

    const [codeValue, setCodeValue] = useState(``);
    const [prependCode, setPrependCode] = useState(screenInfo.prependCode);
    const [appendCode, setAppendCode] = useState(screenInfo.appendCode);

    const [output, setOutput] = useState(null);
    const [isStarted, setIsStarted] = useState(false);

    const [errorRow, setErrorRow] = useState([]);
    const [isCompleteCode, setIsCompleteCode] = useState(null);
    const [isExecuting, setIsExecuting] = useState(false);

    const [updateResult, setUpdateResult] = useState(null);
    const [completedScreens, setCompletedScreens] = useState(screens);
    const [isEnableKeyIntelligence, setIsEnableKeyIntelligence] = useState(
      useSelector((state) => state.keyIntelligence)
    );
    const [isPaste, setIsPaste] = useState(false);

    const diffArray1 = (a1, a2) => {
      const aDiffs = [];
      a1.filter((i) => {
        if (a2.indexOf(i) < 0) {
          aDiffs.push(a1.indexOf(i));
        }
        return false;
      });
      return aDiffs;
    };

    const postCodeResult = async (screen) => {
      var errorLines;

      setIsExecuting(true);
      // setIsStarted(false);

      setOutput(null);
      const payload = {
        screenId: screen.id,
        actionType: screens[activeStep]?.keyType,
        inputCode: codeValue,
        subCategory: location?.state?.subCategory?.code,
      };
      setErrorRow([]);
      if (location?.state?.subCategory?.code === 'SEQUEL') {
        simulationApi.postResult(payload).then((response) => {
          if (response.ok) {
            // postRes = response.data?.result;
            setIsStarted(true);
            setOutput(response.data.result);
            setUpdateResult(response.data);
            console.log(response);
            document.getElementById('simulationSec').scrollTop += 280;
            setIsExecuting(false);

            setCompletedScreens(
              completedScreens.map((item) => {
                if (item.id === screens[activeStep].id) {
                  item.isCompleted = true;
                  return item;
                }
                return item;
              })
            );
          } else if (response.status === 400) {
            setIsStarted(false);
            setIsExecuting(false);
            if (response?.data?.errorLineNum) {
              const errArr = [];
              response?.data?.errorLineNum.forEach((item, indx) => {
                errArr.push({
                  row: item - 1,
                  column: item - 1,
                  type: 'error',
                  text: location?.state?.subCategory?.code === 'SEQUEL' ? 'Query error' : 'Syntax error',
                });
                setErrorRow(errArr);
              });
            }
            if (response?.data?.response) {
              setOutput(screenInfo.result);
              document.getElementById('simulationSec').scrollTop += 280;
              setIsStarted(true);
              return;
            }

            if (((response?.data?.errorLineNum)) && typeof response?.data?.errorLineNum[0] === 'string') {
              document.getElementById('simulationSec').scrollTop += 250;
              setIsCompleteCode(response?.data?.errorLineNum[0]);
            }
          }
        });
      }
      else {
        console.log('code', codeValue)
        const code = checkUserInput(codeValue)
        const payload = {
          // eslint-disable-next-line prefer-template
          code: screenInfo.prependCode + "\n" + code,
          screenId: screen.id,
          actionType: 'ACTION',
          subCategory: location?.state?.subCategory?.code,
        }

        simulationApi.pythonPostResult(payload).then((response) => {
          if (response.ok) {
            setIsStarted(true);
            const out = JSON.parse(JSON.stringify(response.data.result))
            setOutput(out.textResult)
            setUpdateResult(response.data);
            setIsExecuting(false);
            // setIsStarted(true);
            // setUpdateResult(response.Payload);
          }
        })
      }

    };

    const handleCodeExecution = (data) => {
      if (updateResult) {
        updateTopics(updateResult);
      }
      if (activeStep === screens.length - 1) {
        console.log('screens end');
        // post result
        // postCodeResult(data);
        // clearing screens
        // setScreens([]);
        // resetting active screen to zero
        // setActiveStep(0);
        // get next topic data and moving to next step
        // const nextTopicIndex = topics.findIndex((item) => item.id === activeTopic.id) + 1;
        const filteredIds = topics.filter(item => item?.type?.toLowerCase() !== "reference" && item?.id !== "skillset")
        const nextTopicIndex = filteredIds.findIndex((item) => item.id === activeTopic.id) + 1;
        // const copyTopics = topics.map((topic, index) => ({ ...topic, selected: index === nextTopicIndex }));
        // console.log('selected : ', copyTopics);
        // setTopics(copyTopics);

        // if user in last topic completes the navigating to first topic
        if (topics.length > nextTopicIndex) {
          console.log('can go to next topic');
          topicClickHandle(nextTopicIndex);
        } else {
          console.log('can not go to next topic');
          // topicClickHandle(0);
          swal({
            // title: 'Good job!',
            text: 'You have reached the end of the this submodule.',
            buttons: {
              confirm: {
                text: 'Close',
                value: true,
                visible: true,
                className: '',
                closeModal: true,
              },
              //   cancel: {
              //     text: 'Cancel',
              //     value: null,
              //     visible: true,
              //     className: '',
              //     closeModal: true,
              //   },
            },
            closeOnClickOutside: false,
          }).then((res) => {
            if (res) {
              console.log(res);
            }
          });
        }

        // resetting active key index if has multiple keys
        setActiveKeyIndex(0);
      } else {
        setActiveKeyIndex(0);
        // postCodeResult(data);
        handleNext();
      }
    };

    const onLoad = (editor) => {
      // Your editor options comes here
      editor.on('change', (arg, activeEditor) => {
        const aceEditor = activeEditor;
        const newHeight =
          aceEditor.getSession().getScreenLength() *
          (aceEditor.renderer.lineHeight + aceEditor.renderer.scrollBar.getWidth());
        aceEditor.container.style.height = newHeight < 90 ? `90px` : `${newHeight}px`;
        aceEditor.resize();
      });
    };

    const LightTooltip = styled(({ className, ...props }) => <Tooltip {...props} classes={{ popper: className }} />)(
      ({ theme }) => ({
        [`& .${tooltipClasses.tooltip}`]: {
          backgroundColor: '#4b4d4d',
          // color: 'rgba(0, 0, 0, 0.87)',
          boxShadow: theme.shadows[1],
          fontSize: 11,
          zIndex: 999,
          color: '#99bebe',
          border: '2px solid #636569',
        },
      })
    );

    const handleSwitchIntelligence = () => {
      swal({
        text: `${t('The editor is reset after switching the intallisense, please make sure before confirming it')}`,
        // icon: 'warning',
        buttons: {
          confirm: {
            text: `${t('Okay')}`,
            value: true,
            visible: true,
            className: '',
            closeModal: true,
          },
          cancel: {
            text: `${t('Cancel')}`,
            value: null,
            visible: true,
            className: '',
            closeModal: true,
          },
        },
        closeOnClickOutside: false,
      }).then((res) => {
        if (res) {
          setIsEnableKeyIntelligence(!isEnableKeyIntelligence);
          dispatch(setKeyIntelligence(!isEnableKeyIntelligence));
        }
      });
    };

    return (
      <>
        <Box>
          <Box className={classes.codeContainer}>
            <div style={{ minHeight: 50, paddingBottom: '1.5rem' }}>
              <Typography
                variant="body1"
                className={classes.actionDescription}
                dangerouslySetInnerHTML={{
                  __html: screenInfo ? screenInfo.description : '&nbsp;',
                }}
              />

              <Typography gutterBottom mt={2} variant="subtitle1">
                {t("Sample code")}
              </Typography>

              <Box
                sx={{
                  background: location?.state?.subCategory?.code === 'SEQUEL' ? '#E7E9EB' : '#272822',
                  padding: '22px !important',
                  borderRadius: '8px',
                  marginTop: '12px',
                }}
              // className={classes.codeExample}
              >
                <Box
                  sx={{
                    background: '#fff',
                    borderRadius: '4px',
                    padding: location?.state?.subCategory?.code === 'SEQUEL' ? '6px !important' : '0px',
                  }}
                >
                  <AceEditor
                    mode={location?.state?.subCategory?.code === 'SEQUEL' ? 'mysql' : 'python'}
                    theme={location?.state?.subCategory?.code === 'SEQUEL' ? 'textmate' : 'monokai'}
                    fontSize={15}
                    value={screenInfo.inputCode}
                    readOnly
                    height={screenInfo.inputCode?.split(/\r\n|\r|\n/).length * 22}
                    style={{
                      width: '100%',
                      // marginLeft: '178px',
                      // height: getInitialHeight,
                    }}
                    showGutter={false}
                    highlightActiveLine={false}
                    name="PREPEND"
                    editorProps={{ $blockScrolling: true }}
                    setOptions={{
                      enableBasicAutocompletion: true,
                      enableLiveAutocompletion: true,
                      // enableSnippets: true,
                      // firstLineNumber: 6,
                    }}
                  />
                </Box>
              </Box>
            </div>

            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            // className={classes.codeExample}
            >
              <Typography gutterBottom mt={2} variant="subtitle1">
                {t("Try it now")}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography>{t("IntelliSense")}</Typography>
                <Switch
                  checked={isEnableKeyIntelligence}
                  onChange={handleSwitchIntelligence}
                  color="primary"
                  inputProps={{ 'aria-label': 'controlled' }}
                />
                {location?.state?.subCategory?.code === 'SEQUEL' && (
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={codeValue.trim().length === 0}
                    onClick={async () => {
                      if (codeValue.trim().length > 0) {
                        await postCodeResult(screenInfo);
                        document.getElementById('simulationSec').scrollTop += 180;
                      } else {
                        setSnackbarTitle('Enter input code');
                        setOpenSnackbar(true);
                      }
                    }}
                  >
                    {t("RUN SQL")}
                  </Button>
                )}
              </Box>
            </Box>

            <Box
              sx={{
                width: '100%',
                overflowX: 'auto',
                overflowY: 'hidden',
              }}
            >
              <div
                style={{
                  width: '1020px',
                  position: 'relative',
                  paddingBottom: '1rem',
                }}
              >
                {location?.state?.subCategory?.code === 'PYTHON' && (
                  <img
                    src="data:image/png;base64,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
                  x4gVGRQQHjbS/uojjsN4hLuDZauLhkFHJUIvPC9CtFUYOw4NjoaPS6u/zWrVuO4+wNb4miODk5efbMmXuyCBmaZhjmkA4nzsRcWl5+OufOcdzo6Gh0RpJ0sBkqDcO4fuNGp9M56sBoxDTNeqNxd/YA27aru9V7JhNoNOu6Pjl4H4Zhu91+8nlRoyvw1Pz82NjYQb+4vLJiWdaRBEYfNmj6UVHbMnuWZ8jlIDuOORELyfjPD75HdSpUbxf57qMzwFlZYp2m+tFPL+uILL984uSJ10+9Wmpd++l7N7faAVuamn/95ZfmS1u62bV0kkSiFNSuf9ZUzZbDMkL+xNvfemWq7Hp2TW09+AaaS2O71rn40480lD399ktTueHpkeU1pYvQPbm1XLKY9pxU5ZOffaabrpCfOnnq9MSrr+q776/UFKWrSmExmaWR4yOHEHgxWc6Tjc8qbUV39pfkHvieY6HoDp7o/0XE7j9BD1FRAAAAAAAAAADgEWQ5MT9/spDP0/04mGWZRxEYJQgim83Ozc2NjY0l5QRFM5ZpNZpNkUAnWTpNkeztf/sTFEGM0ESGvF2k/aC12rsWshWUH0Foz5PSNEJDmYyQTEJ3A/DMCMPQcexet+2TDKZ5TNGYpG7/uSN6zwrxLBnEl90i9I7R2F7UkB0is1prVes7tl1dWepYpoMczegubbRcVhBpmkfINzXl1qfLbasThxM93+/dWqkZiOZZVnzI5u3mdm17c0WPk9VbO7tNTXcEIfGgOxdWK82txQ9WdMvDCJmdRm1lo6OV5ibSbNZSTN3pyflSiuSi2xUn80mxKNZbjeBOYve+4P7EKDByAAAAAAAAAACA44MgCJZlT544MTc7O1QsJhIJIX6UniIJgkIoScY/92IJRN9JkTpoxmi72TGr6tx/+i++7/zuO5+sN1UkiukTb//2f3TO+dnires3XphG931/kPa4tr5Wr39efKnb7bbbX3iYXdf1paWldqtF9KPRHMemM5m5mVm3P6fk4Y0ALhoA6VSKou/p7vhhaMe2V1ZXO51OGB7CHKO2ba+trSm9Hn3AjFff89qdzpHOJXFXdBmMDA8PJhhF/bzdifHJbre3N2l0eHgkeSecT5Lk0NBQJ1rjyXI2a7Wa53nLKysH/aKiKPeMnCNlmka7025qps0mbSERZ0JaKnLMaIRgTkQUwzPBRKFb4ur1jtBQJdt78L0iGlqhbgS4P5lDGMdavcCwlOj6wP3JicMgwIMa88RgMAa+MPnq/Hh+LM3GUVc+mUdb3YfPVmubhmnon28fMxQjppKoZgwm9thzJJblGbrfn0I02lxo2a6mq9RQfGqoZ5p6Wx0dKaVW9R4vyWlJkBvVWnSs+E7m+5eJRrsgSgzHGZoafYdm4lL1g2RtBBOMAgAAAAAAAAAAD9JTlMtXrpw/d57nueru7lE87swwzMjIyPDIcDKZHEyoiPpT4UWvgCSbQcgT0dI9oQaM3Tv/kD9YYJQgXHVn5dr7f/Nnb5597VeGX3JdC7Eclz49173505sfX6+qR1Z56bhxXVdVFNSvmVOv15vN5mB59GsYholEIp1OR79qmmbbdvRpr9cbdEwqlRLFuNqPYRh7a9k/iWhrYRiYpjkyPFwoFvYWPgr8wDCN5aXlwZybh7K7aFPtdltVVZI8WMZx1DJe31H3TjaTGRsfPzE3d3eygqjNp6amou6oN+qO47Asm0lnpqem704GGp1LtEIQBKtx/aXHD1AOevyxiy89haHbLzQV9jrthun35LFAysaJoiQdNQFGBKKZQZYox/hT+c7L5e1mT7i0MXRrJ/+wPv3iBAUhjv9q8MBQIc8nsqPzw8VUknW0ZtVxo52maFkOHz1m+tu/e2OJ/94Tte79d5ogXjPc+03sB3cet/cMy2z33FPFfGIriHaZECmttmtGm97fLSvqUD5Oe2V817P7WaYsz7P9WkwIxdOMQvElAAAAAAAAAADgfrZtb29v4zCkGUZRlFardei7YFl2ZHgkKSfvpsehOFpKy0mZS6UXdFUgiTxBcgQRYuwitO4FrTsBhAOHb5zWxsoH6kr3V//Lb0/NTvenmHR16/IP/+rDhWtb5ovTr5ZpRn0ZBEEqmYoLwRvG3fAQz/P5fP7M6dPR+5XV1aj7o08HaZIkSaZSqVwuF/VTr9dVVfWwjsfzvO1KRRCinfPysBz9jHbhum63293dra2vr2uadlixm2g7Tt/9H0VNMagoZcWluv2vqndkWR4ZHh4fH7+7hOe5crkUHXm+lo/6juP50tBQuVweTBRL9EW/Rt3UareeJDD6dCK/T8h1HUXpqWzBK5/CDPeANQJfII3pXPPUSGu6QKkW97DA6AGGDSmKmdHTF06wy9cWVzeXqz2bFZnp5Ojs4dRGoygKU3vSpRma4jkReV0PR5emYxi9djeYHEolw3RakFhnZ7vnx3v+8sAoFUdFJYZlA9+3TMPz4vh12A8w84IoiNFtkOjHRkMEAAAAAAAAAACAvQGGIIgLYq+tEYgIwuBQHmW+B8Mw5XJ5EAq7u5AkyVw2V5iYqCwvJXxfDbFMEAHCeoivOl7Vvx2LoB9jf4TVQRf/zf928TnpofiJX4JiGDLOrgwx7r8PPS/ARBhn0D24wyzbbrZatm3ncvlCvrBb3VW121FOmqYzmcz8/Hz0XlHVRqNhGMbgozhmmssPl4dd1+l2uk9e6mcvx3Vv3lzQND3q+5GRkehnT1EWl5YuXbr0dGKUUTvKicTExET0fnNzM9r7V5VGpxu6pmvRxUbET3MPnq0mogYZHR2JXl/o/b7BCnEar2kebqccxwGPsanrbogChr8dFcWYRD6F/KgNButQvpEmaqPZNk2ErBAkhMNIZWUZSqITqHnj8lqlrVkESVF0Jikx9GNNrUBE3yYJhEPfH1yiHM+zokgR3QDHJ0TKslBMDfnKqhHEmbiu5bW7NW4+OYTkdIjD7nod7ScuGg0bLtq0KLqObRm3o6KoX4vJtsLoti4mEmEcJQ2hEBMAAAAAAAAAAHC/uF58MkVRlBX/w9o49NhotP10OnnPw7sEQWQy6YnpacOybm5X1jxXQCgMwm4QtIPQerxH6Z9PbJrOzn/vOyPm9U8X161e+sS//52R3b/6y5sNoerQhP7gUllRL+q6dv3GjVPz83Ozs2EYfPTxx/cUPb8HRZFTk1Nnz5xBCC8tL7fa7Uev/xiio9rZ2bEs6/y5c9GA2NzaWltbe2qZm/l8/sTc3PnzL0XvJUmKzvHuDANPWa+nVKu7jbFmsVjodDpROwuikJQfUB3MNM3omsQYFwrxmtVqtdPpPvdDPv4TAEHGj88PuNa09zfz1MW8oPR/x3LCLEndiYxCU/HdiiQOK8BN9dN5SU6PdpLmsrOvn0wng179oJshSCzPnDs/nEPd7c+urOj9ZamJLBdeqO9criHbzw+XRk9NiOqtj3ccpx/qNjyvtb0rzZapQrjbrLa376tr/0DRrTLwfdPQXcfxvS+EPsMgsEwD96dWxSGkiwIAAAAAAAAAAPeSZXlqcvLll1/muHiO0RvXr29sbh7i9gfPLjMMc3/5kmhhIV946aWXW8PDhqqqnW6z2Wgqir8nj+/AgVGMM5nSuV/+x780gZCor63fuPyjv1xDF379u0OV3fVbP/qs+gz2EYloXkomEMcwlEcxQjIt91iKohhEPKp9bNtZXFzMpNPlUnl6ajouubO88sB8w6hveI7vV8eaFSWx1WotLy8rinIUJ+P7fqfTuXbtGkGSuq4/8IH3o5DP5+dPnpyZmRk8Sj83N0eSZDTSjmLyiC8VBEGj0bz42cVUKtXtdP3ASyaToyOjExMT0XU4SA4Nw3Bzc7OyXen2I6HR8Wu6UavtHnq0+hiiGJoMAxS4qF96aMi+eSH9yYXsZZ6OA38EwgwZ8IzH0HGwz/Eoz6eefKeEp5rd2vWN8sz8a5nZwPT9IPArm9107uCB++hmRzCcIIrIYO/Mc+sobRsH/PzbP38iuhUKCd5H7fUPbnUUO4gzhxH2XKu6tjN5PqUZen234+9vdtE4FdTzossqDB8wMKKFjm3hfmEo+H87AAAAAAAAAADgHpIoTsUlXtIMQw8FQWd4+NADo6IgEAT5oOABwbBMJpvheM62rHaqbSNcV9W9zzcfLDCKsZgdO3H+rTfmac1Es+MnmRTZfu/Ha5rLDJ07LTJ26eJO7UjrL4Uhsg3crCCa3e9XDAWRj4xZBHaoVW5dNtxqt2d6NrF59WO12zR00yO8R8WDfN9vtVqLS0txQfPi0NkzZ0VRqtVrvu8NgoMRnuNykWwun8/PzExLotTpdpeWlur1+hOGLKOdJpPJVCo1mCXzgWRZfuByRVFUVT2s6vAURaWSyfmT87Ozs5lMdtD/2WyuH39EC+HNnqI8/Wijbuhra2sMw0SnGWeMCkKv24t6gu4LwzBavri4WKlUojWji6VWr0cdevynB31ycZheEFmKoB3dN3tYTGWDyoRYmcy277ve48DpbjdRV8QHX1vNzW3NI7W7eZSW0a1vLSqOETqDa86zvPbWwqrT1B0vsPze7upNiRmKdk8g33TM7nq9Q+Rcvau7cWwxULcXN3HD0Px4igtkdSornt8y7s5d7PbaO5uEEvQs7KNObQPrSO84qF+IHvlGR7Hq22G+yCGadDpKr9fcWW99fpUFQajpJhWQmqLV2tZ+q9H3o5+Pun+8AJF0AAAAAAAAAADgMaMQJMmyDEnGxV3iJET6kB9e5zgulU6R/R3c/ykZ752laVqS4tohhmmurq+jPblNBz2a/Mjc9IVvSCv/4z/7M+77v/KrozM0Chy79dEHi9/5TjZVnEKodmRNSVJUdCpC6KPmJtp/PXTH4hISTTEP7aHACJWVi+/e+V1XPvjxnY++bNsY49XVVdQPkg4PD58/f360M6KqKs3cbthsLsdyXFKWC4WC4zhxrujKysLCwpM/3h4NplKpdOLE3N266vu3tLQcHfZhBUajITjcFx2Sqip7jzBaqGmqZdum+bQLc+EwdPsGv1qWtVOtapom94uUBUFgmsZurabp+mD1u/PAvgh4QZBl2TRs1Fp1E0Xe71I4DiCGuJ8fGlAhJjyf7Bm8G1A3Kvm1evqB21F3FveWDyMIXWlGr8+vHsLVnfryJ/XbH8clkLauXNz6wkYuN+58Ggadtc86d35DyKyvXKvvuRAJp7W71tq9/b3a5q3aFy9Tz1Rqq8vLqw++VAmK5sRCmrNb0RXatfcfFwUAAAAAAAAAAMBjsixra2uL7Wu1WvV6/RA3ThBEIiEVCkXykXFCsi+RSORzOYHn9f6cioOPDhoYTQsIi+7ix1tI+7ziN4VQKZPmk0cc+xIEkeqHmQ96zDwvSgn5iI4qCILl5eWoa0+fPn1qfr5QKJRKpbufjo2Oov7Un57nra6tLS4u7uzsHEpJoqhHBUHIZnO5bPag361Wd+lDitBTFMVx8TPNlcoWuj82j7EoitEKjuMch8Q6x3YHhxFnjFrOi/n4c/w3GkQUS8N0q1mv3lAX36dGWsSoFQ/mkKz1Ei1NtD26qQrv3BjXLC7Ez0cEkeW49PxsiWivq7puEhAWBQAAAAAAAAAAjlyv17v42aVur8eybLvdqdUOM6OSYZl0JlMul4l9JFAyDJNKpYrForuzc/cx7oNGx5Za1tSu/6v/5D/84H9a5AeL+KT8+n/wD94gbi5VNq8cWTtaliGIIs2wqfSB44AEQQa+b1nmox+JfWwYY0VRLl+6tLy8XCwUcrlcOp3muLjet+u6mq61Wq1Go6lpmm3bh1WoPQxD0zTbrZbnHbgWdnQkh/XMeBAEqqreuH4dPTzSdEyior7nvff+e+Iliaao6Hgsy3ruC9DfHaDR+RqGvnfshRizHJ/O5tVe1/WjBonvIDQZDmc0xeQshyYRfm4aID15fmbmxMm0x1lX31/ZrTRtBAAAAAAAAAAAgKfC9/3NzU2CIKI3GB9mjlohXyiXyvEco/tYOToAQRRn5+Y63e5jBkYJwm6uX734Ti7/+u/82unSmRk+RZ74h//56UShcu2jTz74dPPo8rBajbquaRT1mEVgwjD0PTf674gOLwgC3TBMyzIMo9Fo8IIwSMkMfN9xneiTaPnhBgejrdXrddu2HzHH6MMoinKIT47Hj6Vb1vG/DjFCqqZF3XS3+NILcgMKMXZsu16t0PS92dZef6qBtiau1jPZhJVN2BwTyLyrmFzX4C2XDp+R6KiydWOZskLzwRFPR2nWN0O/5iG9UW0btocRAAAAAAAAAAAAnhJ8RLXBKYr0Pa/ZbO5zfT8I0BcTFg/8PLVeX1/4wApT/9k3x7e3lvuLWKJz7Uc/fv/GwrZ6dE2oKb3odcz7OQxDve/p7KvXB5fXQdvtxbv/4Og2oXTaD/tcMbnrWwXTYfKyyTGB41F1RdpqJV2fflZOMbovPeKqs7q729ELRj8AAAAAAAAAAPAcMU1zt7arqncCkgRBkSTDfJ4WhjH2/SAIb8dD+7/6dwvSoMcIjEbsXu3K//3fX4HmB+B5UWknoxe0AwAAAAAAAAAAAJ4ViqqapnW38lL0RuzX47lbod7zPEVRLNvakyeHbfvz555paEQAAAAAAAAAAAAAAMCzxXO9vdNmUhSVyaTPnTtHUrdDpb1e79KlS5ZlPazWzpcHRjEul2e/9r3feQv9+H9/5zMGzbz+nd96c/ahqxsIrf/pv/qjz3Y6baj7DJ6Y47iu50E7AAAAAAAAAAAAAIBHCMPQcRzTNIaHR2iGtm2r1Wo+uurPfjJGo022dpZWUNsxvQAp9a3FxYdtD9MsSo299rdeNd+9fGW9qUNsFDyZIAxRCM1wB0HhwkthIgMt8azyPkFeHZoBAAAAAAAAAAA4dBhjVdVuLizU6nWaol3X6XQ7lmU9ot4Mce6llw/zCLgyKv7Kv/zv5Bt/8Oc//fG1rX5glIgW80L0gh4C4LEFvu/6Yfbtf8pkRqE1nlHmxd/zGwvQDgAAAAAAAAAAwD65toVxyAnSUWz8wHOMYoxIimYFniYQSdxZFLiW4/shRraNtjZ26AsEzUAlFwAAAAAAAAAAAAAAwPH0GMWXEunhr/3aP/tv3y6gAtdf4Cjo5h/88//z/b9ZbCGkIPTev/1vPg5104HWBQAAAAAAAAAAAAAAHEsHDoxmp09/7Vvfe739x3/9brututESiuFz06986xteWrr0o8+qCFlqs1/2/osTjIZB4LkQLAXgMeE4W5uBdnim+dFN0DKhHQAAAAAAAAAAgH0Kw4AkySPa+MECoxjnixNT516TW//PH/30Sm9dicuFk1w689LYP/27J8743UsXd2oPL7hEQC0mAB4fDkPf2vrEbS69OOcs8EJ03zCfl2Ciq9QC34OhDAAAAAAAAADg0EX/fBYEwXGcRxRhf1Ydk8AoQsNJWS5k1n/2TkNFt3NCA9tpfXRp4zffSOWLUwjVHnYKFMVSUH8JgMcU+H5gGebiT16os2ZzOZIktWYTBgAAAAAAAAAAAPAI0T+fZVkOw/A5DIweXaNBEwAAAAAAAAAAAAAAAF40B80YXW135mqdb/32f/Hmxh/dbFfjtFE+mTj9a7/xKrG6vlO5AS0KwJHBGPu+H082+sJotVoEQbiuC70PAAAAAAAAAAA8QvTP53q97vt+GIaHu2Wapu+Z5fNuaOIQYxR3Z+B8mlNx0gc8RKOx8tn7P0yxr37vu3//7V8O/XgTFFnON2599MkHn6yrMIsoAEcJ97045wshUQAAAAAAAAAAYD8wxo7jHNGW9/4ahmG05NDDrwNk35fGRjGOVshOnJ0cGc4kH7qWpXd21z5dbSLkP2SDB65Kr9fXb7xvNMn/+DdfSY9l2HiRqwcrP/nrD69e2+rBKAQAAAAAAAAAAAAA4LkU3nFEaVuDze4rNooYVkjIqVT63hVJjMXcSC4p6Mp62Px0tYOQ/5CNHLQqPcOJ0X925wf/w//8AxQe1/xQgiBJ6r4WxDiIuu1o4tkAAAAAAAAAAAAAADyvBiHLoy7uFO0l2n70k6KoR8RGCQITqLbyafT64tcJimQSEjf52t8+O5V3jVbvEVFRdPCM0RNnv/nqd/9e8tN/9L9+jFD3uHZVQpZT2RzLcZ+3S9Rtvt/ttC3D8H0PRjMAAAAAAAAAAAAAAAcSHn3G4RPNIigVEid+7rfeGGdqf3Plp0sfrShenFv6UAcNjOpeYJl2kTne9ezFhFwaHpUSic/btN+sZdtutxqN3ara68JQBgAAAAAAAAAAAADg+ZCZPD9xYn6uSHau/GSlsr3Z1lw/RI983v2ggdH2bqV67croz/3mG1t/ddNoqPaxfJqeYRhBlBLJ1D3LcRLzgkDTDEEQaq/7QhWxAQAAAAAAAAAAAADgsT3NSFq0r32Wp8eYRig99dL4zNRoirZ7S6uby0vbqmsGX/71gwZGsWNSXlB86x/9aifI5Nab2ue7MJR6q77VPM6F6aMGTSRTRDwFKcEwbIgfmv0beL5lGq7rPK3O5uWsLMsCE/p2b7dnhW5AHMFeSJoT5FwuwSDqi5v3TEVXe6rz+DvFtJRKJWSJIQPXaLc0J/DwUx8JBEmxgsCQVD+fGceJwoFjOJ4fhATRv3gpTmQpHPiOs/fwBhP7sgJHoQB7jrOn8TEiEM1LbBh4nusG+MiGd9Q7DBuNSjK0TDfs73cPkmYJmkWmDndhAAAAAAAAAAAAgAFMc6xYGEpNnj09kiHb9aWVjz9Z19B+CyMdNDA6OXf69e//w7ckhH7zn7z1xY9ufvQHP/j9/+WHnxz8HCiKIknS9/2HBZ7JvkHRqydvMklOTkWvuflHrKN0uyu3btR2KoffYXF4lmIYikTxdLW+H8faMBqeffXC61+fyVvK+l//H++uODvaUYwWLpGbeeXvfO9rGZSgvzA+2ksfXvv0/Q83PC9u4MeJ/eHE7Nm3Xn7tXI7Tqtd++INPtvWW+wRNRFIsQxE4bqHAD780HBkNHIIkaF4Si5PTGYZnBusHGBnVW5W2Zpnxr1E7S/nRISHU9e1q/QtB7+gjNlUaTxGa09rZ3tv4FINTo1MFW2t1ak3dPborGfGJbK44xDsrSzUbOV8c6XyqQHKC3mnALQ8AAAAAAAAAAAAgQlA0nRkrzr/2GxeGjMWfvHd59UbVQgfJaTtoYHT11oe/9y9/+0/EB3xkGx2lc/BzEEVxbHxsbGT004sXdU33g3tLRTEMUywWp6emq9Xq5tam7/vPdqcxSSp78tu/eKKAals3bl28UlGPwUGlxs+fQhSFf/JhBdlfeQOzWaF44t/7xRNZd2v5yq2rN6tf2kQYMWIqWyjnspzV3dxUHd+NFlIMlvIFGvEcMuMwKCaQblpDHMtJEouc41WDi0C263mmncwm+ZbvOi7M8wAAAAAAAAAAAADwUIX5N0/Oz80mlOWf/MH1aqeqHvjJ74MFRgnCNtXoVX/EGgc9ApZls5ns5OQkRdE3btyo1+venqrxgiAMDQ298sorkijqhk7T9LMeGGXERHpseqJczvq2InHUV3QYdmNlc3NjYdfDuHDi9dnxUhxWPDFZ/mS3afvBY2yQMLeWL+ndDZ7yzG7D0Z6gl9iEnBmbniqVJEuti+x+mohJZJJJSUZGo9JWVNMaPDtPkMgJ24Q3GDL9sRnqii6kaTkhMu2eH8dKj8vMDwQR2qatqmYxLTNKj8ReeIxnpQAAAAAAAAAAAAB4TMQjS8XvA8YERvliqTQxkS+TPH/mAjcdnrv3OXNDa+0sfbhYR8h7SIxlv4FRjEgkTZw+MzFekrn+Emv7yq2NWqXzpM8We56nqpqiKFNTk57nYhzWG41B9JPjuHK5PDc3N1wuVyoVVVUP5VH6J2h0mqTk8kwxLbDIUA2l52THMyxiSeRZqlrf3NU4uTycTSdk5CCjsVjVbC/
                  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
                  nSvvOXdhsDY6azu7sDRtlqeq3+8FZBJt0Zi5IBsrZ2tQruYD7iXkKmiZtQ6Ah9iN2piOz/z957Pzly3PmC5R28R3tvpns8ySEpUobSW+1qn0RtxN67i9jYiIt7P9zf9eLiIm537+7dWkmrt5IoQyO68aZn2qPhPVDeXmYVgC7YRvfM0AlfjaTuRiHN13++mZXZbNZrpZqgIxRfLZfK9dqInQiknwnNXZ5vlj8oAYH1unoUQ0iGIthowlOSKnt//BjMiJ1Zn5pZ3UY08f39bC1TqcejoalL29ndRxDQheZnZtdW4ihRwpHO7WIWglmeubkpwoeW93YqYN6diYNMRq835GUaZVjOssThQ0VRFAgIHnQx5H5LdmZ7bTW5yBbv/CHVFNSBvEVNHRcK6VwOJE48qhabT7bmrobkKl84SAHNQSrq7qpnEQuF/T4rNxB/mHJTKB3snOTzgoFoJwblXbmSCOI7o9cxdVng62WtIesqNO1isVpHKAuZWlyN+dBS/vETxz8YTSbAXYovzq3MfpbVOA+H4nojd5jPgcjFayKikoPKvUpu99AQTbFRU0HaUFEO5/2rdDIW8FlZHonGk0Ef3czev7efL8MuGhqKkkfJ6JrpaJSH80xduR5RDj7Y3z88yPMCPJ2mrD2eJi95wjPx6TvZvH2oqKkoqiiLgaCF48iEJjShrxEZolF5/Mlvjq1qof4yjpzEcZSmaBSeuwTLKSPCGh3koq/98M8vwZ1fpqqIjd1fv5c/rpThC71nR0PPwiuvxDaWge9FQBQp7X3yNJWrNb4aRx4zsRWQe/rXNDvkVbM7+4c7meLAMATiEF+o7t75NHht8eq3py9rGmZ5qae37+7tpqGDvvAYMIKIbdzaSqBm+eTOflNGEqPKijgHQByuQhDXH2IwqVjKHj8AEKWiWlZNpP0EtXB1dWPjceagbsi1XEGq8Hyh1EJVPt0fvjwbC94vFi3KF5hdX4+ZB/9xdJLZz4sSXkXLTZWSBCkUWByKiKOKVqfpWEB+mCvlMtUc3iAZj6KaovHCZMSGorOXX1kNNcsPhdKhJk5Vi1enY/4npSYAyx4qEJ2PVqu3G4WTap1i5a5UnHIKowZfqmWPQQJQUpAanQreiM/FwiEEbSCJmfl4iGjm7957kM/zEP6TIKhrvtgPtmYSO/lKQWwlPJWTk0xmL1Nq4kqNOlx4g2P8g16Ph/dexha3l5ZWYkF4PIJWffwgc5hNy8Z5zrhE4X04IOEC/bobhvuVMc7jxQmxZ5mTpiivh22Wa7rehf0wuTxMH/ZLharoiSejcz5EEBCdDvtDTJQp7z/hazxftRoxIWiqfLVYLDRlGfEGh/PBizAq5dQQdlI7uzv5Kg/3PNrdMTNXVuZmPk5nbMhiKoJc23+WgrWfnrqH2Vv3CExvv7EalZvHfKVkOAsFAHjmcg7wJFrAkwBp0h0guybkCAmRoCf4g+355IO80H3QKsiaaNbOmnbupw9PYOkGo6zmk2zySiQUmY6dZOszKxsLfnU3f/ToSb4KIIxV1SRKISxFUIt8XVIUQ+VrpWK52halqaqqIEp+P4sTtGUp53cC6XS6Xq9b4LuKAgS8vLS0trZGkqTX62U5FrjnxYUBG+F+9/vfl0qlb0hhFKhVM7NfrDWaOmohejVfFK0oyVIehqmz4YhPbBSK5RKv2vtokWY2W6omvKFF56veRDQSnQmTDd/U8taUfaUExQX9CBLw+TGG0aRGIXewV7h8bX4t5Akaqlq4/6wAT9EauBHX0Jone4W6wIORoGq1WBKxKM1QHMMqXDAeaGQyhUpVBL4eou5GOlOqJ2li4PvbYGCRiDeENo4fpMoaAgdviJoo5kTU9E7Ho5xPL+09TlcMOBIU0VWpkd3LVK9Nc8CgSMRZZ1NrhUq9WdXAvA25WG7KfEWul/MV+3A9tdbg64IXDdFM+3hiQxHE6u5ujucViAphmzup6s01xsMGSaTk9sgkh4XmV2IeQgvJU0ubIQfyeeK+AEkK/gCC9q30WVw0EEksRjkxHZ9bC0dgayTBBCjCT3hI1s+RjB+wKJXKt1hkaEg9lSo2p3B9/AMhDEWqp3bzvCIZqCWK9UpdYWIcg8Mze1zjt2ifNz57edmjpx6ms9WSSAeGlBsIoA3h2DyS+o8SXwX4ul/uQKnkhiQnUF9yanlNzD3L8RWoikTYG5oFLMKlcHRm+VLUrk1ZXNgToCzZB08qmJuL4USxtH9YdLIZVKuJGiKOToksUykeHDQlXVvQN6a50MKKRwPuG93NHpdaWZ5SL0u5k7wC21HrzQYvG/OeIIPkVURqVgXag0Q2NkIUPPfQGwsxGEIzNIo5CM/SVbWYKgkKPCmmW8qXQi0/CKRM4boviCONeMALfHo5m3KUCl69UisXDyvzl85zGqFQOCmXy1BRMaNeKgvmNDBeLwOibzLsU5R0qZCrO84R5fPlUikdC8y0lSoUiQGlEo6AUsU7SkVgAdJDMqyulivZ3cf5lcvxxdAcSHSswv3dfEXSNHbGG04sx1gxE5tZ9vlhNCSAdFjKT3MUR6uGzDeUaf/M2qyG6qV8oa4Ukb5CmNiUBLWKJWNB9Ej0MAEm5JGPH6Zjr4U4D4hrhB6IzdOFHUEVsHB/dxjuh915SM7Tun9Eq+Xr9VoV3g+DIb6ZmWggTuGV0OzGtVnbvphgxO9sQCPLvZV+Q2yKqmpE1xeCFEdhCBtkGRSjaQrDiZaemkrl+CRXztWdbFMpp3Ll1WAgGIjRyIldYNHFWi1/bL8cAd/Glxq1XKpcu7Uc4EgfiZRHiJDykKHZtThr1Hu1ndFqviCBNJJBn6YcgCSv1hKlUASqkqpNzY8bfxjEu7gYQ7THxWyq3DIZRONL5UImzN+IRVm8LCDikOSXCk5HvYRPzX1akBV7ehYIEbHZK+tBJPt59qRQ4BEu0PutZna3mXXKfBhCRS+tsqxRKmQbfHhjI2xbkNqU6uXOJv1TM/XEIhFfjGzuPkgBu1Tto5F0hYcedXua9rDAAZSMFx0KCWaEW/b5TSzfEOVpb2BmakmQ8vs5vlQ30B6ttiwM6I3fh2oNVYbr0a1P9WYx32zddGdZ4RJy/eaNqauX5x4+K+cUXe1zjChO0onVBC1J2XKqovZ7Tio8G+NQVs7vFRXnrK5z4k8S8Y3ShxBHsDgy/sXhOOv3RafmsOyHlUaZ14eOR2s0CodP7sHrctkygd5cWZ5dXounyyBVPqzEN2Oz165eI+COCMYXjXkNaHcK0jk5FsUwKrIQZ3WyXjws29egdjoyTbjtlAxhDOuzbywcUhHGEBQKyMh1CaiVsuOkb3ptcXMlQcv84f6D47o0hLeWaSjlTImXYLSyVClXbGiiWa5Uy3VbXZVyrcnLnghJgfR6IBs1oVFL7+dEUzVRSxDqtaYCAimLUee9rR2I0js3H/NHNA2Jzm965m295cKhAEESwRCCZ5uSbFiReHJ1U8g9Szcq9kFmAyalVvKmLxaZmbnutyv4Ea+XtjAdTAEV6IjPi+Bq5fiw3IpWUpWv5PbKyiJlOaCBDcfXE8rer9KFKi9CJliOnyzNLQTDgQSDAAQIRSarhqQ0/AEDmxRGJzShrxOhpmaJuaOddq3kqwBhEY8/6MM9Su7zp4UmMvCMxa87WQiBYv6plXiQpWxAByKikN/LVngDpbg4iNaiwDMYSF0x05Rl2eQ8HNXkLFm8UG3UImgCZM4rYUY8OEhl0o3RqMpCFBBiLMsbByBOKex3QgxGOpl5ppDPwjQGxlK1USgVyjFpLZJgUZD68QBJax6zkxN6Y34bVVEYhrMhbzg67xcz7x0XQUIFsb+GyIWyjJhsdG4EIqb4iiTVZSY4tz6L7BkA9jTk/HNrLErQTHxtfVOMiTrrD0XnGDn19CTXqNR01FNJa2uRiMdbadYQjzcSiWuZvYZSA2iAGtyaXC1WirmijTT5Wr0ph02/J8BiKDWTjNKMcrL3NFtrJWYg+a3nDvO1m9M+jvYSolMZ5XNHxVqjDmsUeq1QEq0kwdAeauQ2QYi4FdVkaa8nqIpC7WXdYAQvjaBIjrWaDVmHe3VRVw6YH6YPmUKxIczWschcgtk9UqhAJMARXjl/WEPk4aB+MB88XCM0vRwzq49z2UzVOZSj3V1IXInEGbRiz16XeFj3ELX+uofkpOKxldWNeGBaoz3++JJXz+1mslX4JiHbAp7VegMCT4xAvAB4kkwju7Of78jORoKl2q1pgAS9RHfZmvbT4ZmNBC2cROPzDJGESahleQJcgDEYT5DBsDmYpe8UchlYFYUNqlX7fFQUGVbZAUhWkBpeH4vjTNerVuNS3abOr+FIZHFxsfOrzzu43PPxJ59cBHV9RR2uZQGPqmuqS3EZhiHZIInB8qZcFQ2ATDofyrIiSW0VjUe8/sCUz+Pbei0GN953Sh6FtKTLABxpDb1+uJP+7g9enYkITx7fPXnKD3VMpmkKfMPQO1gFhe9hswQTpPCAJ4AIBwBCuIYpSpKiDHE58YjPjzJSNZdBencmJaJ+L8nxisA7Z6M4sNWwGoJk0ATorn04jSSKqubeDiSpqiKKQzmp6zrP86fnuugG0uAlwxygvSyNTSdiwHanV/yh+SV3sahUqyuDTjULB9hQKMkEhPWbQdO1jmE28rIiAhP2s4BFz4QeFomKYpyjMGrogtA0T6+NxxDEy3lwkjq9+xYASzI4G1tav+zN/a4gmbgvmfB5Iz4GJRB/OBSSqpZYs4/VBk9ygUAk5KOOPilo6uDTgoCb5jP57Ex0c2XpjWjiaf3zjJ7NCyJB48k4YBE1ux6ILa+4vsHnGk2LsKYTERpYv1ApnzvYoXLu5HEutee15l5/99WltS2aIMzyR0fOcWdAyrIkuBolMJzwBxC8Cm/sCkzPTq/evBEHkBO1CBDIdUTPuhho67Bhr3QPlzKYQkPDrFDAQxmooomu3lRNEwQROU9hVBIFMGa3/YKIxAUJBAfZIgjMmuy6QFyxL5hrvRITDniCQaBU/OZrIZDxuvGxrMgaghqS1dg7yL727a3lBeQ48+jB04apGCgCUpdIJEl6hZVrgUX3SUZiGZiNKChKbv9obXP1xlaYCewbdzMaX24qhuleEUHlGsgGMo2l+QhLVYFVYgEyu/e0at1Y8fq4SNKqx0Kx0tGHdaXJTYeHdFeUZFHt4wOGIVPxEOtNBgK+y+Epl37oYiktWT23goMQTnNsIBGPbbwBJOunMRMnMJLEtbTbTwqCoLlXPgVJVVR3tNZUFVicKz7pJpClZZ1dwfNwQNsTGG32aXv9hG/qOBIJeglNkjXYvFtVRAkZ84ZAhkLmpqKUXujWB6AQhqiIKMd5UbKB9Gcf9m54PAjcJqqbuVzGliA8/d0/FVm+fD1Y/uMDCWSJyYSH8fk4nDJ8oWC4HrKkqqC6ynw0lby2FmxUjsspKbTwyltvx1ACxzGtwheeFap3ebhZwGXG0bDPTweMBt9ETr2RYdoelfSA8MSNflv6Yvs0qBFuuSHpZiOTzSyG1pZWXg/Fn/Gfp7VCUZAktXtfBkjtaYZWDN3UhmwSRtFKOp3mIv5LNxNJDAdcV3t5jhIEnViY9gqPSrVase98Z9BLeCruNxpaMZ+72K4QAK9G6gPjwUhq/CwCY72+aCxIn3xaUuTh+26E4slJ6jDrxALhZD+TjEc3Yisb3s8/rWSePKYIhHxz9Y1bMAkr7x/Lxd1iIlGpWg4nAVswjIrOJLxKTigXCz29AL4AC8EJmiRHuk/cPt9FMQzDLSAY1xjWH41vvPLWYqBQurfz6PP9DDo6XRG63xEDKYkuj33zqqZpoAnrNAvBUZTzADOsn1+UM4kwzSWDnI+NzbrcnVzLZiXEaOZKualEbHPhzXjsKf9ZSswVQdIna723Y9G+sCe8dGl1cWkzjBsgo6M4LwKAinO+mJ+jLUJUBFe0snMewQraCYvPQ8eCU4jwWDC70knoJxGdZEA6jWk2ogc4SdNkljWxyZX0E5rQhLocKzx42iJRHHh7BBlxbJ1SEyuf/PK9w6oQWbh25dqN6WvbgfwdvigYxhgxUTj6rPec8RexXRTDMByu9zzXW9tyce/pwae/edJ0FUYZHI8tXL62HPF6oW9VLSt3u1ATZYyZ2Xj17StTqfd/8fH+flEkvDH/xjvvvj41Q9+519zZU889EhAKcTbsW7u1HeWPHvJ1hUkmA5bl91I04wkEwuGoCY8tMjtHCoIfhFw+Ox/aACAulNyTOiHGSX0lCNVd9RlZ0UHe3pItRXu40MxcfOXma4kWqiJwq7X1MAiABhfWi3fTlqx0A72RiFgSmpKYf3a4vrl1KxSl/AeP76cVocorpmU+zy5akCLEL18OwdspLK0uFG7/7r2dnH39I6uUj06o12MRX7bpRcbJhRBFASjLnXvBiXsDGBaOBz0401DdoRbRdAvMyWRIDqdpuO4LIIkk9pZuaJqCuXFfCoGhxeNH4B+8ZtbyX3rnz165uZEtP/ms9Gle7VxyNQ7hGDBLwt0wCv5kAmG7SjeOxlLAEChCliXL7NZBWZKH6QOKlKs1oVybur4Qo9NFTywSJFEkkz4Z8a7SMD6EGSIZixNqRTQU2fUh7E6XMc7rRwnZSWO0YXUPpzDKJS5dCusrumXpvFa5/dvf7qRrkoywnjbwbPVO4DbwRImi6gb1NhKURYujPTjNdBdGfR4yHksiFL94OTBruqCiUS0IPEpj0/Eo1UhXpHpt7EKHblgACTMREsfJr7y/J75m8YkkML/Ph6FncbZROn76wT9+fISibnRn6qqzywOe/AXPmGBkWRYF8UIQjsJ9cMv0142DZy0BItmd92/ffZjKda9+GDoy5CwUga/kPvz793eVrhMSLUPTg0szC5BF+Bcxbm8sEt9YW6RCyHd/esWOjnCPHknh1rd+tLH2dOfB584FHeBJnzcQCuCpxweqOizBAYCscXzntye7e9G5rZ/+6G//0kzv/OqTu7tyA9a0sw/f+/jBbq7ryE3TMEiPvvDcM1F59OB3v2jUGm8tziRntv2Hn1ZHpwvJ1VdfvX41FCg8+Y9/fpARFCW8PLe2/jeXzi9lMAWM0pf8HpK0Xpa/IZBAwIujZ+F0nq9kfv9/vHeIor1KZbSiToPnVUCI2uSbrvOVJZl/9vF/f/9pQ2p0fVE34D12x3d/8Q/3g5dvvLJx64erlyV4G9JuQ+qO16Ig1LIF6fWZebbuoVAQHEoV8xjJf2sWoUJBy6DJWuqoCtIHZkR3YJwUExo4tdLJ4/1PfvPgsCueWKbemppLSNHZzWu3vru4wO/86pcP9vIN0Zf0r7zyv13+Iq82BWNK92s78Acm6UEWA14C/ZIuWsUsJBCLBjDrpJDPtjnGhYOxSxsLRBh56y8332g7AYLEkdf+bH1lb+URvOvpNJhQ5NLlS1bxn+B7RNOzt1Yr9/7P3+43CM/W1fWt6+8s3f39EVISv+Ju2dLNxv4n/3H0ZHFq6dJf/uBvfmqm7//8owcPU92nvGqWxTd4T4QmWBaEx2HQR9EEsY7QQ2QKUprQ/GxAPKrU6/1eCeCGYDIeMLVMtVj4anCN83iCsTCVeXysyBd8wRIFBvvkg+zuH503MEx2YW59/jsm36hZ7ZOLCBQNTieDan2nXC71Q1Ik4Gc11ZKlkUdngbbqQEBBknILyELY8Ozla2/fvC5+/LP3Dp6e1Azk60WFoztPP/3wScrt7uBrZohmNqWH72cP9/1T2z/90V//5eu5/ffvfPbBTqo7zbKQxet/9vpWTKo++vQf//VZFUWNuVtvb/qWUbtk4PNyFMW9ECOlaZxlfPU6bkzupJ/QhCbkTss1s87XrTnKSzDs8J3/XYEjf5L1E1H/37z93aVn70lCo/7lDZ+iGdbjHR74L14sNbXdT/7t8PPTCpFpqLoe347PJhbJ1McfH5YrkFkaXy/f+cOT5f+8EIvPLqT2nl3gXnrG64le35rjwsiN761fhTftgfiIkxSGxJNrs0vbux/9X384bkiaC8Q1IYh7uhNduPqjH5yGmPTZmUN88eqNW6/Fo7VnP//nuzlBkcLLseXN/3p5nHEORcSGYRrI3u1/Ob4Tvv7GrbXrf7G5nnv24d//ATz5PBHMkBq1+//2s/u1bF2DYzcNTWvFMElWDtPZb1+KhjyiijxvLvQSyVTR+oM/3Amj10LL84uLn+b3z3UOJcvRMnwjp513siTp8fnbr812WSXcRyLKvmgAJUoIoo7bQ6XeyNebweW5EG74wgESb+Sy6S+VYVLt4S/fe1Q6Kiv2vUSnQn9BBNif+vxf33tabJS7karBhPCVi1TMMA/nazYlXf/q6d/XvTAKFyhE3orRTnxqM5hjWY5jW0XvpiDLpuynWEZReUTpfYUBtagQFVl585JwdPdZIED7Vzaupj560kTUc73soKkmLzeQBOuF5fbOzmAvGAdLDg6ccGBR1c/5gwjW7F69a/KSEtIpBr6x3mwvSJIEGvR5cIkX7T1ZvoswjCSJgD+AobVWfyyDJOeTtFoX7dUDppu39UbDTKCUhWOyIvXybUDroqSKmoAyLKMaoqro3XuIZNUQlAYS8/hxuu5ikcfLMC/YNIC/Kx/d/eCfD+52NNryekKzN3+4pHz2x9ThyV6pvc7pjftDdJAo7u+aqjJ4Xs7+M1PXjEa6tMf/09+ltr51IzqVXJKztxt8w4zhlImisiJ3zxcxkFqD16dxhoJJiHCxiQBBaWqlzku4GfWGgarURn7B5+e8ul45/uxX99M1njdMCkEZaBu1oRY0XMoWTsAXP/Ukx1DuU2FoivL5vC9ATroB2hfMBM5ScGGrbSYMzXg8npZFiJIi6UCpOE4xgP3qffZLePDw+o11vXZ0ICOmf/3a1aPfP6loDUnWBIVHWZrRjJqsaP2SBUmboleePPqoxJdXNm5urG09qDwt1F1nuQLRyzxfOsk1bvmjswmvXtILubKl1zKFpUR0MeEjMa6+V9J0wxrZ3YCr8MCfGk1Bg8dyU6SiDNil3v0Xj4fxoaSc/vjfPj8s83VFxz0W8HMoWnWNFvX5fBTcRNc2qKl4MEAFtNqzOuKsiNI0DQ9n7uiDz0PNxGcw4VHTgAvDgRFgQDUbAAwkhmo7EKURA6J0aztUFa9n3K0JmoFU600jgXXrA3BTuJfxWs1U3VIHvnQBFNU7Gw/LVqGeSbfeiEERqZZ++PG/pB8S7W0WcDkUOIFF88nd9N7hbtG12cGboGY23qbufJiuHWiJmTgRyh0fSmJZMeRaNVtK3AqHyDTfdT4RDxy4JfugA8f59jkeBIb4AhwBHaDAv4yQZ4x0y21PVT/O7jT+tXay9daN4GxivlnJpMQeh2ZZcO8fsKvTnfa90IMmOdaPmINPgKY4IpScD+ULn4nVitWrrgSFeOcTIV7da+Sz5gXfDjPO0IdMw4D6MOblvp6oN8RF6PzxgSZJ1oAh6QpSOcrX1sIYDc++aLrdpNqs10wT7uQwDR0uDDoDSfgCXo8ne3BkWILTJMlgobnpUE064ov5voljGBL0ezWBl6URqLQjIJbzuAUUXd3eXJ9drN/+//74NF0WYeqLfmX2M1LO6zsYOUyUTjQkUZJQFKE/nYB1fYPP1Y7En/0/J9tvvxKJTi0upVOHrsBJ4Igv7Of4wpO9p5/
                  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"
                    alt="editor"
                    width="1020px"
                  />
                )}

                {location?.state?.subCategory?.code === 'SEQUEL' && (
                  <img
                    alt="editor"
                    width="1020px"
                    src="data:image/png;base64,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
                +d7TF1YOeibHpax3pR32XTmqY5VzPdHZrhE55rqtezVdGhQ933XfN4aOoNWfntr8zHpx7z4sBsPcnnW0teD+q7TufyT9quTDCPm0pDll27dm2jsrIyqmz8T64qCzOVlWuql0FdUj3mW7O99TWtOe3Kc25rjpDvVj8v/7XVvKbmGrKMUz+2GjJuAABAy7TP+5kFi5ZGUVGnD1yZb0R9e/k7sX7d+r1ig0nbNgWxbv2GaCwd9iuMguw28zdNB+zfaafDv/vu2qhcsybee+/9Rh2Pncmne9+CfavDzH6F7XY6/Ip3KrL1oiLperFftmHv3bXrorE0dNk0xzTnmnK68/WsqhFve2eq17N9963eaLNfPQ5nsubdqlizpqr6MItN8Xho6PjkWvJzYkPX6VxTz+PaNmS3n49fY9iVZbdu3bpYm63v773/Xrz/nsO/pFC9d94++1SHmbZt2+50+JTrY27ZX96O5tLcf28012tac2rueZ5L/XdeYz7v7szm5+UO2fNy4Z7/mprblWWc6rG1K+MGAAC0PHXGmRr5cYztLt9wrX2+7cnTt6vjvqcv0+Yc/5Y2z3fndlvietDapqcue9O07sn21ufZ5mBep2feNZ6WOi9b8muN9Q8AAFqHzWfgrahYFQAAAAAAADStzXHmX/7jh9ES/dvXvtxixw0AAAAAAKCh9g0AAAAAAACSEWcAAAAAAAASEmcAAAAAAAASEmcAAAAAAAASEmcAAAAAAAASahNNoduAuODUw6Mku/V1y1+LRyfNijeyi4++6Kr4aum7MeWe2+PuuQEAAAAAALDXafQ4c/TIS+PSUz8SB2y5JIYMGhBPzPtQnDKwS7RbVxYdYnd8Jm68uTTemfjtuOGpSOLSa8fFwIpfx2W3PPuB644+4zMxOF6K2x9/LWLoJXHbqK7xRvW45eN5bMSMsXHNPQEAAAAAAFCtkeLMATH48yPj9LZt4tCjPxIdK5fG1AceiNufezcGjzw/xpx6WJw+MBssu/yJu34Y41vRXjMDBx0bQ9q3jxezODM9mkYeh4bEH+KzN/wyAAAAAACAPVvjxJl+n4wLBh4WXfLvK/8cP7/l9nh4ycarpj9we7xZcUlce85h0THWR+W6aFXG3zA2xgcAAAAAAED97F6cKT0+zmo/L37zhwlxw8RL4vpRh8UBHUrixJMPj4fve23zYG88/pP4wUFXxVcHfSTOOueUuPf706KxlAy9JG4Y1T3emfnLuPGul6K884nx1S99PI47qH319asXPxfj75gUr519VfzXoDbx7M3fjx9Uj1rP+Oo3/z6OW/NcfPXGSRFDPxfXnt0nulQfc21NlM2ZFv9567Tqc+VsJbv9a6/5ZPRe9WLc/qNfxNFfqu9eLYfHmGvOj1O6t4922U9rK5fGtPsmxPg/rNx4demIuP6iAXF4Udvsh3XZ/f82u/+S+MrNx8Yh1QMcG3ff/JGYetXNcXsAAAAAAAB7qn1jlw2Ir190TvzdRZ+LK449IMqf+kncPXNFdnn76PGx8+OG8w/faugX7/lpzHw7ot2H+8Rp0UhKPxPXnHNYxLzJG8NMFkCuuPKTcXTMi9u+Mzb++e7nslhzQoy56MQof+TP8WYUR49Bm86G03lAHHJgxKKF07LfOzv+7zl9osOSqXHD9f8RN0xeFh36nhmXnX/ANnd4eFz6pWFRGq/HvVmYmb486m3wF0bG6Qf9NWbe/YP47HceiJmrSuL0kefEKdXjkgWfz58QB7/17Mb7n5jVo+z+v3LRrLjmqrEx9a1smLeyACTMAAAAAADAHm834syqKF+zLqJtlzjxokvi61/4cowZWBxrK8pi5brtB5pq69dHZTSGPGgcGyXL/xA/uOXZLLBkhg6Jow9cES/e+4uYtiTijecmxd2vrYiO3UrjtOW/jbmLIw7p+fEoyQYtOePgOCSWxmuP53uu7Bdt266L8iW/j7nLV8bcSVPigd+9GK+9tXWcOevK82NI55Ux9a6fxG8aEGZy+7dvE7GmPKY9tyxiyaz4weRn49l5S6uvKzn7+Cht++eYcsvkjff/1ISYvnBdNq4DAgAAAAAAaF128bBmh8el146OIQdFrFxcFtG9SxxzdHZx5evxP9//ScwsHR3XjizNAs3ouLHthLjmnteiZOjILJxErF34ekyP3Vdy6rDo0mFFPHvfL+PFmgu7fyg6RnEcd9W4uLv2wJUro22sjEkLl8bpHzs4RnSOWNfz4IjFz8Wk6sgyK15cWBqfOPVrcVu/pfHGwnkx9fFfxPgltW6j84lxwYHto2zmpLh9bjTYb15cGJ84pzSu+Y9r4s0ly2LuzKlx710Lq6PSWZ0/VB25Rtw8LkbU/qW3AgAAAAAAaGV2Kc6Ufv6cLMy0jdXzfh3X3/JsxNBN55tpe0CUlh4Qv3l6Qtxbek185ehOccigLNB0Xhb79/lIdMzjzZ2Ncb6Z9tFh+WPxxJqPxylnfyaO/kOtQBNL49Grfrh1nKnx+J9j0cdOiMOzsLMuC0uLZkzbuMdNLIy7v//tmHrCsPjEsYdl0zAkLjt6QAx86Mb4r6c2/mrH9ivj5797N0YO+nhcWvpSwwPNUxPi/77UM84646Q4rk/3OGXU38fgQc/Ef934yMbrK+bFf1/300YJVwAAAAAAQMu1S4c1O6V7cfVeMg9sOpxYfr6Z6ye+HivbFsdxIy+Jr19+VVyahZmoXBOro20csinM/PyWhh8ObPvWxBsvTovxD7wU5Z37xWcv2nT4tLfejbVREoefUWvQzgdUH8as2vJJ1Yc263HsgDi4bc0hzTJZlLni88Oix3OT4/Zbb4//+40H4oU1naL06BM338zqJXPj4ft+HdOXHxCDR2ZBKBoiizLn/22M6Rfxm/smxA033BjX/G5pdOzep/qcM29UvBtRVBIDD9/BeAMAAAAAAK3GbpxzZmu1A80xfbtEu8ql8cRd345r88s2DbNuSTSu134Zd7/41zhk0DlxRWn28+O/j7kVbbM4c2l89tgDIroNiDEXfzluvGZE9N70K3e/tjQLIZ2i41tLNx3SLHdwlA48MUZ+4fg4NPvp0BMOj0OKsvFd89dt7nBh3P7A3HjnoGNjzOcPb8CIrowuPUvj9LPP3DhenXvG6d2zf9dtPP/O3ElzY9G64hh40eg4rVtEyeEnxlevvCquv3zjOWcWVayJaF8cpx3eM0o7BwAAAAAAsAfbpTgzbfGKiA7dY8jIrltdvjnQ5D+0bRNtN1/2TDx610/iN9H4XrzroXj+7eI48fzRcXTMiu/+6LF4YU1JfOLir8Xd/zQyTum0LKZMmhTza37hgT/HouyfNxf+dtMhzTLP/TRuf3JxxOHnxLdvHhff/mxptF34XNx+x0sfvMO5v4jxL66ILgPPia+WRj2tjLvveCSeXVUSp+Xjdf3fx4gP/zWef2jCxsOvLX8k/vOe52Jp+8Pjkn8aF/911SejdP3ceOC+WdW/nZ+vpqz9YXHJVaNjZL3vEwAAAAAAaIn2eT+zYNHS+Jf/+GEDfu3EuPbfs4DQYU3MnfjDuOGplbWuOzyu+Obn48QDs2/XlcXUO25u+PlZavm3r325geO2E0MvidtGfShmXp+NV6McYi21/EBojXHeHgAAAAAAoDns2mHNOq+P1WvWZd+0j9JRX47rR/bbeH6UbsfHV785Ogsz6+LNOfPitYXzYvpuhJlG1blnDDz2+LjiY92j4+LX44E9MszkhBkAAAAAANiTtYmG6pwFmCvPjuMOjHhz5tR4s+eJcdyp58d/ZV8brYuVcybHjbc+u+WwYS1B6Wlx6QWHRcfqc+FMalnjBgAAAAAA7DUaHmeqrY+yF7MAc9fvs8jxUpwy4rQ4/eiS6LCmPGb+bkrc+9yyaHGe/klc9nQAAAAAAAA0q4bHmeW/j//619/XumBZTJs0IfsKAAAAAAAAdmLXzjkDAAAAAADALhFnAAAAAAAAEhJnAAAAAAAAEhJnAAAAAAAAEtrn/cyCRUsDAAAAAACAptem5ptePQ4OAAAAAAAAmpbDmgEAAAAAACQkzgAAAAAAACQkzgAAAAAAACQkzgAAAAAAACQkzgAAAAAAACQkzgAAAAAAACQkzgAAAAAAACQkzgAAAAAAACQkzkRZTPqXm+J7szf9WD4/nn9ledDUymLO9DnZfwEAAAAAYO+yy3Fm1StT4tv//N9x4mU3xcAr/ju+dueLUb4uWoA58b18nD7w9ZOYtGznv13+wtNx5Y9mxOvVP20MN195tOUlhIo5E+O6UYOitHfv6N1/WFx+6/QWEjpmxbhsnMbN2s4147JxrbmibEbcPObqeHj+zm8x+8Xo3XtczAoAAAAAANjztYldUDX7/rj4R2Vx4udHxWN/0zErBYtj0p2TY8S/l8fEsafFIdH8LvzS1fGP/aPBSk7/XDx7erRoVVmsGDV6Rgy5dWLMGFCUzf/5MfFbY2LImLExefyo6B57gC7D49a5wwMAAAAAAPY2u7DnTBZi7lsSB3/mvPjHkw6OTvsXRaeP9I3R3xgWo95+Ke78fdXGwZZNia9cdn+8VOs3X/rhTTHwh3M2//zmM/fHV67YuGfLmf/ySLz0Tmz1u5Me/WmcmV33vd/mP/9/cf+ft9zW6/f+KAaOezpWRcO9+dS98fnq+/3v+MqdL8XC9VuuK3/0JzHwX6ZEefU4TIjr34p49pcTNl5WPcT67Pdrj/f98cyyrafxK/dMifFfv6n6dxrf4izE3BndvzU+xg7tHkVFRVHUfUCMuWl8jJ5/Xdw6fdP8L5sYY7bZ22SrPVfyW3pqXIwZVBq9e5fGoDHjY1ZFbPW7Ex++KgaV9o5xU/KfR8WExVtua/6E4dH78oejInZR9X2MiYk1u/ssfmzznkClg0bFdQ8v3jLO592ZfXdnnFd7j5yKWTHhqlHRv3rPoVFx1YQ5URUNH38AAAAAAEit4XGmfH48+XaHOKt/l60vb9s3Tj0iYtLshfW6marf3x+X3hdx8beviJn/PSZuOXJpXPwfNQEktyTufLV33HbDmLjyY/3irIMq48k/1WzJL4s//mltnHpSaXSKBnplUlw5YXWMuGpM9f3+8xFl2fRsZ7iup8V/3zY6rj8o4sTPjI6Z/3ZalOTj/czEGPXLqhjxjctj5m2Xx239K+Jr37w3nqnc8qvPTl0YcdH58eQ3To5GVzY7Js8eFMMHbTP/CwfEsGFrY+LUOfW6marp42L0tyIumzw35s+fEeOHTI3zrp5Y69Bod2ah58yYMHVWXHPakBg+JLvfGVvm/+zs94YNHxJF0Rjmx/gxV8eS0RNi9vz5MXvC6Fjy9dFxazYpA8bOj/n3X5wNc3Hcn103dsDG4SeMGR0T+14XU7PL5k/9enQZPyJGT6h9jLT6jT8AAAAAAKTW8DizbEXMjOLo2fWDVx14YLtYu25DPW6kLB6btCROOn9EHLd/myzsFMWR5w+IUW8tjJmb60zX+NqXTo7DSoqisG2XOHVocTz7zEsb4807r8aTbxXHmcd13uE9/OxH25xzZtMeOy89sSCqTjw5zjsyywptO8YhJw2Jiw+MesrG+9FlcdKIs+OsjxRmPxfGYSPPjeu7L8siwJasccRpZ8aY/gdHpw6F0egWz4npURq9tnPssi69S2NtVX1upCwevu3OGHLdNTG4uq4URd/RF8eFUx+Ozf0iLozrxg6P3l2y+R9dYtioYTH14akb403FjJg8dVgWPHacZu48r3f07r31V/UOMNtVEYsX9I4h/XtH9VztPTzGTbgxBhXtYGJmTYhxZVfHuMsHbIxDRYNj7E1fj7JxE2rtKbR74w8AAAAAAE1ll845s/vK4pW3IibedWv2VfvyTtFv8yHGCqJT21rXHNc7Tr1vfsx8J+Kk2fPjye694/r9d3wP2z/nTFksXBbR7/SDa13WpgFzIR/vdnHiYbWjUOfo+ZGCeHZBvtl/494sB+7fBFGmUS2OOVOzgDW1b/xsq8uHxIDN3xdGUa3JKBoyPIZdncWbilExZPpjMXnY8LixjrZx8f01e7lskR+i7LztDt03Rl1XFKOHD4qpoy6P4cOHxLDBg3d47pyyBQti7ZAh0bv2hd17Rf+1MyJfDAMaYfwBAAAAAKCpNDzOdC2OgfHqxsixzd4zb7+9Ntq1LajnDRXEV/7hKzHmyO1ctWw7l+1fGmd2fz6enF0WnV5YsWuHNGsi9dtbqJF07xuDY0IsWJxFiG3qRdn8udGu3l2oW3x90tS4vO92rirbzmVFQ2L4sKvjsenZ/J/6qxg2/FvReG2jMPqOmRCzR82PWVNnxMTbRsd1Y4fETRO+G2d2qedNVK2t+/xDTTr+AAAAAABQfw0/rFlJ7zj1wMr4zexttuCvmxNPvhoxon/PrS7e/oGpiqLngRuy0LI46q9znDSkOB6b+khM+lPdhzTbsS7Vh2N76fWltS5bX/3/+v7+kQetjWdfX17rsuXxZhaTTuxV34qwm7r0j2H9Z8TDM7aZ/1WzYvLkdjFqyNa1Zfvzv0v07r8kJjdo/hfFkJGfil89MC4mTmzkQ4JVzI/p0+dHRVHvGDB8dHxr/OT4bu8H4mdTy7Y7eJdevaLd1LlR+wwzUbY45rfrFTteDE04/gAAAAAA0AANjzPRPUac3y2W/vL++N4zS2PVOxWxqnx+TLr5qZh4YL+4+PhNu2507Ro9Y0lMfGJpVK2rivJXHovbXthyG2edVRyvTnks7n+lIvI6siq7/itfnxSvrNvxPXfq3zvOXLwiHjuoewzcP3ZJv9N7ReGzT2+833Wr4/UnpsSdb+9o6C5xcBZzFv55efU05D+f+Ymu8cykyfHMsvznqnj90YfjlsVdY/SQRHEmm3ejrrs4Fl83JsY9tTgqKiqiomxOTLzumpjQ+1tx+eBN879LFiraTYgJDy/OxrIqyuZMiJsnbLmN4Zd9KuaMGxsT5lRUX1KRXT9m1K0xp45z1hQNHh6fmvyr+NXgYTGoUdvG/Jgw5vK48alNMaZiVsya0y5KOtVMS98YlK1LS8qyKcnHb8DoGNvlpvjuhDlRsWn4W8d+N7qMHR0DojnGHwAAAAAA6m+XzjlT2P+8uPMfpsQtd02MM+/aEGuzy9pFh7j0HwbFIZuH6htXXrkorvzRfXHSfRE9ux8Rw4+IeHbTtZ2Gjo6J6yfG/3Pz+Ph/10d8qKhr/OOXhseRbeu44/1L49Tuz8eq4/tFyU7G8Wc/ummb86nUnIdmRNwy+t5N91sQJ57YN049sDx21CT6nX5EHHLzo3HSV3rEnbedG/1OGhUT12TjPe7WuDIb7wMP6hbXX3dunNQhkikcMDYmTpwYN44dFYNml8fadu2i3dq+cfWk4bXO0zIgrpl4TYwZPTT6Xt0peg27OkYNj5i66dqiM2+Oyd+9Lq4eMyjGlq/NwteFMfbGsdG3rsOiFQ2OM4dFrBo2JBo1RRWdGd99eFWMu2Z4lF6STU+n/vGpaybGd8/cVFC6D4mLR46Pqwf3jcE3TY/xw3vH6PETouqaq2PI2AWxqnr4+2P86N47uZ8mGn8AAAAAAGiAfd7PLFi0NHr1ODh23fp484l748r7KuKjo8+Nfx56cNT71CcNMie+d9n0OPJfL4kRXYNaFj/89Rhz9ezo/6Px8a0zuzfR/J8V40pvjr5Tx8eoPbJu7OnjDwAAAABAa7BLe85s72YOOf2imHDYnHiza1OEmfVR9U5lFoCmx8TuR8ZjwswHdB/+3Xi4dFYs7t40YaaqoiIWT745JgwdFTP2wLCxp48/AAAAAACtxy6cc2bHCnv1jcOa5PBe8+P2a8bH6GcL49tfPDk6BdtT2HtA9G6SXWZmxU2DBsTw8SVx03XDY887XcuePv4AAAAAALQmjXRYMwAAAAAAAOqjUfecAQAAAAAAoG7iDAAAAAAAQELiDAAAAAAAQELiDAAAAAAAQEJtar4ZeNlNQfOZedvVAQAAAAAAtH72nAEAAAAAAEhInAEAAAAAAEhon/czCxYtDQAAAAAAAJpedZwJAAAAAAAAknBYMwAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQAAAAAAgITEGQD4/9uzYwEAAACAQf7Ws9hVGgEAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIzkDAAAAAAAwEjOAAAAAAAAjOQMAAAAAADASM4AAAAAAACM5AwAAAAAAMBIzgAAAAAAAIwCds+yJXhfVRwAAAAASUVORK5CYII="
                  />
                )}

                {isExecuting ? (
                  <CircularProgress
                    color="secondary"
                    sx={{
                      position: 'absolute',
                      top: '50%',
                      left: ' 50%',
                    }}
                  />
                ) : (
                  location?.state?.subCategory?.code === 'PYTHON' && (
                    <LightTooltip title="Run 'main'">
                      <IconButton
                        size="small"
                        sx={{
                          position: 'absolute',
                          zIndex: '999',
                          top: '12px',
                          right: '132px',
                          background: 'unset',
                          '&:hover': {
                            background: 'unset',
                          },
                          '&:after': {
                            background: 'unset !important',
                          },
                        }}
                        // className={classes.glowing}
                        disabled={codeValue.trim().length === 0}
                        onClick={async () => {
                          // setLoadingResult(true);
                          if (codeValue.trim().length > 0) {
                            await postCodeResult(screenInfo);
                            document.getElementById('simulationSec').scrollTop += 180;
                          } else {
                            setSnackbarTitle('Enter input code');
                            setOpenSnackbar(true);
                          }
                        }}
                      >
                        <PlayCircleIcon
                          color="#FE7000"
                          className={classes.playicon}
                          sx={{
                            width: '21px',
                            height: '21px',
                            margin: '10px',
                            transform: 'scale(1)',
                            color: '#FE7000',
                            boxShadow: '0 0 1px 6px rgb(253 134 67 / 58%), 0 0 1px 0px rgb(28 196 54 / 20%)',
                            borderRadius: '50%',
                            position: 'relative',
                          }}
                        />

                      </IconButton>
                    </LightTooltip>
                  )
                )}

                <Box
                  className={clsx({
                    [classes.pythonEditor]: location?.state?.subCategory?.code === 'PYTHON',
                    [classes.sequelEditor]: location?.state?.subCategory?.code === 'SEQUEL',
                  })}
                >
                  <Box
                    sx={{
                      height: '434px',
                      overflow: 'auto',
                    }}
                  >
                    {screenInfo?.prependCode?.trim().length > 0 && (
                      <AceEditor
                        mode={location?.state?.subCategory?.code === 'SEQUEL' ? 'mysql' : 'python'}
                        theme={location?.state?.subCategory?.code === 'SEQUEL' ? 'textmate' : 'monokai'}
                        fontSize={15}
                        value={prependCode}
                        readOnly
                        height={prependCode?.split(/\r\n|\r|\n/).length * 22}
                        style={{
                          width: '100%',
                          // marginLeft: '178px',
                          // height: getInitialHeight,
                        }}
                        name="PREPEND"
                        editorProps={{ $blockScrolling: true }}
                        setOptions={{
                          enableBasicAutocompletion: true,
                          enableLiveAutocompletion: true,
                          // enableSnippets: true,
                          // firstLineNumber: 6,
                        }}
                      />
                    )}

                    <AceEditor
                      mode={location?.state?.subCategory?.code === 'SEQUEL' ? 'mysql' : 'python'}
                      theme={location?.state?.subCategory?.code === 'SEQUEL' ? 'textmate' : 'monokai'}
                      fontSize={15}
                      value={codeValue}
                      // maxLines={screenInfo?.inputCode.split(/\r\n|\r|\n/).length}
                      placeholder="Write your code here"
                      // showGutter={false}
                      highlightActiveLine={false}
                      onChange={(code, event) => {
                        if (isPaste) {
                          setIsPaste(false);
                        } else {
                          setCodeValue(code);
                          setIsCompleteCode(null);
                        }
                      }}
                      onPaste={() => setIsPaste(true)}
                      onSelectionChange={(val, event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        // setIsPaste(true);
                      }}
                      annotations={errorRow}
                      onLoad={onLoad}
                      style={{
                        width: '100%',
                        // marginLeft: '178px',
                        height: '90px',
                      }}
                      name="INPUT"
                      editorProps={{ $blockScrolling: true }}
                      setOptions={{
                        enableBasicAutocompletion: isEnableKeyIntelligence,
                        enableLiveAutocompletion: isEnableKeyIntelligence,
                        enableSnippets: isEnableKeyIntelligence,
                        readOnly: false,
                        mergeUndoDeltas: true,
                        firstLineNumber:
                          screenInfo?.prependCode?.trim().length > 0 ? prependCode.split(/\r\n|\r|\n/).length + 1 : 1,
                      }}
                    />
                    {screenInfo?.appendCode?.trim().length > 0 && (
                      <AceEditor
                        mode={location?.state?.subCategory?.code === 'SEQUEL' ? 'mysql' : 'python'}
                        theme={location?.state?.subCategory?.code === 'SEQUEL' ? 'textmate' : 'monokai'}
                        fontSize={15}
                        value={appendCode}
                        readOnly
                        style={{
                          width: '100%',
                          // marginLeft: '178px',
                          height: '320px',
                        }}
                        name="APPEND"
                        editorProps={{ $blockScrolling: true }}
                        setOptions={{
                          enableBasicAutocompletion: true,
                          enableLiveAutocompletion: true,
                          enableSnippets: true,
                          overwrite: false,
                          firstLineNumber:
                            codeValue.split(/\r\n|\r|\n/).length +
                            (screenInfo?.prependCode?.trim().length > 0
                              ? prependCode.split(/\r\n|\r|\n/).length + 1
                              : 1),
                        }}
                      />
                    )}
                  </Box>
                </Box>
              </div>
            </Box>
            {isCompleteCode && (
              <Typography color="#eb0e0e" gutterBottom>
                {isCompleteCode}
              </Typography>
            )}
            {output && (
              <>
                <Typography variant="h5" sx={{ marginTop: '1rem' }}>
                  {t("Output")}
                </Typography>

                <Box
                  mt={1}
                  sx={{
                    border: location?.state?.subCategory?.code === 'SEQUEL' ? 'none' : '1px solid #6a6767',
                    padding: '0.7rem !important',
                    borderRadius: '12px',
                    borderBottom:
                      location?.state?.subCategory?.code === 'SEQUEL' ? 'none' : '1px solid #6a6767 !important',
                    // width: '812px',
                    overflow: 'auto',
                  }}
                // className={classes.codeExample}
                >
                  <Box>
                    <Typography
                      variant="body1"
                      className={classes.actionDescription}
                      dangerouslySetInnerHTML={{
                        __html: location?.state?.subCategory?.code === 'SEQUEL' ? (getOutputText(codeValue, output.textResult)) : (getOutputText(codeValue, output.replace(/^"|"$/g, '').replace(/[<>]/g, '').replace(/\\n/g, '<br/>'))),

                      }}


                    />

                  </Box>
                </Box>
              </>
            )}

            {isStarted && (
              <Box textAlign={'end'} sx={{ padding: '1rem 0 !important' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    setIsStarted(false);
                    handleCodeExecution(screenInfo);
                    setOutput(null);
                    window.scrollTo(0, 0);
                  }}
                >
                  {t("Continue")}
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </>
    );
  };

  const handleSwitchKeyboard = async (value) => {
    await setKeyboard(value);
    await addBase64(value);
  };

  const nextBtnElement = useRef();
  const [sfxPlayed, setsfxPlayed] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setsfxPlayed(true);
    }, 6000);
  }, []);

  useEffect(() => {
    const handleKeyPress = (event) => {
      //  handleVideoButtonClick();
    };

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [activeStep, screens, topics, activeTopic]);

  const handleVideoButtonClick = () => {
    const nextStep = activeStep + 1;
    const nextScreen = screens[nextStep];

    if (nextScreen !== undefined && nextScreen.type) {

      setActiveStep(nextStep);
    } else {
      setActiveStep(0);
      setActiveTopic(topics[1]);

      getScreens(topics[1].id, topics[1]?.actionType?.code);
    }
  };
  const handleVideoLoadStatus = (status) => {
    setStatus(status)
    console.log(status, 'statusstatusstatus');

    if (status) {
      console.log('Video loaded successfully!');
    } else {
      console.error('Failed to load the video.');
    }
  };


  return (
    <Page title={'Begin Course'} style={{ padding: '0px', paddingTop: '58px !important' }}>
      <Grid container>
        <Grid item sm={9}>
          <div
            id="simulationSec"
            // style={{ position: 'relative' }}
            className={clsx({
              [classes.keySection]: screens[activeStep]?.keyType !== 'CODE',
              [classes.codeSection]: screens[activeStep]?.keyType === 'CODE',
            })}
          >
            {isLoading ? (
              <div
                style={{
                  position: 'absolute',
                  width: '100%',
                  height: `calc(100vh - 70px)`,
                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                  display: 'flex',
                  flexFlow: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  zIndex: 999,
                }}
              >

                <LottieLoading loading={isLoading} />
              </div>
            ) : (
              screens.length === 0 && isLoadingScreens === false && (
                <Typography align="center" variant="h6" sx={{ marginTop: '1rem' }}>
                  This topic contains no screen
                </Typography>
              )
            )}

            <div style={{ display: 'flex', justifyContent: 'center' }}>
              {screens.length > 0 && screens[activeStep] && screens[activeStep]?.type === 'VIDEO' && screens[activeStep]?.keyType !== 'CLICK' && (
                <div className={classes.container}>
                  <VideoPlayer videoUrl={videoURL} onClickCallBack={postVideoResult} videoLoadCallback={handleVideoLoadStatus} />
                  {location.state?.type !== 'video' && (<Button
                    ref={nextBtnElement}
                    variant="contained"
                    onClick={handleVideoButtonClick}

                    className={classes.extroTxt}
                    sx={{
                      color: "orange",
                      backgroundColor: "rgba(255, 0, 0, 0)",
                      fontSize: '0.675rem',
                      position: 'absolute',
                      marginLeft: '42%',
                      bottom: '-45px',

                      visibility: sfxPlayed ? 'visible' : 'hidden'
                    }}
                  >
                    {t("Click here to continue")}
                  </Button>)}
                </div>
              )}

              {screens[activeStep] && screens[activeStep]?.keyType === 'CLICK' && screens[activeStep]?.type !== 'VIDEO' && screens[activeStep]?.type !== 'SUCCESS' && screens[activeStep]?.type !== 'REFERENCE' && screens[activeStep]?.type !== 'INTRO' &&
                <Button
                  variant="contained"
                  className={classes.extroTxt}
                  sx={{
                    color: "orange",
                    backgroundColor: "rgba(255, 0, 0, 0)",
                    fontSize: '0.675rem',
                    position: 'absolute',
                    bottom: '-20px',
                    marginLeft: '0px',
                    visibility: 'visible',
                    textTransform: 'none'
                  }}
                >
                  {/* Click the highlighted area */}
                  {t("Please click on the Highlighted section")}
                </Button>
              }
            </div>

            {/* {screens.length > 0 && screens[activeStep].type === 'VIDEO' ? <VideoPlayer videoUrl={videoURL} /> : null} */}
            {/* {screens.length > 0 &&  screens[activeStep].type === 'VIDEO' ? <VideoPlayer videoUrl={videoURL} /> : null} */}
            {screens.length > 0 && screens[activeStep]?.type === 'INTRO' && (screens[activeStep].type !== 'REFERENCE') ? <IntroScreen /> : null}
            {screens.length > 0 && screens[activeStep]?.type === 'SUCCESS' && (screens[activeStep].type !== 'REFERENCE') ? <IntroScreen /> : null}

            {/* {screens.length > 0 &&  screens[activeStep].type !== 'INTRO'|| 'SUCCESS'||'ACTION' ? <ReferenceScreen subModuleId={searchParams.get('id')} subModuleName={subModuleData?.name} />:null} */}
            {screens.length > 0 && screens[activeStep].type === 'ACTION' && (screens[activeStep].type !== 'REFERENCE') ?
              (
                <>
                  {/* <BreadCrumb/> */}
                  {['CODE', 'CLICK'].indexOf(screens[activeStep].keyType) === -1 && <ActionScreen screenKeyType={actionType} />}

                  {screens[activeStep].keyType === 'CODE' && <CodeScreen />}

                  {screens[activeStep].keyType === 'CLICK' && <ClickScreen />}

                  {(location.state?.type !== 'VIDEO' && location.state?.type !== 'video') && (<div style={{ display: "flex", justifyContent: "space-between", padding: '2px' }}>
                    <ArrowIcon direction="backward" onClick={handleArrowBack} enabled={enableBackArrow} />
                    <ArrowIcon direction="forward" onClick={handleArrowNext} enabled={enableForwardArrow} />
                  </div>)}
                </>
              ) : null}

            {screens.length > 0 && screens[activeStep].type === 'REFERENCE'
              &&
              <ReferenceScreen
                id={id}
                name={name}
              />}


          </div>
          {location.state?.type !== 'video' && screens[activeStep]?.type !== 'VIDEO' && screens[activeStep]?.type !== 'REFERENCE' && (
            <>

              <div style={{ display: "flex", justifyContent: "space-between", padding: '2px' }}>
                {!(screens[activeStep]?.type === 'ACTION' || (screens[activeStep]?.type === 'CLICK' && screens[activeStep]?.keyType === 'CLICK')) && (
                  <ArrowIcon direction="backward" onClick={handleArrowBack} enabled={enableBackArrow} />

                )}
                {!(screens[activeStep]?.type === 'ACTION' || (screens[activeStep]?.type === 'CLICK' && screens[activeStep]?.keyType === 'CLICK')) && (
                  <ArrowIcon direction="forward" onClick={handleArrowNext} enabled={enableForwardArrow} />
                )}
              </div>
              {screens[activeStep]?.keyType !== 'CLICK' && screens[activeStep]?.type !== 'ACTION' && (
                <Box style={{ position: 'relative', display: 'flex', justifyContent: 'center' }}>
                  <Button ref={nextBtnElement}
                    variant="contained"
                    color="primary"
                    onClick={introBtnClickHandle}
                    className={classes.extroTxt}
                    sx={{
                      color: "orange",
                      backgroundColor: "rgba(255, 0, 0, 0)",
                      fontSize: '0.675rem',
                      //  position: 'absolute',
                      bottom: '100px',
                      position: 'relative',
                      top: '0',
                      display: 'flex',
                      justifyContent: 'center',
                      visibility: sfxPlayed ? 'visible' : 'hidden'
                    }}
                  >
                    {t("Press any key to continue")}
                  </Button>
                </Box>)
              }
            </>
          )}

          {location.state?.type === 'video' && screens[activeStep]?.type !== 'REFERENCE' && screens[activeStep]?.type !== 'references' && screens[activeStep]?.type !== 'reference' && (
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: '5px', position: 'relative' }}>
              <ArrowIcon
                direction="backward"
                // onClick={activeStep === 0 ? null : handleBackVideo}
                // disabled={activeStep === 0}
                onClick={handleBackVideo}
                // disabled={activeStep === 0}
                style={{ color: activeStep === 0 ? 'gray' : 'black' }}
                ref={backButtonRef}
              />

              {location.state?.type === 'video' && screens[activeStep]?.type !== "SUCCESS" && (
                <Button
                  variant="contained"
                  className={classes.extroTxts}
                  sx={{
                    color: "orange",
                    backgroundColor: "rgba(255, 0, 0, 0)",
                    fontSize: '0.675rem',
                    visibility: sfxPlayed ? 'visible' : 'hidden',
                  }}
                  style={{ position: 'static', margin: '0 10px' }}
                >
                  {t("Press")}&nbsp;<span className={classes.highlightedText}>{t("Enter")}</span>&nbsp; {t("to continue")}
                </Button>
              )}

              <ArrowIcon
                direction="forward"
                onClick={handleNextVideo}
                // disabled={activeStep === screens?.length - 1}
                // onClick={activeStep === screens?.length - 1 ? null : handleNextVideo}
                // disabled={activeStep === screens?.length - 1}
                style={{ color: activeStep === screens?.length - 1 ? 'gray' : 'black' }}
                ref={nextButtonRef}
              />
            </div>

          )}
        </Grid>

        <Grid item sm={3} sx={{ zIndex: 1000 }}>
          <Box className={classes.courseItem} sx={{ height: 1, display: 'flex', flexDirection: 'column' }}>
                <Box style={{
                          position: 'relative',
                          borderBottom: '1px solid #e5e5e5',
                          boxShadow: 'rgba(0, 0, 0, 0.1) 0px 10px 50px',
                        }}
                        // onClick={toggleTopics}
                        >
            
                          <SubModuleProgress id="ModuleIsProgress"
                            // value={subModuleData && subModuleData.progress}
                            //  subModuleName={subModuleData && subModuleData.name}
                            moduleName={
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <IconButton
                                  size="small"
                                  sx={{ backgroundColor: '#edededc9' }}
                                  onClick={() => {
                                    if (!status) {
                                      return undefined;
                                    }
                                 
                                      navigate('/app/course-content', {
                                        state: location.state?.id,
                                      });
                                    
                                    
                                  }}
                                >
                                  {/* <ArrowBackIcon fontSize="small" sx={{ fontSize: '1rem' }} /> */}
                                </IconButton>
                                {/* <Typography sx={{ fontSize: '0.75rem' }}>&nbsp;{searchParams.get('module')}</Typography> */}
                              </div>
                            }
                          // mic={mic}
                          // micToggle={handleMicToggle}
                          // handleKeyboard={handleSwitchKeyboard}
                          // keyboardType={keyboard}
                          // isKeyboardScreen={screens[activeStep]?.keyType}
                          // screenAudio={screens.length > 0 && !isLoadingScreens && screens[activeStep]?.audio}
                          // handleAutoPlay={() => setAutoAudioPlay(!autoAudioPlay)}
                          // autoPlay={autoAudioPlay}
                          // type={courseDetails && courseDetails.type}
                          />
                          <Box style={{
                            display: 'flex', cursor: 'pointer', marginLeft: '3px',
                            alignItems: 'center', minHeight: '25px'
                          }}>
                            <IconButton
                              size="small"
                              sx={{
                                margin: '0px',
                                background: 'none', position: 'relative',
                                color: '#000', alignItems: 'baseline'
            
                              }}
                              onClick={() => {
                                if (!status) {
                                  return undefined;
                                }
                                navigate('/app/course-content', {
                                  state: location.state?.id,
                                });
                              }}
                            >
                              <ArrowBackIcon fontSize="small" sx={{ fontSize: '1rem' }} />
                            </IconButton>
                            <Typography variant='subtitle2' sx={{ color: '#666', fontWeight: '500', fontSize: '14px', padding: '0px', marginLeft: '8px' }}>
                              {searchParams.get('module')}
                            </Typography>
                          </Box>
                              <Box id="audioPlayer">
                                {screens.length > 0 && !isLoadingScreens && screens[activeStep]?.audio && (
                                  <Box mt={1}>
                                    <audio
                                      style={{ height: '40px', width: '100%', padding: '0 5px' }}
                                      controls
                                      src={screens[activeStep]?.audio}
                                      controlsList="nodownload"
                                      autoPlay={autoAudioPlay}
                                    >
                                      <track kind="captions" src="" />
                                    </audio>
                                  </Box>
                                )}
                              </Box>
            
            
                          <Typography variant='subtitle1' sx={{
                            color: '#000', fontWeight: '500', fontSize: '16px', display: 'flex', alignItems: 'center', marginLeft: '10px',
                            justifyContent: 'space-between',
                            paddingRight: '0px'
                          }}>
                            {/* <span style={{display: 'inline-flex', alignItems: 'center', justifyContent: 'center', marginLeft: '8px',
                marginRight: '5px' }}>1.</span> */}
                            <span>
                              {subModuleData && subModuleData.name}
                            </span>
                            <IconButton
                              size="small"
                              sx={{
                                padding: 0,
                                transition: 'transform 0.3s',
                                top: '12px'
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                // toggleTopics();
                              }}
                            >
                              {/* {topicsCollapsed ?  
                    <ExpandMoreIcon sx={{ transform: 'rotate(180deg)' }} /> : 
                    <ExpandMoreIcon />
                   } */}
                            </IconButton>
                          </Typography>
                          {/* <Button style={{
                              position: 'absolute', float: 'right',
                              right: '2px', top: '12px',
                              height: '15px', color: '#111', maxWidth: '20px',
                              padding: '10px', minWidth: '20px',
                            }} onClick={(e) => { e.stopPropagation(); toggleMenu(); }} ><CloseIcon /></Button> */}
                        </Box>
           

            {location.state?.type !== 'video' ? (<TopicList
              onClickCallBack={topicClickHandle}
              onVideoClick={videoClickHandle}
              onReferenceButtonClick={referenceButtonClick}
              data={topics}
              selectedIndex={activeTopic && activeTopic.id}
              submoduleId={searchParams.get('id')}
              subModuleName={modulename}
              location={location}
              status={status}
            />) : (
              <TopicList
              onClickCallBack={topicClickHandle}
              onVideoClick={videoClickHandle}
              onReferenceButtonClick={referenceButtonClick}
              data={topics}
              selectedIndex={activeTopic && activeTopic.id}
              submoduleId={searchParams.get('id')}
              subModuleName={modulename}
              location={location}
              status={status}
            />
            // <VideoTopicList
            //   onClickCallBack={clickHandle}
            //   topics={topics}
            //   status={status}
            //   onReferenceButtonClick={referenceButtonClick}
            //   // data={screens}
            //   // selectedIndex={activeTopic && activeTopic.id}
            //   // selectedIndex={screens && screens?.length > 0 && screens[0]?.type === "REFERENCE" ? "REFERENCE":activeStep}
            //   selectedIndex={screens && screens?.length > 0 && screens[0]?.type === "REFERENCE" ? "REFERENCE" : activeTopic && activeTopic.id}

            //   referenceItem={topics[1]}
            //   submoduleId={searchParams.get('id')}
            //   subModuleName={modulename}
            //   location={location}
            //   details={details}
            //   screens={screens}
            // />
          )
            }



            <Box sx={{ flexGrow: 1 }} />



            {location.state?.type !== 'video' &&
              <Box sx={{ zIndex: 99999 }}>
                <MobileStepper
                  variant="progress"
                  steps={screens.length}
                  position="static"
                  activeStep={activeStep}
                  LinearProgressProps={{
                    className: classes.hideLinearProgress, // and set this class
                  }}
                  nextButton={
                    <Button size="small" id="nextbutton" disabled={activeTopic && topics[topics.length - 1].id === activeTopic.id} onClick={handleNext}>
                      {("Next")}
                      {theme.direction === 'rtl' ? <KeyboardArrowLeft /> : <KeyboardArrowRight />}
                    </Button>
                  }

                  backButton={
                    <Button size="small" className="my-first-step" onClick={handleBack} disabled={activeTopic && topics[0].id === activeTopic.id}>
                      {theme.direction === 'rtl' ? <KeyboardArrowRight /> : <KeyboardArrowLeft />}
                      {t("Previous")}
                    </Button>
                  }
                />
              </Box>}



          </Box>



        </Grid>
      </Grid>
      <Snackbar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
    </Page>
  );
};

const useStyles = makeStyles((theme) => ({
  body: {
    color: '#fff',
    fontFamily: 'Nunito Semibold',
    textAlign: 'center',
  },
  content: {
    display: 'grid'
  },
  gridCell: {
    border: '1px solid transparent',
    cursor: 'pointer',
  },
  selectedButton: {
    background: '#3bbced',
    color: '#fff',
  },
  gridButton: {
    padding: '5px 10px 5px 10px',
    cursor: 'pointer',
    margin: '0 5px',
  },
  gutter: {
    marginBottom: '20px',
  },
  gridContainer: {
    // backgroundImage: `url(${NewXlFile})`,
    minHeight: `calc(100vh - ${200}px)`,
    backgroundSize: '100%',
    backgroundRepeat: 'no-repeat',

    display: 'flex',
    // padding: 10,
  },
  clickContainer: {
    // backgroundImage: `url(${NewXlFile})`,
    // minHeight: `calc(100vh - ${200}px)`,
    backgroundSize: '100%',
    backgroundRepeat: 'no-repeat',

    display: 'flex',
    // padding: 10,
  },
  codeContainer: {
    padding: '16px 28px',
  },
  playicon: {
    color: '#fe7000',
    background: '#fff',
    padding: '0px',
    borderRadius: '50%',
    animation: '$pulse 1.5s infinite',
    '&:hover': {
      cursor: 'pointer',
    },
  },
  extroTxt: {
    animation: '$pulse 1.5s infinite',
  },
  courseItem: {
    background: '#fff',
    minHeight: `calc(100vh - ${70}px)`,
    borderLeft: '1px solid #BCBCBC',
  },
  progressBox: {
    background: '#FAFAFA',
    borderLeft: 'none',
    border: '1px solid #BCBCBC',
    padding: 10,
  },
  codeSection: {
    height: '91vh',
    overflow: 'auto',
  },
  keySection: {
    position: 'relative',
  },
  codeExample: {
    [theme.breakpoints.up('1440')]: {
      width: '1020px',
    },
  },
  highlightedText: {
    animation: '$colorChange 1.5s infinite',
    color: 'red',
    fontWeight: 'bold',
  },
  pythonEditor: {
    position: 'absolute',
    top: '80px',
    width: '80%',
    left: '178px',
  },

  sequelEditor: {
    position: 'absolute',
    top: '58px',
    width: '100%',
  },

  activeKey: {
    boxSizing: 'border-box;',
    // lineHeight: '60px;',
    fontSize: '1.2rem',
    textAlign: 'center;',
    width: '60px',
    minWidth: 'max-content',
    padding: '12px 16px',
    cursor: 'pointer',
    background: '#FEFEFF 0% 0% no-repeat padding-box',
    margin: '0 16px 12px 16px;',
    // height: '40px',
    color: '#090909',
    borderColor: '#f2f2f2;',
    borderStyle: 'solid;',
    textShadow: '0 0.5px 1px #777, 0 2px 6px #f2f2f2;',
    borderWidth: '1px;',
    borderRadius: '6px;',
    // background: '-webkit-linear-gradient(top, #f9f9f9 0%, #D2D2D2 80%, #c0c0c0 100%);',
    fontFamily: 'sans-serif;',
    display: 'inline-block;',
    transition: 'box-shadow 0.3s ease, transform 0.15s ease;',
    // boxShadow: '0 0 15px #888, 0 1px 0 #fff, 0 5px 0 #c0c0c063',
    boxShadow: '1px 1px 3px #a4a4a4',
  },

  buttoneight: {
    animation: 'zoominout 1s infinite;',
  },

  keyWrong: {
    boxShadow: '0 0 12px #e30e0e, 0 1px 0 #fff, 0 5px 0 #c0c0c063',
    animation: 'shake .5s linear',
  },
  keyCorrect: {
    boxShadow: `0 0 12px ${palette.secondary.main}, 0 1px 0 #fff, 0 5px 0 #c0c0c063`,
  },
  introContainer: {
    minWidth: '250px',
    maxWidth: '600px',
    justifyContent: 'center',
    padding: '16px 0 16px',
    // paddingBottom: 20,
    overflow: 'hidden',
    borderRadius: 8,
    backgroundColor: '#e7e7e7cf',
    margin: '3rem',
    // boxShadow: `4px 4px 8px 2px #a7a7a7`,
    boxShadow: '0px 2px 6px #00000029',
    // border: '2px solid #ddd',
  },
  successContainer: {
    padding: '16px 0 16px',
    borderRadius: 8,
    backgroundColor: '#e7e7e7cf',
    boxShadow: '0px 2px 6px #00000029',
    // border: '2px solid #ddd',
    overflow: 'hidden',
    left: '5%',
    position: 'absolute',
    animation: 'slidein 0.5s ease forwards, slideout 0.5s 3s ease forwards'
  },
  introItem: {
    textAlign: 'center',
  },
  introImg: {
    height: 50,
    textAlign: 'center',
  },
  keyName: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  hideLinearProgress: {
    display: 'none',
  },
  glowing: {
    padding: '0px',
    borderRadius: '50%',
    animation: '$pulse 1.5s infinite',
  },
  actionDescription: {
    color: palette.common.black,
    fontSize: '1.14rem',
    fontWeight: '500',
  },


  '@keyframes slideInLeft': {
    '0%': {
      transform: (props) =>
        props && props === 'right-to-left' ? 'translateX(100%)' : 'translateX(-100%)',
      opacity: '0',
    },
    '100%': {
      transform: (props) =>
        props && props === 'right-to-left' ? 'translateX(0)' : 'translateX(0)',
      opacity: '1',
    },
  },

  // '@keyframes slideInRight' : {
  //     '0%' : {
  //         transform: 'translateX(100%)',
  //         opacity: '0'
  //     },

  //     '100%' : {
  //         transform: 'translateX(0)',
  //         opacity: '1',
  //     }
  // },



  '@global': {
    '@keyframes slidein': {
      '0%': {
        transform: 'translateX(-400px)',
        opacity: '1'
      },
      '100%': {
        transform: 'translateX(400px)',
        opacity: '1'
      }
    },
    '@keyframes slideout': {
      '0%': {
        transform: 'translateX(400px)',
        opacity: '1'
      },
      '100%': {
        transform: 'translateX(1200px)',
        opacity: '1'
      }
    },
    '@keyframes zoominout': {
      '0%': {
        transform: 'scale(1)',
      },
      '70%': {
        transform: 'scale(0.9)',
      },
      '100%': {
        transform: 'scale(1)',
      },
    },
    '@keyframes shake': {
      '8%, 41%': {
        transform: 'translateX(-10px)',
      },
      '25%, 58%': {
        transform: 'translateX(10px)',
      },
      '75%': {
        transform: 'translateX(-5px)',
      },
      '92%': {
        transform: 'translateX(5px)',
      },
      '0%, 100%': {
        transform: 'translateX(0)',
      },
    },

    '@global': {
      '@keyframes pulse': {
        '0%': {
          transform: 'scale(0.95)',
          boxShadow: '0 0 0 0 #FE7000',
          opacity: 7,
        },
        '70%': {
          transform: 'scale(1)',
          boxShadow: '0 0 0 10px rgba(0, 0, 0, 0)',
        },
        '100%': {
          transform: 'scale(0.95)',
          boxShadow: '0 0 0 0 rgba(0, 0, 0, 0)',
        },
      },
    },
  },
}));

export default SimulationPreview;
