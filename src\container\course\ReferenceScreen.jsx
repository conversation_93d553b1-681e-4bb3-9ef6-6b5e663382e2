import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Typography, Box, Button, Link,  Paper, Stack } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import PhotoIcon from '@mui/icons-material/Photo';
import DescriptionIcon from '@mui/icons-material/Description';
import GetAppIcon from '@mui/icons-material/GetApp';
import InsertChartIcon from '@mui/icons-material/InsertChart';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import LottieLoading from '../../components/LottieLoading';
import adminServices from '../../services/adminServices';
//  to force file download


const ReferenceScreen = ({ id, name }) => {
  const theme = useTheme();

  const fileTypeColorMap = {
    pdf: '#F44336',
    xlsx: '#4CAF50',
    txt: '#E91E63',
    ipynb: '#F44336'
  };

  const getIcon = (type, color) => {
    switch (type) {
      case 'pdf':
        return <PictureAsPdfIcon sx={{ fontSize: 32, color }} />;
      case 'xlsx':
        return <InsertChartIcon sx={{ fontSize: 32, color }} />;
      case 'ipynb':
        return <DescriptionIcon sx={{ fontSize: 32, color }} />;
      case 'docx':  
        return <TextSnippetIcon sx={{ fontSize: 32, color }} />;
      default: 
        return <PhotoIcon sx={{ fontSize: 32, color }} />;
    }
  };

  const [highlightFetchedFiles, setHighlightFetchedFiles] = useState(false);
  const [referenceAllData, setReferenceAllData] = useState([]);
  const [referenceData, setReferenceData] = useState('');
  console.log(referenceData, "referenceData");
  console.log(referenceAllData, "referenceAllData");

  const getFileTypeFromUrl = (url) => {
    const parts = url.split('.');
    return parts.length > 1 ? parts.pop().toLowerCase() : '';
  };


  // eslint-disable-next-line no-unused-vars
  const [showReferenceScreen, setShowReferenceScreen] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (id && name) {
      setIsLoading(true);
      getReferenceData(id, name);
    }
  }, [id, name]);

  const getReferenceData = async (id, name) => {
    try {
      const response = await adminServices.getReferenceFileDetails(id, name);
      const isFetched = response?.data?.ref_data.filter(data => data.isFetched === 'true' || data.isFetched === true);
      // const isFetchedfalse = response?.data?.ref_data.filter(data => data.isFetched === 'false');
      setReferenceData(isFetched && isFetched?.length > 0 && isFetched[0])
      setReferenceAllData(response?.data?.ref_data)

    } catch (error) {
      console.error('Error fetching reference data:', error);
      setError('Error fetching reference data');
    } finally {
      setIsLoading(false);
    }
  };

  if (error) {
    return <Typography style={{ marginTop: '100px' }}>{error}</Typography>;
  }


  const handleClick = () => {
    setHighlightFetchedFiles(true);
  }  

  return referenceAllData && referenceAllData?.length > 0 ? (
    <Box style={{ margin: '20px', marginTop: '0px !important' }}>
     {referenceAllData?.some(item => item.isFetched === "true" || item.isFetched === true) && ( 
      <Box
        sx={{
          background: 'linear-gradient(90deg, #4ab48c 0%, #219a6a 50%, #00824b 100%)',
          // padding: { xs: 3, sm: 4, md: 5 },
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          color: '#fff',
          borderRadius: '10px',
          margin: 0,
          padding: '15px 20px !important',
          boxShadow: theme.shadows[4],
        }}
      >
        <Typography
          variant="subtitle2"
          sx={{
            backgroundColor: 'white',
            display: 'inline-block',
            px: 2.5,
            py: 1,
            borderRadius: '30px',
            fontWeight: 700,
            color: '#00824b',
            // fontSize: { xs: '0.75rem', sm: '0.875rem' },
            alignSelf: 'flex-start',
          }}
        >
          FEATURED RESOURCE
        </Typography>

      

        <Typography
          variant="body2"
          dangerouslySetInnerHTML={{
            __html: referenceData?.referenceText
          }}
          sx={{
            color: 'white',
            fontSize: { xs: '0.85rem', sm: '1rem' },
            lineHeight: 1.6,
          }}
        />

        <Button
          variant="contained"
          onClick={handleClick}
          sx={{
            width: 'fit-content',
            backgroundColor: '#3F51B5',
            textTransform: 'none',
            px: 2,
            py: 1,
            borderRadius: '8px',
            fontWeight: 500,
            '&:hover': {
              backgroundColor: '#303F9F',
            },
          }}
        >
          View File
        </Button>
      </Box>)}
      <Box style={{
        boxShadow: 'rgba(0, 0, 0, 0.2) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px', marginTop: '20px', marginBottom: '20px',
        padding: '20px 20px', borderRadius: '8px'
      }} sx={{ p: { xs: 2, sm: 3 }, mx: '0' }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: '10px' }}>
          Downloadable Resources
        </Typography>

        <Box sx={{ borderRadius: '8px', overflow: 'hidden', bgcolor: '#f8f8f8' }}>
          {referenceAllData && referenceAllData?.length > 0 && referenceAllData?.map((file, index) => {
            const fileType = getFileTypeFromUrl(file?.referenceUrl);
            const color = fileTypeColorMap[fileType] || '#9E9E9E';

            const isLast = index === referenceAllData.length - 1;
            const shouldHighlight = highlightFetchedFiles && file.isFetched === "true" || highlightFetchedFiles &&  file.isFetched === true

            return (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  px: { xs: 2, sm: 3 },
                  py: 0,
                  borderBottom: !isLast ? '1px solid #cfcece !important' : 'none',
                  borderRadius: isLast ? '0 0 16px 16px' : 0,
                  bgcolor: shouldHighlight ? '#345CA1' : '#e6e6e6',
                  color: shouldHighlight ? '#fff' : '#637381',
                  border: shouldHighlight ? '2px solid #345CA1' : 'none',
                }}
              >
                {/* {console.log(index, "index")} */}
                <Stack direction="row" spacing={2} alignItems="flex-start" flex={1}>
                  {getIcon(fileType, color)}
                  <Box>
                    <Typography variant="subtitle1" sx={{   color: shouldHighlight ? '#fff' : '#637381',}} fontWeight={600}>
                      {file.name}
                    </Typography>
                    <Typography
                      dangerouslySetInnerHTML={{ __html: file?.referenceText }}
                      variant="body2"
                      color="text.secondary"
                      style={{ lineHeight: '1.2', marginTop: '5px',    color: shouldHighlight ? '#fff' : '#637381', }}
                    />
                  </Box>
                </Stack>
                <Link
                  id="downloadtemplate"
                  href={file?.referenceUrl}
                  target="_blank"
                  onClick={() => setHighlightFetchedFiles(false)}
                  rel="noopener noreferrer"
                  style={{
                    textDecoration: 'none',
                    marginLeft: '20px',
                    marginTop: '25px',
                    marginBottom: '10px',
                  }}
                  color="secondary"
                >
                  <GetAppIcon />
                </Link>
              </Paper>
            );
          })}

        </Box>
      </Box>

    </Box>
  ) : (
    <div
      style={{
        position: 'absolute',
        width: '100%',
        height: `calc(100vh - 70px)`,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        display: 'flex',
        flexFlow: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 999,
      }}
    >
      <LottieLoading loading={isLoading} />
    </div>
  );
};

// Add propTypes for prop validation
ReferenceScreen.propTypes = {
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
};

export default ReferenceScreen;
