/* eslint-disable arrow-body-style */
/* eslint-disable no-unused-vars */
/* eslint-disable no-alert */
/* eslint-disable no-return-assign */
/* eslint-disable consistent-return */
/* eslint-disable react/jsx-key */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Grid, Typography, Container, Button, IconButton, List, ListSubheader, ListItemButton, ListItemIcon, ListItemText, Divider, useTheme, useMediaQuery, Paper, InputBase, FormControl, Select, OutlinedInput, MenuItem } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import GridOnIcon from '@mui/icons-material/GridOn';
import ViewListIcon from '@mui/icons-material/ViewList';
import { makeStyles } from '@mui/styles';
import LinearProgress from '@mui/material/LinearProgress';
import BookIcon from '@mui/icons-material/MenuBookOutlined';
import { useNavigate } from 'react-router-dom';
import { useSelector,useDispatch } from 'react-redux';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import Drawer from '@mui/material/Drawer';
import UserCard from './UserCardB2B';
import PageHeader from '../../../components/PageHeader';
import CardSkeleton from '../../../components/Skeleton/cardSkeleton';
import Background from '../../../assets/logo/myCourseBackground.svg';
import ChatDialog from './ChatDialog';
import SatCard from '../../../components/cards/SatCardB2B';
import NeetCard from '../../../components/cards/NeetCardB2b'
import {EmptyModule} from '../../../store/reducer'


export default function CourseList() {
  const classes = useStyles();
  const noSatRef = useRef(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { t } = useTranslation('translation');
  const [courseList, setCourseList] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [satDetails, setSatDetails] = useState([]);
  const [neetDetails, setNeetDetails] = useState([]);
console.log(neetDetails,"neetDetails");

  
  const [groupedCourses, setGroupedCourses] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(true);

  const [courseGroups, setCourseGroup] = useState([]);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState(categoryList?.[0] || null);
  console.log(activeCategory,"activeCategory")
  const categoryRefs = useRef({});
  const [mobileOpen, setMobileOpen] = useState(false);
  const theme = useTheme();
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('xl'));
  const isMediumScreen = useMediaQuery(theme.breakpoints.between('lg', 'xl'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('lg'));
  const isMobile = useMediaQuery('(max-width:768px)');
  const userRole = useSelector((state) => state.userInfo && state.userInfo.role);
  const pageNAme = useSelector((state) => state?.pageName || 'paid');
  const alldetails = useSelector((state) => state);
  const satSectionRef = useRef(null);
  const neetSectionRef = useRef(null);
  const [showSkeletons, setShowSkeletons] = useState(true);
  const isClickScrollingRef = useRef(false);
  
  useEffect(() => {
    let timer;
    if (courseList.length === 0) {
      setShowSkeletons(true);
      timer = setTimeout(() => {
        setShowSkeletons(false);
      }, 5000);
    } else {
      setShowSkeletons(false);
    }

    return () => clearTimeout(timer);
  }, [courseList]);


  useEffect(() => {
    if (!isMobile && mobileOpen) {
      setMobileOpen(false);
    }
  }, [isMobile]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // useEffect(() => {
  //   if (categoryList.length === 0 && !satSectionRef.current) return;

  //   const observer = new IntersectionObserver(
  //     (entries) => {
  //       entries.forEach((entry) => {
  //         if (entry.isIntersecting) {
  //           const cat = entry.target.dataset.category;
  //           console.log(cat,"ccccccccccc");
            
  //           if (cat) {
  //             setActiveCategory(cat);
  //           }
  //         }
  //       });
  //     },
  //     {
  //       // threshold: 0.5,
  //       threshold: [0.25, 0.1],
  //       rootMargin: '0px 0px -50% 0px'
  //     }
  //   );

  //   categoryList.forEach((category) => {
  //     const el = categoryRefs.current[category];
  //     if (el) observer.observe(el);
  //   });

  //   if (satSectionRef.current) {
  //     observer.observe(satSectionRef.current);
  //   }

  //   return () => {
  //     categoryList.forEach((category) => {
  //       const el = categoryRefs.current[category];
  //       if (el) observer.unobserve(el);
  //     });
  //     if (satSectionRef.current) {
  //       observer.unobserve(satSectionRef.current);
  //     }
  //   };
  // }, [categoryList, satSectionRef.current]);


  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };


  // const handleCategoryClick = (category) => {

  
  //   if (category === 'SAT Assessment') {
  //       satSectionRef.current?.scrollIntoView({ behavior: 'auto', block: 'start' });
  //       setActiveCategory(category);
  //   } else {
  //     categoryRefs.current[category]?.scrollIntoView({ behavior: 'auto', block: 'start' });
  //     setActiveCategory(category);
  //   }
  // };

  const handleCategoryClick = (category) => {
    isClickScrollingRef.current = true;
    setActiveCategory(category);

    if (category === 'SAT Assessment') {
      satSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } 
    else if (category === 'Neet Assessment') {
      neetSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } 
    else {
      categoryRefs.current[category]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    setTimeout(() => {
      isClickScrollingRef.current = false;
    }, 500); 
  };

  useEffect(() => {
    if (categoryList.length === 0 && !satSectionRef.current && !neetSectionRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cat = entry.target.dataset.category;
            if (cat && !isClickScrollingRef.current) {
              setActiveCategory(cat);
            }
          }
        });
      },
      {
        threshold: [0.25, 0.1],
        // rootMargin: '0px 0px -50% 0px',
        rootMargin: '0px 0px -40% 0px',
      }
    );

    categoryList.forEach((category) => {
      const el = categoryRefs.current[category];
      if (el) observer.observe(el);
    });

    if (satSectionRef.current) {
      observer.observe(satSectionRef.current);
    }
    if (neetSectionRef?.current) {
      observer.observe(neetSectionRef.current);
    }
    return () => {
      categoryList.forEach((category) => {
        const el = categoryRefs.current[category];
        if (el) observer.unobserve(el);
      });
      if (satSectionRef.current) {
        observer.unobserve(satSectionRef.current);
      }
      if (neetSectionRef?.current) {
        observer.unobserve(neetSectionRef.current);
      }
    };
  }, [categoryList]);
  

  localStorage.removeItem('expandedItem');
  localStorage.removeItem('subModuleExpandedItem');

  useEffect(() => {
    setLoading(true);
    setCourseList(alldetails?.courseListB2b);
    setSatDetails(alldetails?.SatListB2b);
    setNeetDetails(alldetails?.NeetListB2b)
    const categories = [...new Set(alldetails?.courseListB2b?.map(item => item.category.description))];
    setCategoryList(categories);
    const coursesByCategory = {};
    alldetails?.courseListB2b?.forEach(course => {
      const category = course.category.description;
      if (!coursesByCategory[category]) {
        coursesByCategory[category] = [];
      }
      coursesByCategory[category].push(course);
    });
    setGroupedCourses(coursesByCategory);
  

    const individualCourses = alldetails?.courseListB2b?.filter((item) => item.isBundle === false);
    const bundleCourses = alldetails?.courseListB2b?.filter((item) => item.isBundle);
    const grouped = Array.from(
      bundleCourses?.reduce((m, o) => m.set(o.subscriptionPlanId, (m.get(o.subscriptionPlanId) || []).concat(o)), new Map())
        .values()
    );
    setCourseGroup([[...individualCourses], ...grouped]);

    if (userRole === 'USER_DTC' && alldetails?.courseListB2b?.length === 0) {
      navigate('/auth/subscribe');
    }
    setLoading(false);

  }, [alldetails?.courseListB2b])

   useEffect(() => {
        const handleBeforeUnload = (event) => {
          event.preventDefault();
          event.returnValue = '';
            navigate('/login');
        };
        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => {
          window.removeEventListener('beforeunload', handleBeforeUnload);
        };
      }, [navigate]);

  useEffect(async () => {
    const script = document.getElementById('zsiqscript');
    if (script) {
      script.parentNode.removeChild(script);
    }
  }, [pageNAme]);


  const handleCardClickItem = (data) => {
    // dispatch(EmptyModule())
    if (userRole === 'USER_DTC') {
      navigate('/auth/course-details', { state: data });
    } else {
      navigate('/app/course-details', { state: data });
    }
  };

  const handleChatOpen = () => {
    setIsChatOpen(true);
  };

  const handleChatClose = () => {
    setIsChatOpen(false);
  };

  const handleSearch = () => {
    if (!searchValue.trim()) return;

    const lowerInput = searchValue.trim().toLowerCase();
    const match = categoryList.find((cat) => cat.toLowerCase().includes(lowerInput)) ||
      (lowerInput.includes('sat') ? 'SAT Assessment' : null);

    if (match) {
      setActiveCategory(match);
      if (match === 'SAT Assessment' && satSectionRef.current) {
        satSectionRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else if (categoryRefs.current[match]) {
        categoryRefs.current[match].scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      alert('Category not found');
    }
  };



  function LinearProgressWithLabel({ value }) {
    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', marginTop: '5px !important' }} mb={0.5}>
          <Box sx={{ width: '100%', mr: 1 }}>
            <LinearProgress
              variant="determinate"
              value={value}
              sx={{
                backgroundColor: '#e3e3e3',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#52c28c'
                }
              }}
            />
          </Box>
          <Box sx={{ width: 'max-content' }}>
            <Typography variant="body2" sx={{ fontSize: '0.7rem' }}>
              {value}%
            </Typography>
          </Box>
        </Box>
        <Typography variant="body2" color={'#000'} sx={{ fontSize: '0.7rem', marginTop: '-8px' }}>
          Overall Progress
        </Typography>
      </Box>
    );
  }

  const ButtonGrp = () => (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
      <IconButton>
        <SearchIcon fontSize="medium" style={{ color: '#BCBCBC' }} />
      </IconButton>
      <IconButton>
        <FilterAltIcon fontSize="medium" style={{ color: '#BCBCBC' }} />
      </IconButton>
      <IconButton>
        <GridOnIcon fontSize="medium" style={{ color: '#00B673' }} />
      </IconButton>
      <IconButton>
        <ViewListIcon fontSize="medium" style={{ color: '#BCBCBC' }} />
      </IconButton>
    </div>
  );

  let sidebarWidth;
  if (isLargeScreen) {
    sidebarWidth = 280;
  } else if (isMediumScreen) {
    sidebarWidth = 250;
  } else {
    sidebarWidth = 220;
  }


  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      height: '100vh',
      overflow: 'hidden',
      position: 'relative'
    }}>

      {isMobile && (
        <Box sx={{
          position: 'fixed',
          top: 75,
          left: 10,
          zIndex: 1100,
          display: 'flex',
          alignItems: 'center'
        }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              backgroundColor: '#f5f5f5',
              '&:hover': {
                backgroundColor: '#e0e0e0'
              }
            }}
          >
            <MenuIcon />
          </IconButton>


        </Box>
      )}


      {isMobile && (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 280,
              backgroundColor: '#fafafa'
            },
          }}
        >
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2,
            borderBottom: '1px solid #ddd'
          }}>
            <Typography variant="h6">Categories</Typography>
            <IconButton onClick={handleDrawerToggle}>
              <CloseIcon />
            </IconButton>
          </Box>


          <Box id="mobileSidebar" sx={{ p: 2 }}>
            <Typography
              component="div"
              id="nestedListSubHeading"
              sx={{
                color: '#999',
                fontWeight: '600',
                fontSize: 14,
                mb: 1
              }}>
              LEARN
            </Typography>

            {categoryList && categoryList?.length > 0 && categoryList?.map((text, index) => (
              <Box
                id="mobileSidebarItem"
                key={text}
                onClick={() => {
                  handleCategoryClick(text);
                  handleDrawerToggle();
                }}
                sx={{
                  p: 1.5,
                  borderRadius: '4px',
                  mb: 1,
                  backgroundColor: activeCategory === text ? '#fee9db' : 'transparent',
                  color: activeCategory === text ? '#ff7e4a' : '#666',
                  fontWeight: activeCategory === text ? '600' : 'normal',
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: '#f0f0f0',
                  },
                }}
              >
                <BookIcon fontSize="small" sx={{
                  color: activeCategory === text ? '#ff7e4a' : '#666',
                  mr: 2
                }} />
                {text}
              </Box>
            ))}

            <Typography
              component="div"
              id="nestedListSubHeading"
              sx={{
                color: '#999',
                fontWeight: '600',
                fontSize: 14,
                mt: 2,
                mb: 1
              }}>
              ASSESSMENT
            </Typography>

            <Box
              id="mobileSidebarItem"
              onClick={() => {
                handleCategoryClick('SAT Assessment');
                handleDrawerToggle();
              }}
              sx={{
                p: 1.5,
                borderRadius: '4px',
                mb: 1,
                backgroundColor: activeCategory === 'SAT Assessment' ? '#fee9db' : 'transparent',
                color: activeCategory === 'SAT Assessment' ? '#ff7e4a' : '#666',
                fontWeight: activeCategory === 'SAT Assessment' ? '600' : 'normal',
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: '#f0f0f0',
                },
              }}
            >
              <BookIcon fontSize="small" sx={{
                color: activeCategory === 'SAT Assessment' ? '#ff7e4a' : '#666',
                mr: 2
              }} />
              SAT Assessment
            </Box>
          </Box>
        </Drawer>
      )}


      {!isMobile && (
        <Box
          id="sidePanelAsideB2B"
          sx={{
            width: sidebarWidth,
            height: '100vh',
            position: 'fixed',
            top: 0,
            left: 0,
            bgcolor: '#fafafa',
            borderRight: '1px solid #ddd',
            overflowY: 'auto',
            zIndex: 10,
            paddingTop: '60px',
            boxSizing: 'border-box',
            display: isMobile ? 'none' : 'block',
          }}
        >
          <List
            component="nav"
            aria-labelledby="nested-list-subheader-2"
            subheader={
              <ListSubheader
                component="div"
                id="nestedListSubHeading"
                sx={{
                  bgcolor: 'transparent',
                  color: '#999',
                  fontWeight: '600',
                  fontSize: 14,
                  mt: 0,
                  mb: 1,
                  pl: 2,
                  display: 'grid',
                  gridTemplateColumns: 'auto 1fr',
                  gap: '1.5rem',
                  alignItems: 'center',
                  paddingRight: 0,
                }}>
                LEARN
              </ListSubheader>
            }
          >
            {categoryList && categoryList?.length > 0 && categoryList?.map((text, index) => (
              <ListItemButton
                disableRipple
                key={text}
                onClick={() => handleCategoryClick(text)}
                selected={activeCategory === text}
                sx={{
                  pl: 4,
                  fontWeight: activeCategory === text ? '600' : 'normal',
                  color: activeCategory === text ? '#fff' : '#999',
                  lineHeight: '1.2',
                  '&.Mui-selected': {
                    bgcolor: '#5d7db4',
                    '&:hover': {
                      bgcolor: '#5d7db4',
                    },
                  },
                  '&:hover': {
                    bgcolor: activeCategory === text ? '#fee9db' : '#f5f5f5',
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 36, marginRight: "10px" }}>
                  <BookIcon fontSize="small" sx={{ color: activeCategory === text ? '#fff' : '#999' }} />
                </ListItemIcon>
                <ListItemText
                  primary={text}
                  primaryTypographyProps={{ fontSize: 13, lineHeight: '1.2' }}
                />
              </ListItemButton>
            ))}
          </List>

          <Typography
            component="div"
            id="nestedListSubHeading"
            sx={{
              color: '#999', fontWeight: '600', fontSize: 14, mt: 0, mb: 1, pl: 2,
              display: 'grid', gridTemplateColumns: 'auto 1fr', gap: '1.5rem', alignItems: 'center', paddingRight: 0,
            }}>
            ASSESSMENT
          </Typography>

          <ListItemButton
            onClick={() => handleCategoryClick('SAT Assessment')}
            selected={activeCategory === 'SAT Assessment'}
            sx={{
              pl: 4,
              fontWeight: activeCategory === 'SAT Assessment' ? '600' : 'normal',
              color: activeCategory === 'SAT Assessment' ? '#ff7e4a' : '#999',

              '&.Mui-selected': {
                bgcolor: '#5d7db4',
                color: '#fff',
                '&:hover': {
                  bgcolor: '#5d7db4',
                },
              },
              '&:hover': {
                bgcolor: activeCategory === 'SAT Assessment' ? '#fee9db' : '#f5f5f5',
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 36, marginRight: "10px" }}>
              <BookIcon fontSize="small" sx={{ color: activeCategory === 'SAT Assessment' ? '#fff' : '#999' }} />
            </ListItemIcon>
            <ListItemText sx={{ lineHeight: '1.2', }}
              primary={'SAT Assessment'}
              primaryTypographyProps={{ fontSize: 13 }}
            />
          </ListItemButton>


          <ListItemButton
            onClick={() => handleCategoryClick('Neet Assessment')}
            selected={activeCategory === 'Neet Assessment'}
            sx={{
              pl: 4,
              fontWeight: activeCategory === 'Neet Assessment' ? '600' : 'normal',
              color: activeCategory === 'Neet Assessment' ? '#ff7e4a' : '#999',

              '&.Mui-selected': {
                bgcolor: '#5d7db4',
                color: '#fff',
                '&:hover': {
                  bgcolor: '#5d7db4',
                },
              },
              '&:hover': {
                bgcolor: activeCategory === 'Neet Assessment' ? '#fee9db' : '#f5f5f5',
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 36, marginRight: "10px" }}>
              <BookIcon fontSize="small" sx={{ color: activeCategory === 'Neet Assessment' ? '#fff' : '#999' }} />
            </ListItemIcon>
            <ListItemText sx={{ lineHeight: '1.2', }}
              primary={'Neet Assessment'}
              primaryTypographyProps={{ fontSize: 13 }}
            />
          </ListItemButton>
        </Box>
      )}

      <Box
        sx={{
          flexGrow: 1,
          marginLeft: isMobile ? 0 : `${sidebarWidth}px`,
          width: isMobile ? '100%' : `calc(100% - ${sidebarWidth}px)`,
          height: isMobile ? 'calc(100vh - 60px)' : '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          boxSizing: 'border-box',
          paddingTop: isMobile ? '50px' : 0
        }}
      >
        <PageHeader pageTitle={"MycoursePageB2BUser"} breadcrumbs={<ButtonGrp />} />
        <Container
          maxWidth={false}
          sx={{
            padding: isMobile ? '0 10px !important' : '0 !important',
            width: '100%',
            maxWidth: '100% !important'
          }}
        >
          <Grid container spacing={isMobile ? 1 : 2} sx={{ width: '100%', margin: 0 }}>
            {loading ? (
              <>
                {[1, 2, 3, 4, 5].map((_, idx) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} xl={3} key={`skeleton-${idx}`}>
                    <CardSkeleton />
                  </Grid>
                ))}
              </>
            ) : (
              <>
                <Box style={{
                  display: 'flex',
                  width: '100%',
                  justifyContent: 'end',
                  marginRight: '10px',
                  flexDirection: isMobile ? 'column' : 'row',
                  alignItems: isMobile ? 'stretch' : 'center',
                  marginBottom: isMobile ? '15px' : '0'
                }}>
                  <Paper
                    component="form"
                    sx={{
                      p: '0px 2px',
                      display: 'flex',
                      alignItems: 'center',
                      width: isMobile ? '100%' : 300,
                      marginTop: isMobile ? "15px" : "25px",
                      marginLeft: "15px",
                      borderRadius: '30px',
                      border: '1px solid #bbb'
                    }}>
                    <InputBase
                      sx={{ ml: 1, flex: 1 }}
                      placeholder="Search"
                      value={searchValue}
                      onChange={(e) => setSearchValue(e.target.value)}
                      inputProps={{ 'aria-label': 'search Category' }}
                    />
                    <IconButton type="button" sx={{ p: '10px' }} aria-label="search" onClick={handleSearch}>
                      <SearchIcon sx={{ color: '#bbb' }} />
                    </IconButton>
                  </Paper>
                </Box>

                {categoryList && categoryList?.length > 0 && categoryList?.map((text, index) => (
                  <React.Fragment key={`category-${text}`}>
                    <Box
                      ref={(el) => (categoryRefs.current[text] = el)}
                      sx={{
                        backgroundColor: activeCategory === text ? '#345ca1' : '#fff',
                        border: activeCategory === text ? '1px solid #345ca1' : '1px solid #111',
                        color: '#fff',
                        borderRadius: '5px',
                        width: '100%',
                        margin: index === 0 ? '10px 10px 0px' : '35px 10px 0px',
                        padding: '10px !important',
                        fontWeight: { md: '500', lg: '600' },
                        scrollMarginTop: '80px'
                      }}
                      data-category={text}
                    >
                      <Typography variant="h6" sx={{
                        fontSize: { xs: '13px', sm: '14px', md: '17px', lg: '17px' },
                        color: activeCategory === text ? '#fff' : '#000',
                      }}>
                        {text}
                      </Typography>
                    </Box>

                    {groupedCourses && groupedCourses[text]?.map((course, cIndex) => {
                      return (
                        <Grid item xs={12} sm={6} md={4} lg={3} xl={3} key={cIndex}>
                          <UserCard
                            handleCardClick={() => handleCardClickItem(course)}
                            image={course?.coverageImage}
                            planStatus={course.planStatus}
                            title={course.title}
                            trial={course.isTrial}
                            subscribed={course.isSubscribed}
                            category={course.category}
                            logo={course.category.categoryImgUrl}
                            enrollDate={course.validFrom}
                            progress={<LinearProgressWithLabel value={course.completed} />}
                            data={course}
                          />
                        </Grid>
                      )
                    })}
                  </React.Fragment>
                ))}

                {/* {loading === false && courseList.length === 0 && (
                  <Grid item xs={12}>
                    <Box mt={4}>
                      <Typography variant="h6" textAlign={'center'}>
                        Coming soon
                      </Typography>
                    </Box>
                  </Grid>
                )} */}

                {courseList?.length === 0 && showSkeletons && (
                  <>
                    {[1, 2, 3, 4, 5].map((_, idx) => (
                      <Grid item xs={12} sm={6} md={4} lg={3} xl={3} key={`skeleton-${idx}`}>
                        <CardSkeleton />
                      </Grid>
                    ))}
                  </>
                )}
                {/* {courseList?.length === 0 && !showSkeletons && (
                  <Grid item xs={12}>
                    <Typography variant="body1" align="center">
                      Coming Soon
                    </Typography>
                  </Grid>
                )} */}


              </>
            )}
          </Grid>
          {satDetails && satDetails?.length > 0 ? (
            <Box
              ref={satSectionRef}
              data-category="SAT Assessment"
              sx={{ scrollMarginTop: '80px' }}
            >
              <Grid container spacing={isMobile ? 1 : 2} sx={{ width: '100%', margin: 0 }}>
                <Box
                  sx={{
                    backgroundColor: activeCategory === 'SAT Assessment' ? '#345ca1' : '#fff',
                    border: activeCategory === 'SAT Assessment' ? '1px solid #345ca1' : '1px solid #111',
                    color: activeCategory === 'SAT Assessment' ? '#fff' : '#000',
                    borderRadius: '5px',
                    width: '100%',
                    margin: '35px 10px 15px',
                    padding: '10px !important',
                    fontWeight: { md: '500', lg: '600' },
                    scrollMarginTop: '80px'
                  }}>
                  <Typography variant="h6" sx={{
                    fontSize: { xs: '13px', sm: '14px', md: '14px', lg: '16px' },
                    color: activeCategory === 'SAT Assessment' ? '#fff' : '#000'
                  }}>
                    SAT Assessment
                  </Typography>
                </Box>
                {satDetails.map((item, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} xl={3} key={`sat-${index}`}>
                    <SatCard
                      comingFrom={'B2B'}
                      data={item}
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>
          )
          :
          <Box
          ref={satSectionRef}
          data-category="SAT Assessment"
          sx={{ scrollMarginTop: '80px' }}
        >
          <Grid container spacing={isMobile ? 1 : 2} sx={{ width: '100%', margin: 0 }}>
            <Box
              sx={{
                backgroundColor: activeCategory === 'SAT Assessment' ? '#345ca1' : '#fff',
                border: activeCategory === 'SAT Assessment' ? '1px solid #345ca1' : '1px solid #111',
                color: activeCategory === 'SAT Assessment' ? '#fff' : '#000',
                borderRadius: '5px',
                width: '100%',
                margin: '35px 10px 15px',
                padding: '10px !important',
                fontWeight: { md: '500', lg: '600' },
                scrollMarginTop: '80px'
              }}>
              <Typography variant="h6" sx={{
                fontSize: { xs: '13px', sm: '14px', md: '14px', lg: '16px' },
                color: activeCategory === 'SAT Assessment' ? '#fff' : '#000'
              }}>
                SAT Assessment
              </Typography>
            </Box>
            {satDetails.length === 0 && (
            <Box sx={{ mt: 2,
              mb: 5,ml:2  }}>
              <Typography style={{textAlign:'center'}} variant="h6" fontWeight="bold" id="TitleName" >
                Sorry! No assessments are allotted to your account!
              </Typography>
            </Box>
          )}
          </Grid>
        </Box>
          
          }


{neetDetails && neetDetails?.length > 0 ? (
            <Box
              ref={neetSectionRef}
              data-category="Neet Assessment"
              sx={{ scrollMarginTop: '80px' }}
            >
              <Grid container spacing={isMobile ? 1 : 2} sx={{ width: '100%', margin: 0 }}>
                <Box
                  sx={{
                    backgroundColor: activeCategory === 'Neet Assessment' ? '#345ca1' : '#fff',
                    border: activeCategory === 'Neet Assessment' ? '1px solid #345ca1' : '1px solid #111',
                    color: activeCategory === 'Neet Assessment' ? '#fff' : '#000',
                    borderRadius: '5px',
                    width: '100%',
                    margin: '35px 10px 15px',
                    padding: '10px !important',
                    fontWeight: { md: '500', lg: '600' },
                    scrollMarginTop: '80px'
                  }}>
                  <Typography variant="h6" sx={{
                    fontSize: { xs: '13px', sm: '14px', md: '14px', lg: '16px' },
                    color: activeCategory === 'Neet Assessment' ? '#fff' : '#000'
                  }}>
                    Neet Assessment
                  </Typography>
                </Box>
                {neetDetails.map((item, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} xl={3} key={`sat-${index}`}>
                    <NeetCard
                      comingFrom={'B2B'}
                      data={item}
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>
          )
          :
          <Box
          ref={neetSectionRef}
          data-category="Neet Assessment"
          sx={{ scrollMarginTop: '80px' }}
        >
          <Grid container spacing={isMobile ? 1 : 2} sx={{ width: '100%', margin: 0 }}>
            <Box
              sx={{
                backgroundColor: activeCategory === 'Neet Assessment' ? '#345ca1' : '#fff',
                border: activeCategory === 'Neet Assessment' ? '1px solid #345ca1' : '1px solid #111',
                color: activeCategory === 'Neet Assessment' ? '#fff' : '#000',
                borderRadius: '5px',
                width: '100%',
                margin: '35px 10px 15px',
                padding: '10px !important',
                fontWeight: { md: '500', lg: '600' },
                scrollMarginTop: '80px'
              }}>
              <Typography variant="h6" sx={{
                fontSize: { xs: '13px', sm: '14px', md: '14px', lg: '16px' },
                color: activeCategory === 'Neet Assessment' ? '#fff' : '#000'
              }}>
                Neet Assessment
              </Typography>
            </Box>
            {neetDetails.length === 0 && (
            <Box sx={{ mt: 2,
              mb: 5,ml:2  }}>
              <Typography style={{textAlign:'center'}} variant="h6" fontWeight="bold" id="TitleName" >
                Sorry! No assessments are allotted to your account!
              </Typography>
            </Box>
          )}
          </Grid>
        </Box>
          
          }

       
        </Container>
        {/* {process.env.REACT_APP_ENV === 'dev' && (
          <Button
            variant="contained"
            color="primary"
            onClick={handleChatOpen}
            style={{ 
              position: 'fixed', 
              bottom: isMobile ? 10 : 20, 
              right: isMobile ? 10 : 20,
              zIndex: 1000
            }}
          >
            Chat
          </Button>
        )}
        <ChatDialog open={isChatOpen} onClose={handleChatClose} /> */}
      </Box>
    </Box>
  );
}

const useStyles = makeStyles(() => ({
  coursediv: {
    height: '10rem',
    backgroundImage: `url(${Background})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    marginTop: '4rem',
  },
  title: {
    padding: '4rem 0rem 0rem 0rem',
  },
}));
