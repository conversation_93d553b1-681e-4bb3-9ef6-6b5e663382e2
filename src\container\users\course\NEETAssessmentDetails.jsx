/* eslint-disable prefer-const */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-undef */
/* eslint-disable arrow-body-style */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/jsx-key */
/* eslint-disable react/button-has-type */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation, Link, useParams } from 'react-router-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.min.css';
import './index.css';
import DOMPurify from 'dompurify';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Button, Typography, Card, CardContent, Grid, Box, Breadcrumbs, Tabs, Tab, Paper, Table, TableHead, TableRow, TableCell, TableBody, Accordion, AccordionSummary, AccordionDetails, LinearProgress, Select, MenuItem, FormControl, InputLabel, Chip, Stack, Avatar, TableContainer, Tooltip } from '@mui/material';
import { PieChart, BarChart } from '@mui/x-charts';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { getRecommendedAssessment, getIndividualNEETAssessment } from '../../../Redux/Action'
import helper from '../../../utils/helper';
import trialAndStripeSubscriptionService from '../../../services/trialAndStripeSubscriptionService';
import { openSnackbar, ComingFrom, FromGeneralAssessmentView } from '../../../store/reducer';


const dummyData = [
    {
        questionText: "What is the powerhouse of the cell?",
        timeTaken: 32,
        chapter: "Cell Biology",
        subject: "Biology",
        options: ["Nucleus", "Mitochondria", "Ribosome", "Chloroplast"],
        correctOption: "Mitochondria",
        userOption: "Mitochondria"
    },
    {
        questionText: "The atomic number of Oxygen is:",
        timeTaken: 15,
        chapter: "Periodic Table",
        subject: "Chemistry",
        options: ["6", "7", "8", "9"],
        correctOption: "8",
        userOption: "8"
    },
    {
        questionText: "Ohm's Law is represented by:",
        timeTaken: 20,
        chapter: "Current Electricity",
        subject: "Physics",
        options: ["V=IR", "F=ma", "E=mc^2", "P=VI"],
        correctOption: "V=IR",
        userOption: "V=IR"
    }
];
const getSubjectStats = (data) => {
    const stats = {};
    data.forEach(q => {
        if (!stats[q.subject]) {
            stats[q.subject] = { total: 0, correct: 0, time: 0 };
        }
        stats[q.subject].total += 1;
        if (q.userOption === q.correctOption) stats[q.subject].correct += 1;
        stats[q.subject].time += q.timeTaken;
    });
    return stats;
}
const NeeAssessmentCourseDetails = () => {
    const location = useLocation()
    const { t } = useTranslation('translation');
    const neetAssessDetails = location.state?.details;    
    const subjectStats = getSubjectStats(dummyData);
    const classes = useStyles();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const allcourseDetails = useSelector((state) => state);
    const { userInfo } = useSelector((state) => state);
    const [currency, setCurrency] = useState(userInfo?.currencyType ? userInfo?.currencyType.code : 'USD');
    const [tabIndex, setTabIndex] = useState(0);
    const [assessmentDetails, setAssessmentDetails] = useState('');
    

    const [parseArray, setParseArray] = useState('');
    const { id } = useParams();
    const [assessmentId, setAssessmentId] = useState(null);
    // const [assessments, setAssessments] = useState([]);
    const [intfrequency, setintfrequency] = React.useState(1);
    const [viewPrice, setViewPrice] = useState(0);
    const [newValueIs, setNewValueIs] = useState(0);
    const [seriesNb, setSeriesNb] = React.useState(2);
    const [itemNb, setItemNb] = React.useState(5);
    const [skipAnimation, setSkipAnimation] = React.useState(false);
    const [value, setValue] = useState(0);

    const totalHours = 18.5;

    const subjectData = [
        { label: 'Biology', hours: 8, color: '#52c28c' },
        { label: 'Chemistry', hours: 2, color: '#fc9f06' },
        { label: 'Physics', hours: 5, color: '#437bfc' },
    ];

    const handleItemNbChange = (event, newValue) => {
        if (typeof newValue !== 'number') {
            return;
        }
        setItemNb(newValue);
    };
    const handleSeriesNbChange = (event, newValue) => {
        if (typeof newValue !== 'number') {
            return;
        }
        setSeriesNb(newValue);
    };

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };



    React.useMemo(() => {
        const filteredList = assessmentDetails?.stripe_pricing_list?.filter(data =>
            allcourseDetails?.currency === 'USD'
                ? data?.related_currency_type_lk === 2
                : data?.related_currency_type_lk === 1
        );
        let displayPrice;

        if (filteredList && filteredList?.length > 0 && allcourseDetails?.currency === 'USD') {
            displayPrice = `$${filteredList[0].value}`;
        } else if (filteredList && filteredList?.length > 0 && allcourseDetails?.currency === 'INR') {
            displayPrice = `₹${filteredList[0].value}`;
        } else if (filteredList && filteredList?.length === 0) {
            displayPrice = 'NAN'
        }
        setViewPrice(displayPrice)

    }, [allcourseDetails])


    useEffect(() => {
        if (neetAssessDetails?.id) {
            setAssessmentId(neetAssessDetails?.id);
        }
    }, [neetAssessDetails?.id]);

    useEffect(() => {
        if (assessmentId) {
            dispatch(getIndividualNEETAssessment(assessmentId, allcourseDetails?.userInfo.id))
            dispatch(getRecommendedAssessment(allcourseDetails?.userInfo.id))
            dispatch(FromGeneralAssessmentView(false))
        }
    }, [assessmentId])

    useEffect(() => {
        window.history.scrollRestoration = "manual";
        window.scrollTo(0, 0);
    }, []);

    const physicsChapters = useMemo(() => {
        if (!assessmentDetails) return [];
        return assessmentDetails.chapters.filter(chapter =>
          chapter.types?.includes("Physics")
        );
      }, [assessmentDetails]);
    
      const chemistryChapters = useMemo(() => {
        if (!assessmentDetails) return [];
        return assessmentDetails.chapters.filter(chapter =>
          chapter.types?.includes("Chemistry")
        );
      }, [assessmentDetails]);
    
      const biologyChapters = useMemo(() => {
        if (!assessmentDetails) return [];
        return assessmentDetails.chapters.filter(chapter =>
          chapter.types?.includes("Biology")
        );
      }, [assessmentDetails]);


    useEffect(() => {
        setAssessmentDetails(allcourseDetails && allcourseDetails?.GetNEETAssessmentIndividual[0])
    }, [allcourseDetails])

    const nbsp = '\u00A0'; // 4 spaces
    const assessments = [
        { id: 1, title: "Attempt 1", name: `${neetAssessDetails?.title}${nbsp}1` },
        { id: 2, title: "Attempt 2", name: `${neetAssessDetails?.title}${nbsp}2` },
        { id: 3, title: "Attempt 3", name: `${neetAssessDetails?.title}${nbsp}3` },
        { id: 4, title: "Attempt 4", name: `${neetAssessDetails?.title}${nbsp}4` },
        { id: 5, title: "Attempt 5", name: `${neetAssessDetails?.title}${nbsp}5` },
    ];

    const formatTimeTimer = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${minutes.toString().padStart(1, '0')} Minutes`;

    };
    const handleSwitchAssessment = (id) => {
        setAssessmentId(id)
        window.scrollTo({
            top: 0,
            left: 0,
            bottom: 0,
            behavior: 'smooth',
        });
    }

    const subjectColorMap = { Chemistry: '#fc9f06', Biology: '#52c28c', Physics: '#437bfc' };

    const handleNavigateDetailsMyLearning = () => {
        dispatch(ComingFrom("MyLearning"))

        if(userInfo?.role === 'AUTH_USER'){
            navigate("/app/subscribe")
            }
            else{
                navigate("/auth/subscribe")
            }
    }

    const highlightedSection = assessmentDetails?.highlighted_section;
    // console.log(highlightedSection, 'highlightedSection');

    const handleStartTest = (id) => {
        console.log(id, "id");

        // setFromIndex(id)
        // handleClickTrialButton(view?.assessment_details?.subscriptionplanid, true, view?.assessment_details?.id)
        // setComingFrom('start')
        // setAssessmentView(true)
    }


    const handleNavigate = (id) => {
        //     setFromIndex(id + 1)
        //     setComingFrom('analysis')
        //     setAssessmentView(false)       
        //     if(allassessmentdetails.userInfo.role === 'AUTH_USER'){
        //        navigate(`/app/SATAnalysis?id=${location.state.id}`, {
        //         state: {
        //             ...location.state,
        //             comingFrom: 'ComingfromAnalysis',
        //             fromindex: id + 1,
        //             allassessmentdetails
        //         }
        //     })  
        //     }
        //     else{
        // navigate(`/auth/SATAnalysis?id=${location.state.id}`, {
        //         state: {
        //             ...location.state,
        //             comingFrom: 'ComingfromAnalysis',
        //             fromindex: id + 1,
        //             allassessmentdetails
        //         }
        //     })
        // }
    }

    const handleResumeTest = async (data, index) => {
        // const lastData = data[data.length - 1];
        // let totalMarksEarned = 0;
        // let totalPossibleMarks = 0;
        // let percentage = 0;
        // const questionsList = lastData.questions_list;
        // lastData.user_result.forEach((result) => {
        //     const { multiplier, responseRecorded } = result; 
        //     if (responseRecorded === 'correct') {
        //         totalMarksEarned += multiplier;
        //     }
        //     totalPossibleMarks += multiplier;
        // });

        // percentage = totalPossibleMarks > 0 ? Math.round((totalMarksEarned / totalPossibleMarks) * 100) : 0;

        // const QuestionId = lastData?.user_result.map((item) => item.questionId);
        // const  section = lastData.user_result.length >= 2 ? 'Maths' : 'English';
        // const result = await adminServices.getSatAssessmentResume(
        //     userid,
        //     percentage,
        //     lastData?.related_assessment_id,
        //     section, 
        //     QuestionId,
        //     questionsList 
        // );

        // if (result.data) {
        //     setResumeData(result.data); 
        //     setAssessmentView(true); 
        //     setFromIndex(index + 1); 
        //     setResumeLength(data[0]?.user_result.length); 
        // }
    };


    useMemo(() => {
        if (highlightedSection !== undefined && highlightedSection !== null) {
            const cleanedStr = highlightedSection?.replace(/[{}"]/g, '').split(',')
            const formattedOutput = cleanedStr?.join('\n');

            const data = formattedOutput?.split('\n').map((text) => ({ text }))
            setParseArray(data)
        }
    }, [highlightedSection])

    const handleStartTestAssessment = () => {
        if(userInfo?.role === 'AUTH_USER'){
        navigate("/app/NeetSimulation", { state: location.state })
        }
        else{
            navigate("/auth/NeetSimulation", { state: location.state })
        }
    }

    const handleClickTrialButton = async (planId, enrollmentType, assessmentId) => {
        try {
            const res = await trialAndStripeSubscriptionService.postTrialAssessmentEnrollmentDetails(
                JSON.stringify({ planId, enrollmentType, assessmentId, authUserId: userInfo.id })
            );
            if (res.ok) {
                const data = {
                    id: location.state?.id,
                    from: location.state?.from
                }
                navigate("/auth/AssessmentOverview", { state: data })
            }
        } catch (error) {
            console.log(error);
        }
    };

    const handleTabChange = (event, newValue) => {
        setTabIndex(newValue);
    };

    const baseTabs = [
        <Tab key="overview" label="Course Overview" />,
        // <Tab key="analytics" label="Overall Analytics" />,
    ];

    const extraTabs = [
        <Tab key="bio" label="Biology" />,
        <Tab key="chem" label="Chemistry" />,
        <Tab key="phy" label="Physics" />,
    ];


    const pieSeries = [{
        data: Object.entries(subjectStats).map(([subject, stats]) => ({
            label: subject,
            value: stats.correct
        }))
    }];

    function TabPanel({ children, value, index }) {
        return (
            value === index && (
                <Box p={2}>
                    <Typography>{children}</Typography>
                </Box>
            )
        );
    }


    // Bar chart: time taken per question
    const barSeries = [{
        data: dummyData.map((q, i) => ({ x: `Q${i + 1}`, y: q.timeTaken }))
    }];
    const barXAxis = [{
        data: dummyData.map((q, i) => `Q${i + 1}`),
        scaleType: 'band'
    }];

    const barChartsParams = {
        series: [
            {
                id: 'series-1',
                data: [3, 4, 1, 6, 5],
                label: 'A',
                stack: 'total',
                highlightScope: {
                    highlight: 'item',
                },
            },
            {
                id: 'series-2',
                data: [4, 3, 1, 5, 8],
                label: 'B',
                stack: 'total',
                highlightScope: {
                    highlight: 'item',
                },
            },
            {
                id: 'series-3',
                data: [4, 2, 5, 4, 1],
                label: 'C',
                highlightScope: {
                    highlight: 'item',
                },
            },
        ],
        xAxis: [{ data: ['0', '3', '6', '9', '12'], id: 'axis1' }],
        yAxis: [{ data: ['0', '3', '6', '9', '12'], id: 'axis2' }],
        height: 350,
        margin: { left: 0 },
    };

    const highlightScope = {
        highlight: 'series',
        fade: 'global',
    };

    const recommendSubjects = [{ Subject: "Biology", Chapter: "Review the stage of cellular respiration and energy production processes.", backgroundColor: '#f0fdf4' }, { Subject: "Chemistry", Chapter: "Focus on understanding IUPAC nomenclature rules and practice naming different types of organic molecules.", backgroundColor: '#fefce8' },
    { Subject: "Physics", Chapter: "Review essential principles and tackle additional problems related to magnetic fields and their interactions.", backgroundColor: '#eff6ff' }
    ]

    const series = [
        {
            label: 'Biology',
            data: [
                1145, 1214, 975, 2266, 1768, 2341, 747, 1282, 1780, 1766, 2115, 1720, 1057,
                2000, 1716, 2253, 619, 1626, 1209, 1786,
            ],
            color: '#52c28c',
            chapters: 26
        },
        {
            label: 'Chemistry',
            data: [
                2362, 2254, 1962, 1336, 586, 1069, 2194, 1629, 2173, 2031, 1757, 862, 2446,
                910, 2430, 2300, 805, 1835, 1684, 2197,
            ],
            color: '#fc9f06',
            chapters: 18
        },
        {
            label: 'Physics',
            data: [
                2423, 2210, 764, 1879, 1478, 1373, 1891, 2171, 620, 1269, 724, 1707, 1188,
                1879, 626, 1635, 2177, 516, 1793, 1598,
            ],
            color: '#437bfc',
            chapters: 12
        },
    ].map((s) => ({ ...s, highlightScope }));

    // Add these helper functions after your existing functions
    function CustomTabPanel(props) {
        const { children, value, index, ...other } = props;

        return (
            <div
                role="tabpanel"
                hidden={value !== index}
                id={`simple-tabpanel-${index}`}
                aria-labelledby={`simple-tab-${index}`}
                {...other}
            >
                {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
            </div>
        );
    }

    function a11yProps(index) {
        return {
            id: `simple-tab-${index}`,
            'aria-controls': `simple-tabpanel-${index}`,
        };
    }



    return (

        <Box sx={{ width: '92%', margin: 'auto' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h4" fontWeight="bold">General Assessment</Typography>
                <Typography variant="body2" color="textSecondary">Assessment Overview</Typography>
            </Box>

            <Box sx={{ marginTop: '60px', }}>
                <Breadcrumbs
                    aria-label="breadcrumb"
                    sx={{
                        padding: '15px',
                        paddingLeft: '0px',
                        paddingBottom: '0px',
                        paddingTop: '0px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                    separator=">"
                >
                    {location.state?.from === 'NEET' ?
                        <Link style={{
                            cursor: 'pointer',
                            color: "#212B36 !important",
                            textDecoration: 'none',
                            "&:hover": {
                                textDecoration: 'underline',
                            }
                        }}
                            underline="hover"
                            color="#212B36"
                            // onClick={handleNavigation}
                            to="/auth/subscribe"
                        >
                            {t("NEET Assessment")}
                        </Link>
                        :
                        <button style={{
                            cursor: 'pointer',
                            textDecoration: 'none',
                            border: 'none',

                            background: 'none',
                            color: '#0000ee',
                            fontSize: '16px',
                            fontWeight: '500'
                        }}
                            // underline="hover"
                            color="#212B36"
                            // to="/auth/AssessmentCourseDetails"
                            onClick={handleNavigateDetailsMyLearning}
                        >
                            {t("My Learning")}
                        </button>}



                    <Typography color="text.primary">
                        {t("Assessment Overview")}
                    </Typography>

                </Breadcrumbs>
            </Box>

            <Box sx={{ marginTop: '20px', }}>
                <Card sx={{
                    mx: "auto", boxShadow: 3, borderRadius: '10px',
                    border: '1px solid #dfdede'
                }}>
                    <Grid sx={{ padding: '15px' }} container spacing={2}>
                        <Grid item xs={12} sm={4} sx={{ paddingRight: '15px' }}>
                            <Box
                                component="img"
                                src={neetAssessDetails?.image_name}
                                // src="https://s3-alpha-sig.figma.com/img/0656/9cc3/1739478f82289ef1c25b236830e244e3?Expires=1739145600&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=pe8zQWB3DSA4lwK-pjWJIzsvBsrp7n0t9g1~fbnrnkp26hsEgLKwq92N8CR1BNA4qwvokjUo-hlpT3HbZtrraFjNWvLwGIPsuMPCPVfygNlwWNdcpQX3mgAe9YTp2C~w7SUllRbrNK0R1TIVK9sjllitNOydMM4Z8LKmoZIR3AuGj6KwKljFN9Lz~UlpGmL2xaYcpSbVnvHIdzbou0cJCxQbZlBhz-JLElqzA~mgjq33SY5rcscFRNXPC4NSDCuYn4XOJj2Uj44LG5-L1umDc0opJRIatNBXmHDEVkHdpyeO2g9WYrouwFwQMZtTKvRb4guXbeJ~C6bk9ZL31kmY5g__"
                                alt="Assessment"
                                sx={{ width: "100%", height: "220px", borderRadius: 2 }}
                            />
                        </Grid>

                        <Grid item xs={12} sm={8} sx={{ padding: 0, paddingRight: '15px' }}>
                            <CardContent sx={{
                                padding: 0, height: '100%', paddingBottom: '0px !important', display: 'flex',
                                flexDirection: 'column'
                            }}>
                                <Typography variant="h5" fontWeight="bold">
                                    {neetAssessDetails && neetAssessDetails?.title}
                                </Typography>

                                <Typography variant="body2" color="text.secondary" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(assessmentDetails && assessmentDetails?.short_description) }} sx={{ mb: 2, flex: '1 !important' }} />
                                {/* {allcourseDetails?.SubscribedAssessment.map((item) => item.assessmentDetails[0].id === assessmentDetails?.id) && */}
                                <Typography >
                                    <span style={{
                                        textDecoration: 'line-through',
                                        marginRight: '5px',
                                        background: '#ddd',
                                        padding: '4px 8px',
                                        borderRadius: '5px'
                                    }}>
                                        Price: 
                                    </span>
                                    <strong style={{
                                        background: '#ddd',
                                        padding: '4px 8px',
                                        borderRadius: '5px'
                                    }}>
                                        Free
                                    </strong>
                                </Typography>
                                {/* // } */}
                                <Box className="cardShowEven">

                                    <Box sx={{ flex: 1 }}>

                                        <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                            Number of Questions:180
                                            {/* {neetAssessDetails && neetAssessDetails?.module_details[3]?.maxMarks} */}
                                        </Typography>
                                        <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                            Time:1hour 30mins
                                            {/* {formatTimeTimer(neetAssessDetails && neetAssessDetails?.module_details[3]?.maxMarks)} */}
                                        </Typography>

                                        {/* {allcourseDetails?.SubscribedAssessment.map((item) => item.assessmentDetails[0].id === assessmentDetails?.id) &&
                                            <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                                Enrolled
                                            </Typography>} */}

                                        {/* {neetAssessDetails?.isPaid !== true && neetAssessDetails?.is_free === true && */}
                                        <Typography className={classes.badgeShower} variant="span" sx={{ mt: 1 }}>
                                            Free
                                        </Typography>
                                        {/* } */}
                                    </Box>
                                    {/* {(allcourseDetails?.SubscribedAssessment.map((item) => item.assessmentDetails[0].id === assessmentDetails?.id)) || assessmentDetails?.isPaid !== true ?
                                        <Button
                                            variant="contained"
                                            className='buyNowbtnOne'
                                            onClick={handleStartTestAssessment}
                                            sx={{
                                                mr: 2
                                            }} >
                                            {t("Go To Test")}
                                        </Button>
                                        :
                                        <Button
                                            variant="contained"
                                            className='buyNowbtnOne'
                                            onClick={() => { handleClickTrialButton(assessmentDetails?.subscriptionplanid, true, assessmentDetails?.id) }}
                                            sx={{
                                            }} >
                                            {t("Enroll Now")}
                                        </Button>} */}

                                </Box>
                            </CardContent>
                        </Grid>
                    </Grid>
                </Card>
            </Box>
            <Box style={{ display: 'flex', justifyContent: 'space-between', marginTop: '15px', marginBottom: '0' }}>
                <Tabs
                    value={tabIndex}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    TabIndicatorProps={{
                        style: {
                            backgroundColor: '#5c7fc1',
                            height: '3px',
                        },
                    }}
                    sx={{
                        justifyContent: 'left', mb: 2,
                        '& .MuiTab-root': {
                            color: '#555',
                        },
                        '& .Mui-selected': {
                            color: '#5c7fc1 !important',
                            fontWeight: 'bold',
                        },
                    }}>
                    {baseTabs}
                    {tabIndex >= 1 && extraTabs}


                </Tabs>


                {tabIndex === 1 &&
                    <FormControl sx={{ width: 160 }}>
                        <InputLabel id="label">Attemps</InputLabel>
                        <Select label="Attemps"
                        //   onChange={handleChange}
                        >
                            <MenuItem value="attmpts1">Attemps 1</MenuItem>
                            <MenuItem value="attmpts2">Attemps 2</MenuItem>
                            <MenuItem value="attmpts3">Attemps 3</MenuItem>
                            <MenuItem value="attmpts4">Attemps 4</MenuItem>
                            <MenuItem value="attmpts5">Attemps 4</MenuItem>
                        </Select>
                    </FormControl>}
            </Box>
            {tabIndex === 0 &&
                <>
                    <Box xs={12} sm={12} md={10} className="cardShowEvens">
                        <Box xs={12} sm={12} md={12}
                            sx={{ marginRight: '10px', boxShadow: 'none', flex: 2, }} >
                            {assessmentDetails?.chapters?.length > 0 ? (
                                <Box display="flex" flexDirection="column" gap={2} mt={0}>
                                    {/* {assessmentDetails?.chapters?.map((chapter, index) => (
                                        <Box key={index} sx={{
                                            boxShadow: 2,
                                            borderRadius: '8px',
                                            padding: 4,
                                            backgroundColor: '#fff',
                                            border: '1px solid #e0e0e0',
                                        }}
                                        >
                                            <Typography variant="body1" color="textPrimary" sx={{ padding: 2 }}>
                                                {chapter}
                                            </Typography>
                                        </Box>
                                    ))} */}
                                    {/* {console.log(Array.from({ length: Math.max(physicsChapters.length, chemistryChapters.length, biologyChapters.length) }), "cheeeekkkinggg") */}
                                        <TableContainer component={Paper}
                                        sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: 1, border: '1px solid #111' }} >
                                       
                                       <Table>
                                            <thead>
                                                <tr style={{ borderBottom: '2px solid rgb(108 108 108)', padding: '5px 10px', textAlign: 'start' }}>
                                                    <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start', width: '33.33%' }}>Physics</th>
                                                    <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start', width: '33.33%' }}>Chemistry</th>
                                                    <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start', width: '33.33%' }}>Biology</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {Array.from({ length: Math.max(physicsChapters.length, chemistryChapters.length, biologyChapters.length) }).map((yes, index) => (
                                                    <tr key={index} style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                        <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                            {physicsChapters[index]?.name || physicsChapters[index]?.title || '-'}
                                                        </td>
                                                        <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                            {chemistryChapters[index]?.name || chemistryChapters[index]?.title || '-'}
                                                        </td>
                                                        <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                            {biologyChapters[index]?.name || biologyChapters[index]?.title || '-'}
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </Table>
                                        </TableContainer>
                                </Box>
                            ) : (
                                <Typography variant="body2" color="textSecondary">
                                    <TableContainer component={Paper}
                                        sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: 1, border: '1px solid #111' }} >
                                        <Table>
                                            <tr style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start' }}>
                                                <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start' }}>Subject</th>
                                                <th style={{ border: '1px solid #c1c1c1', padding: '5px 10px', textAlign: 'start' }}>Chapters</th>
                                            </tr>
                                            {series.map((s) => (

                                                <tr style={{ border: '1px solid #c1c1c1', padding: '10px' }}>
                                                    <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>{s.label}</td>
                                                    <td style={{ border: '1px solid #c1c1c1', padding: '10px' }}>{s.chapters}</td>
                                                </tr>
                                            ))}
                                        </Table>
                                    </TableContainer>
                                </Typography>
                            )}

                        </Box>

                      

                    </Box>
                    <Box xs={12} sm={12} md={12}>
                        {/* <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, mt: 2 }}> Highlights </Typography> */}
                        <Box sx={{ width: '100%', typography: 'body1', marginTop: '24px' }}>
                            <Card sx={{
                                mx: "auto", boxShadow: 3, borderRadius: '10px',
                                border: '1px solid #dfdede'
                            }}>
                                <Box className="121" sx={{ width: '100%' }}>
                                    <Box className="122" sx={{ borderBottom: 1, borderColor: 'divider', height: "40px" }}>
                                        <Tabs value={value} onChange={handleChange} className="tabBasic" aria-label="basic tabs example"
                                            sx={{
                                                "& .MuiTabs-indicator": {
                                                    backgroundColor: "#ff0004",
                                                },
                                                "& .MuiTab-root": {
                                                    color: "#424242 !important",
                                                },
                                                "& .Mui-selected": {
                                                    color: "#424242 !important",
                                                },
                                            }}
                                        >
                                            <Tab label={"All"} {...a11yProps(0)} />
                                            <Tab label={"Not Started"} {...a11yProps(1)} />
                                            <Tab label={"Completed"} {...a11yProps(2)} />
                                        </Tabs>
                                    </Box>
                                    {/* <CustomTabPanel value={value} index={0} style={{ padding: '10px 10px 20px' }}>
                                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                            {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                                let borderColor = '';
                                                let Color = '';

                                                if (assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts[index]?.records &&
                                                    assessmentDetails?.attempts[index]?.records?.filter(item => item.response_recorded === "correct" && item.response_recorded !== "incorrect")?.length === assessmentDetails[index]?.records?.length) {
                                                    borderColor = '#40C057'
                                                    Color = '#EAFBEA'
                                                }
                                                else if (assessmentDetails && assessmentDetails?.attempts?.length > 0 && assessmentDetails?.attempts[index]?.records &&
                                                    assessmentDetails?.attempts[index]?.records?.filter(item => item.response_recorded === "incorrect" || "correct")?.length === assessmentDetails?.attempts[index]?.records?.length) {
                                                    borderColor = '#FA5252'
                                                    Color = '#FCEBEA'
                                                }
                                                else {
                                                    borderColor = '#dddada';
                                                    Color = '#f5f5f5';

                                                }
                                                const firstEmptyAssessmentIndex = assessments.findIndex((_, i) => {
                                                    const results = assessmentDetails?.attempts?.[i]?.records || [];
                                                    return results.length === 0;
                                                });

                                                return (
                                                    <Box
                                                        key={assessment.id}
                                                        sx={{

                                                            borderRadius: "4px",
                                                            width: '99%',
                                                            padding: '1px 0px 0px !important',
                                                            margin: '5px auto 0px !important',
                                                            minHeight: '63px',
                                                            // backgroundColor: assessment.color,
                                                            backgroundColor: Color,
                                                            borderBottom: `3px solid ${borderColor}`,
                                                            flexWrap: "wrap"
                                                        }}>
                                                        <Box sx={{
                                                            // borderBottom: `3px solid ${borderColor}`,

                                                            display: "flex",
                                                            justifyContent: "space-between", padding: '8px 15px !important', alignItems: "center",
                                                        }}>
                                                            <Box sx={{ flex: 1 }}>
                                                                <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                    {assessment?.title}: <span style={{ fontWeight: "normal" }}>{assessment?.name}</span>
                                                                </Box>
                                                               
                                                            </Box>

                                                            {
                                                                // assessmentDetails?.attempts &&
                                                                //     assessmentDetails?.attempts[index]?.records &&
                                                                //     assessmentDetails?.attempts[index]?.records?.length > 0 ? (
                                                                //     <>
                                                                //         {assessmentDetails?.attempts[index]?.records[0]?.user_result?.length >= 4 ?
                                                                //             <Button
                                                                //                 variant="outlined"
                                                                //                 onClick={() => handleNavigate(index)}
                                                                //                 sx={{
                                                                //                     borderColor: "#3B82F6",
                                                                //                     borderRadius: '4px !important',
                                                                //                     color: "#3B82F6",
                                                                //                     padding: '4px 18px !important',
                                                                //                     "&:hover": {
                                                                //                         backgroundColor: "#3B82F6",
                                                                //                         color: "#fff",
                                                                //                         borderColor: '#3B82F6',
                                                                //                     },
                                                                //                 }}
                                                                //             >
                                                                //                 {"Analysis"}
                                                                //             </Button>
                                                                //             :
                                                                //             <Button
                                                                //                 variant="outlined"
                                                                //                 onClick={() => handleResumeTest(assessmentDetails?.attempts[index]?.records, index)}
                                                                //                 sx={{
                                                                //                     borderColor: "#3B82F6",
                                                                //                     borderRadius: '4px !important',
                                                                //                     color: "#3B82F6",
                                                                //                     padding: '4px 18px !important',
                                                                //                     "&:hover": {
                                                                //                         backgroundColor: "#3B82F6",
                                                                //                         color: "#fff",
                                                                //                         borderColor: '#3B82F6',
                                                                //                     },
                                                                //                 }}
                                                                //             >
                                                                //                 {"Resume Test"}
                                                                //             </Button>}
                                                                //     </>
                                                                // )
                                                                    // : (
                                                                        <>
                                                                            <Tooltip title={firstEmptyAssessmentIndex !== index ||
                                                                                (index > 0 &&
                                                                                    assessmentDetails?.attempts[index - 1]?.records[0]?.user_result?.length < 4) ?
                                                                                "To unlock this test, please attend the previous test!" :
                                                                                "Click to start the test"
                                                                            }>
                                                                                <span>
                                                                                    <Button
                                                                                        variant="outlined"
                                                                                        // onClick={() => handleStartTest(assessment?.id)}
                                                                                        onClick={() => handleStartTestAssessment(assessment?.id)}
                                                                                        disabled={
                                                                                            firstEmptyAssessmentIndex !== index ||
                                                                                            (index > 0 &&
                                                                                                assessmentDetails?.attempts[index - 1]?.records[0]?.user_result?.length < 4)
                                                                                        }
                                                                                        sx={{
                                                                                            borderColor: "#3B82F6",
                                                                                            borderRadius: '4px !important',
                                                                                            color: "#3B82F6",
                                                                                            padding: '4px 18px !important',
                                                                                            "&:hover": {
                                                                                                backgroundColor: "#3B82F6",
                                                                                                color: "#fff",
                                                                                                borderColor: '#3B82F6',
                                                                                            },
                                                                                        }}
                                                                                    >
                                                                                        {"Start"}
                                                                                    </Button>
                                                                                </span>
                                                                            </Tooltip>
                                                                        </>
                                                                    // )
                                                            }



                                                        </Box>
                                                    </Box>
                                                )


                                            })}

                                        </Box>
                                    </CustomTabPanel> */}
                                    <CustomTabPanel value={value} index={0} style={{ padding: '10px 10px 20px' }}>
                                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                            {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                                let borderColor = '';
                                                let Color = '';
                                                let buttonToShow = 'start'; // 'start', 'completed', or 'resume'

                                                // Get the user_result for this assessment index
                                                const userResult = assessmentDetails?.user_result?.[index];

                                                // Check if user has attempted this assessment
                                                const hasAttempted = userResult && userResult.user_result?.length > 0;

                                                // Determine colors based on responses
                                                if (hasAttempted) {
                                                    const allCorrect = userResult.user_result.every(
                                                        item => item.response_recorded === "correct" && item.response_recorded !== null
                                                    );

                                                    if (allCorrect) {
                                                        borderColor = '#40C057';
                                                        Color = '#EAFBEA';
                                                    } else {
                                                        borderColor = '#FA5252';
                                                        Color = '#FCEBEA';
                                                    }
                                                } else {
                                                    borderColor = '#dddada';
                                                    Color = '#f5f5f5';
                                                }

                                                // Determine which button to show
                                                if (assessmentDetails?.user_result?.length > index) {
                                                    buttonToShow = 'completed';
                                                } else if (index > 0 && !assessmentDetails?.user_result?.[index - 1]) {
                                                    // Previous assessment not completed
                                                    buttonToShow = 'locked';
                                                } else {
                                                    buttonToShow = 'start';
                                                }

                                                return (
                                                    <Box
                                                        key={assessment.id}
                                                        sx={{
                                                            borderRadius: "4px",
                                                            width: '99%',
                                                            padding: '1px 0px 0px !important',
                                                            margin: '5px auto 0px !important',
                                                            minHeight: '63px',
                                                            backgroundColor: Color,
                                                            borderBottom: `3px solid ${borderColor}`,
                                                            flexWrap: "wrap"
                                                        }}>
                                                        <Box sx={{
                                                            display: "flex",
                                                            justifyContent: "space-between",
                                                            padding: '8px 15px !important',
                                                            alignItems: "center",
                                                        }}>
                                                            <Box sx={{ flex: 1 }}>
                                                                <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                    {assessment?.title}: <span style={{ fontWeight: "normal" }}>{assessment?.name}</span>
                                                                </Box>
                                                            </Box>

                                                            <Tooltip title={
                                                                buttonToShow === 'locked' ?
                                                                    "To unlock this test, please attend the previous test!" :
                                                                    buttonToShow === 'completed' ?
                                                                        "" :
                                                                        "Click to start the test"
                                                            }>
                                                                <span>
                                                                    {buttonToShow === 'completed' ? (
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleNavigate(index)}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                cursor: "default",
                                                                                borderRadius: '4px !important',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px !important',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                            }}
                                                                        >
                                                                            {"Completed"}
                                                                        </Button>
                                                                    ) : (
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleStartTestAssessment(assessment?.id)}
                                                                            disabled={buttonToShow === 'locked'}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                borderRadius: '4px !important',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px !important',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                                opacity: buttonToShow === 'locked' ? 0.7 : 1,
                                                                            }}
                                                                        >
                                                                            {buttonToShow === 'locked' ? "Locked" : "Start"}
                                                                        </Button>
                                                                    )}
                                                                </span>
                                                            </Tooltip>
                                                        </Box>
                                                    </Box>
                                                );
                                            })}
                                        </Box>
                                    </CustomTabPanel>

                                    <CustomTabPanel value={value} index={1} style={{ padding: '10px 10px 20px' }}>
                                        <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                            {assessments && assessments.length > 0 && assessments
                                                .filter((_, index) => {
                                                    // Only show assessments where user_result[index] doesn't exist or is empty
                                                    return !assessmentDetails?.user_result?.[index] ||
                                                        assessmentDetails.user_result[index].user_result?.length === 0;
                                                })
                                                .map((assessment, filteredIndex) => {
                                                    // Get the original index in the assessments array
                                                    const originalIndex = assessments.findIndex(a => a.id === assessment.id);

                                                    // Check if previous assessment was completed
                                                    const isPreviousCompleted = originalIndex > 0 &&
                                                        assessmentDetails?.user_result?.[originalIndex - 1]?.user_result?.length > 0;

                                                    // Find first unattempted assessment
                                                    const firstUnattemptedIndex = assessments.findIndex((_, i) => {
                                                        return !assessmentDetails?.user_result?.[i] ||
                                                            assessmentDetails.user_result[i].user_result?.length === 0;
                                                    });

                                                    const isLocked = originalIndex > firstUnattemptedIndex ||
                                                        (originalIndex > 0 && !isPreviousCompleted);

                                                    return (
                                                        <Box
                                                            key={assessment.id}
                                                            sx={{
                                                                borderRadius: "4px",
                                                                width: '99%',
                                                                padding: '1px 0px 0px !important',
                                                                margin: '5px auto 15px !important',
                                                                backgroundColor: '#f5f5f5',
                                                                borderBottom: `3px solid #dddada`,
                                                                flexWrap: "wrap",
                                                            }}
                                                        >
                                                            <Box
                                                                sx={{
                                                                    display: "flex",
                                                                    justifyContent: "space-between",
                                                                    padding: '8px 15px !important',
                                                                    alignItems: "center",
                                                                }}
                                                            >
                                                                <Box sx={{ flex: 1 }}>
                                                                    <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                        {assessment.title}: <span style={{ fontWeight: "normal" }}>{assessment.name}</span>
                                                                    </Box>
                                                                </Box>

                                                                <Tooltip title={
                                                                    isLocked
                                                                        ? "To unlock this test, please attend the previous test!"
                                                                        : "Click to start the test"
                                                                }>
                                                                    <span>
                                                                        <Button
                                                                            variant="outlined"
                                                                            onClick={() => handleStartTestAssessment(assessment?.id)}
                                                                            disabled={isLocked}
                                                                            sx={{
                                                                                borderColor: "#3B82F6",
                                                                                borderRadius: '4px',
                                                                                color: "#3B82F6",
                                                                                padding: '4px 18px',
                                                                                "&:hover": {
                                                                                    backgroundColor: "#3B82F6",
                                                                                    color: "#fff",
                                                                                    borderColor: '#3B82F6',
                                                                                },
                                                                                opacity: isLocked ? 0.7 : 1,
                                                                            }}
                                                                        >
                                                                            {isLocked ? "Locked" : "Start"}
                                                                        </Button>
                                                                    </span>
                                                                </Tooltip>
                                                            </Box>
                                                        </Box>
                                                    );
                                                })}
                                        </Box>
                                    </CustomTabPanel>

                                    <CustomTabPanel value={value} index={2} style={{ padding: '10px 10px 20px' }}>
                                        {assessmentDetails?.user_result?.length > 0 ? (
                                            <Box sx={{ display: "flex", flexDirection: "column", gap: 2, padding: '10px 10px 20px' }}>
                                                {assessments && assessments?.length > 0 && assessments?.map((assessment, index) => {
                                                    const userResult = assessmentDetails?.user_result?.[index];
                                                    const hasCompleted = userResult && userResult.user_result?.length > 0;

                                                    if (!hasCompleted) return null;
                                                    let borderColor = '';
                                                    let Color = '';

                                                    const allCorrect = userResult.user_result.every(
                                                        item => item.response_recorded === "correct" && item.response_recorded !== null
                                                    );

                                                    if (allCorrect) {
                                                        borderColor = '#40C057';
                                                        Color = '#EAFBEA';
                                                    } else {
                                                        borderColor = '#FA5252';
                                                        Color = '#FCEBEA';
                                                    }

                                                    return (
                                                        <Box
                                                            key={assessment.id}
                                                            sx={{
                                                                borderRadius: "4px",
                                                                width: '99%',
                                                                minHeight: '63px',
                                                                padding: '1px 0px 0px !important',
                                                                margin: '5px auto 15px !important',
                                                                backgroundColor: Color,
                                                                borderBottom: `3px solid ${borderColor}`,
                                                                flexWrap: "wrap"
                                                            }}
                                                        >
                                                            <Box sx={{
                                                                display: "flex",
                                                                justifyContent: "space-between",
                                                                padding: '8px 15px !important',
                                                                alignItems: "center",
                                                            }}>
                                                                <Box sx={{ flex: 1 }}>
                                                                    <Box sx={{ fontWeight: "bold", fontSize: "16px" }}>
                                                                        {assessment.title}: <span style={{ fontWeight: "normal" }}>{assessment.name}</span>
                                                                    </Box>
                                                                </Box>

                                                                <Button
                                                                    variant="outlined"
                                                                    onClick={() => handleNavigate(index)}
                                                                    sx={{
                                                                        borderColor: "#3B82F6",
                                                                        borderRadius: '4px !important',
                                                                        color: "#3B82F6",
                                                                        padding: '4px 18px !important',
                                                                        "&:hover": {
                                                                            backgroundColor: "#3B82F6",
                                                                            color: "#fff",
                                                                            borderColor: '#3B82F6'
                                                                        },
                                                                    }}
                                                                >
                                                                    {"Completed"}
                                                                </Button>
                                                            </Box>
                                                        </Box>
                                                    );
                                                })}
                                            </Box>
                                        ) : (
                                            <Box style={{ height: '200px' }}>
                                                <Typography style={{
                                                    textAlign: 'center',
                                                    fontWeight: '500',
                                                    paddingTop: '20px'
                                                }}>
                                                    No assessments completed
                                                </Typography>
                                            </Box>
                                        )}
                                    </CustomTabPanel>
                                </Box>
                            </Card>
                        </Box>
                    </Box>
                </>
            }
            {tabIndex === 1 &&

                <Box sx={{ p: 3 }}>
                    <Typography variant="h4" gutterBottom>NEET Exam Analytics</Typography>

                    <Grid container spacing={2} sx={{ mb: 4 }}>
                        <Grid item xs={12} md={7}>
                            <Paper elevation={3}
                                sx={{
                                    padding: '16px !important', borderRadius: '12px', backgroundColor: '#fff', height: '282px',
                                    display: 'flex', flexDirection: 'column', justifyContent: 'space-between'
                                }}>
                                <Typography variant="h6" sx={{ marginBottom: '10px' }}>Time Spent</Typography>

                                <Stack direction="row" alignItems="center" spacing={1} mb={3}>
                                    <Avatar sx={{ bgcolor: '#737cc8', width: 32, height: 32 }}>
                                        {/* <AccessTimeIcon fontSize="small" /> */}
                                        <AccessTimeIcon />
                                    </Avatar>
                                    <Typography variant="h6" fontWeight="bold">
                                        {totalHours}hrs
                                    </Typography>
                                </Stack>
                                <Box>
                                    {subjectData.map((subject) => {
                                        const percentage = (subject.hours / totalHours) * 100;

                                        return (
                                            <Box key={subject.label} mb={2}>
                                                <Stack direction="row" justifyContent="space-between" mb={0}>
                                                    <Typography variant="body2" fontSize="15px" fontWeight={500}>
                                                        {subject.label}
                                                    </Typography>
                                                    <Typography variant="body2" fontWeight={500}>
                                                        {subject.hours}hrs
                                                    </Typography>
                                                </Stack>
                                                <LinearProgress
                                                    variant="determinate"
                                                    value={percentage}
                                                    sx={{
                                                        height: 10,
                                                        borderRadius: 5,
                                                        backgroundColor: '#eee',
                                                        '& .MuiLinearProgress-bar': {
                                                            backgroundColor: subject.color,
                                                        },
                                                    }}
                                                />
                                            </Box>
                                        );
                                    })}
                                </Box>
                            </Paper>
                        </Grid>
                        <Grid item xs={12} md={5}>
                            <Paper elevation={3} sx={{ p: 2 }}>
                                <Typography variant="h6">Overall Accuracy</Typography>
                                <PieChart series={[{
                                    data: Object.entries(subjectStats).map(([subject, stats]) => ({
                                        label: subject, value: stats.correct, color: subjectColorMap[subject]
                                    })), innerRadius: 42, outerRadius: 100, paddingAngle: -1, cornerRadius: 0,
                                    startAngle: -45, cx: 100,
                                }]} height={220} />
                            </Paper>
                        </Grid>

                        {/* <Grid item xs={12} md={7}> */}
                        {/* <Paper elevation={3} sx={{ p: 2 }}> */}
                        {/* <Typography variant="h6">Time Spend</Typography> */}
                        {/* <BarChart
                                    height={280}
                                    series={series
                                        .slice(0, 3)
                                        .map((s) => ({ ...s, data: s.data.slice(0, itemNb) }))}
                                    skipAnimation={skipAnimation}
                                    margin={{ left: 2, right: 1 }}
                                /> */}

                        {/* <Slider
                                    value={itemNb}
                                    onChange={handleItemNbChange}
                                    valueLabelDisplay="auto"
                                    min={1}
                                    max={20}
                                    aria-labelledby="input-item-number"
                                /> */}

                        {/* </Paper> */}
                        {/* </Grid> */}
                    </Grid>


                    {/* <Typography variant="h6" gutterBottom>Subject-wise Summary</Typography>
                    <Grid container spacing={2} sx={{ mb: 4 }}>
                        {Object.entries(subjectStats).map(([subject, stats]) => (
                            <Grid item xs={12} sm={4} key={subject}>
                                <Paper elevation={3} sx={{ p: 2 }}>
                                    <Typography variant="h6">{subject}</Typography>
                                    <Typography>Total Questions: {stats.total}</Typography>
                                    <Typography>Correct: {stats.correct}</Typography>
                                    <Typography>Accuracy: {((stats.correct / stats.total) * 100).toFixed(0)}%</Typography>
                                    <Typography>Total Time: {stats.time}s</Typography>
                                    <LinearProgress
                                        variant="determinate"
                                        value={(stats.correct / stats.total) * 100}
                                        sx={{ mt: 1 }}
                                    />
                                </Paper>
                            </Grid>
                        ))}
                    </Grid> */}

                    <Box id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Top Chapter</Typography>
                        <Table sx={{ mb: 4 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell>Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time (sec)</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {dummyData.map((q, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{q.chapter}</TableCell>
                                        <TableCell>
                                            <Chip label={q.subject} size="small" sx={{
                                                backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                borderRadius: '15px', fontWeight: '500'
                                            }} /> </TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? 1 : 0}</TableCell>
                                        <TableCell align="center">1</TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? '100%' : '0%'}</TableCell>
                                        <TableCell align="center">{q.timeTaken}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </Box>

                    <Box id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Weak Chapter</Typography>
                        <Table sx={{ mb: 4 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell>Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time (sec)</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {dummyData.map((q, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{q.chapter}</TableCell>
                                        <TableCell>
                                            <Chip label={q.subject} size="small" sx={{
                                                backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                borderRadius: '15px', fontWeight: '500'
                                            }} /> </TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? 1 : 0}</TableCell>
                                        <TableCell align="center">1</TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? '100%' : '0%'}</TableCell>
                                        <TableCell align="center">{q.timeTaken}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </Box>

                    <Box sx={{
                        border: '1px solid #111', background: '#fff', borderRadius: '15px',
                        padding: '20px 15px !important', marginBottom: '30px',
                    }}>
                        <Typography variant="h6" gutterBottom>
                            Recommended Focus Areas
                        </Typography>

                        {recommendSubjects && recommendSubjects.length > 0 ? (
                            recommendSubjects.map((data, index) => (
                                <Box sx={{ backgroundColor: data.backgroundColor, padding: '15px 12px !important', color: "#ddd", borderRadius: '10px', marginBottom: '18px' }}>
                                    <Typography key={index} variant="body2" color="#000" fontSize="17px" fontWeight="500">
                                        {data.Subject}
                                    </Typography>
                                    <Typography variant="body2" color="#000" fontSize="14px" fontWeight="400">
                                        {data.Chapter}
                                    </Typography>
                                </Box>
                            ))
                        ) : (
                            <Typography variant="body2" color="textSecondary" textAlign="center">
                                {t('No recommended focus area.')}
                            </Typography>
                        )}
                    </Box>

                    <Typography variant="h6" gutterBottom>Mistake Review</Typography>
                    {dummyData.filter(q => q.userOption !== q.correctOption).map((q, i) => (
                        <Accordion key={i} sx={{ mb: 2 }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Typography>{q.questionText.slice(0, 60)}...</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Typography><strong>Subject:</strong> {q.subject}</Typography>
                                <Typography><strong>Chapter:</strong> {q.chapter}</Typography>
                                <Typography><strong>Your Answer:</strong> {q.userOption}</Typography>
                                <Typography><strong>Correct Answer:</strong> {q.correctOption}</Typography>
                                <Typography><strong>Time Taken:</strong> {q.timeTaken}s</Typography>
                                <Typography><strong>Options:</strong> {q.options.join(', ')}</Typography>
                            </AccordionDetails>
                        </Accordion>
                    ))}
                </Box>
            }

            {tabIndex === 2 &&
                <>
                    <Paper elevation={3} sx={{ p: 2 }}>
                        <Typography variant="h6">Time Spend</Typography>
                        <BarChart
                            height={280}
                            series={series
                                .slice(0, 1)
                                .map((s) => ({ ...s, data: s.data.slice(0, itemNb) }))}
                            skipAnimation={skipAnimation}
                            margin={{ left: 2, right: 1 }}
                        />
                    </Paper>
                    <Box sx={{ mt: 3 }} id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Top Chapter</Typography>
                        <Table sx={{ mb: 4 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell>Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time (sec)</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {dummyData.map((q, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{q.chapter}</TableCell>
                                        <TableCell>
                                            <Chip label={q.subject} size="small" sx={{
                                                backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                borderRadius: '15px', fontWeight: '500'
                                            }} /> </TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? 1 : 0}</TableCell>
                                        <TableCell align="center">1</TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? '100%' : '0%'}</TableCell>
                                        <TableCell align="center">{q.timeTaken}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </Box>
                    <Box sx={{ mt: 3 }}>
                        <Box sx={{
                            border: '1px solid #111', background: '#fff', borderRadius: '15px',
                            padding: '20px 15px !important', marginBottom: '30px',
                        }}>
                            <Typography variant="h6" gutterBottom>
                                Recommended Focus Areas
                            </Typography>

                            {recommendSubjects && recommendSubjects.length > 0 ? (
                                recommendSubjects.map((data, index) => (
                                    <Box sx={{ backgroundColor: data.backgroundColor, padding: '15px 12px !important', color: "#ddd", borderRadius: '10px', marginBottom: '18px' }}>
                                        <Typography key={index} variant="body2" color="#000" fontSize="17px" fontWeight="500">
                                            {data.Subject}
                                        </Typography>
                                        <Typography variant="body2" color="#000" fontSize="14px" fontWeight="400">
                                            {data.Chapter}
                                        </Typography>
                                    </Box>
                                ))
                            ) : (
                                <Typography variant="body2" color="textSecondary" textAlign="center">
                                    {t('No recommended focus area.')}
                                </Typography>
                            )}
                        </Box>
                    </Box>
                </>
            }
            {tabIndex === 3 &&
                <>
                    <Paper elevation={3} sx={{ p: 2 }}>
                        <Typography variant="h6">Time Spend</Typography>
                        <BarChart
                            height={280}
                            series={series
                                .slice(1, 2)
                                .map((s) => ({ ...s, data: s.data.slice(0, itemNb) }))}
                            skipAnimation={skipAnimation}
                            margin={{ left: 2, right: 1 }}
                        />
                    </Paper>
                    <Box sx={{ mt: 3 }} id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Top Chapter</Typography>
                        <Table sx={{ mb: 4 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell>Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time (sec)</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {dummyData.map((q, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{q.chapter}</TableCell>
                                        <TableCell>
                                            <Chip label={q.subject} size="small" sx={{
                                                backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                borderRadius: '15px', fontWeight: '500'
                                            }} /> </TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? 1 : 0}</TableCell>
                                        <TableCell align="center">1</TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? '100%' : '0%'}</TableCell>
                                        <TableCell align="center">{q.timeTaken}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </Box>
                    <Box sx={{ mt: 3 }}>
                        <Box sx={{
                            border: '1px solid #111', background: '#fff', borderRadius: '15px',
                            padding: '20px 15px !important', marginBottom: '30px',
                        }}>
                            <Typography variant="h6" gutterBottom>
                                Recommended Focus Areas
                            </Typography>

                            {recommendSubjects && recommendSubjects.length > 0 ? (
                                recommendSubjects.map((data, index) => (
                                    <Box sx={{ backgroundColor: data.backgroundColor, padding: '15px 12px !important', color: "#ddd", borderRadius: '10px', marginBottom: '18px' }}>
                                        <Typography key={index} variant="body2" color="#000" fontSize="17px" fontWeight="500">
                                            {data.Subject}
                                        </Typography>
                                        <Typography variant="body2" color="#000" fontSize="14px" fontWeight="400">
                                            {data.Chapter}
                                        </Typography>
                                    </Box>
                                ))
                            ) : (
                                <Typography variant="body2" color="textSecondary" textAlign="center">
                                    {t('No recommended focus area.')}
                                </Typography>
                            )}
                        </Box>
                    </Box>
                </>
            }
            {tabIndex === 4 &&
                <>
                    <Paper elevation={3} sx={{ p: 2 }}>
                        <Typography variant="h6">Time Spend</Typography>
                        <BarChart
                            height={280}
                            series={series
                                .slice(2, 3)
                                .map((s) => ({ ...s, data: s.data.slice(0, itemNb) }))}
                            skipAnimation={skipAnimation}
                            margin={{ left: 2, right: 1 }}
                        />
                    </Paper>
                    <Box sx={{ mt: 3 }} id="commonShadow">
                        <Typography variant="h6" gutterBottom>Performace - Top Chapter</Typography>
                        <Table sx={{ mb: 4 }}>
                            <TableHead>
                                <TableRow style={{ backgroundColor: '#ccdbff', borderRadius: '10px' }}>
                                    <TableCell>Chapter Name</TableCell>
                                    <TableCell>Subject</TableCell>
                                    <TableCell align="center">Correct</TableCell>
                                    <TableCell align="center">Total Answered</TableCell>
                                    <TableCell align="center">Accuracy</TableCell>
                                    <TableCell align="center">Avg Time (sec)</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {dummyData.map((q, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{q.chapter}</TableCell>
                                        <TableCell>
                                            <Chip label={q.subject} size="small" sx={{
                                                backgroundColor: subjectColorMap[q.subject], color: "#fff",
                                                borderRadius: '15px', fontWeight: '500'
                                            }} /> </TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? 1 : 0}</TableCell>
                                        <TableCell align="center">1</TableCell>
                                        <TableCell align="center">{q.userOption === q.correctOption ? '100%' : '0%'}</TableCell>
                                        <TableCell align="center">{q.timeTaken}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </Box>
                    <Box sx={{ mt: 3 }}>
                        <Box sx={{
                            border: '1px solid #111', background: '#fff', borderRadius: '15px',
                            padding: '20px 15px !important', marginBottom: '30px',
                        }}>
                            <Typography variant="h6" gutterBottom>
                                Recommended Focus Areas
                            </Typography>

                            {recommendSubjects && recommendSubjects.length > 0 ? (
                                recommendSubjects.map((data, index) => (
                                    <Box sx={{ backgroundColor: data.backgroundColor, padding: '15px 12px !important', color: "#ddd", borderRadius: '10px', marginBottom: '18px' }}>
                                        <Typography key={index} variant="body2" color="#000" fontSize="17px" fontWeight="500">
                                            {data.Subject}
                                        </Typography>
                                        <Typography variant="body2" color="#000" fontSize="14px" fontWeight="400">
                                            {data.Chapter}
                                        </Typography>
                                    </Box>
                                ))
                            ) : (
                                <Typography variant="body2" color="textSecondary" textAlign="center">
                                    {t('No recommended focus area.')}
                                </Typography>
                            )}
                        </Box>
                    </Box>
                </>
            }



            {/* {assessments && assessments?.length > 0 && tabIndex === 0 &&
                <Card sx={{
                    mx: "auto", boxShadow: 3, borderRadius: '10px', marginBottom: '10px',
                    border: '1px solid #dfdede', marginTop: 4, padding: "15px"
                }}>
                    <Typography variant="h6" fontWeight="bold" style={{ marginBottom: '15px' }}>{t("Related Assessments")}</Typography>

                    <Swiper
                        spaceBetween={10}
                        slidesPerView={4}
                        breakpoints={{
                            1200: {
                                slidesPerView: 4,
                            },
                            1000: {
                                slidesPerView: 4,
                            },
                            600: {
                                slidesPerView: 3,
                            },
                            450: {
                                slidesPerView: 2,
                            },
                            0: {
                                slidesPerView: 1,
                            },
                        }}
                        navigation={assessments?.length > 4}
                        pagination={{
                            el: assessments.length > 4 ? `.${classes.swiperPagination}` : "",
                            clickable: assessments.length > 4,
                            bulletClass: 'custom-bullet',
                            bulletActiveClass: 'custom-bullet-active'
                        }}
                        keyboard
                        className="mySwiper"
                    >
                        {assessments && assessments?.length > 0 && assessments.map((assessment) => (
                            <SwiperSlide key={assessment.id} >
                                <Box item xs={12} key={assessment.id}>
                                    <Card sx={{ padding: 0, border: '1px solid #e7e7e7 !important' }}>
                                        <img
                                            style={{
                                                borderRadius: '15px',
                                                height: '160px',
                                                width: '100%',
                                                objectFit: 'cover',
                                                minHeight: '160px',
                                                maxHeight: '160px',
                                                borderTopRightRadius: '0',
                                                borderTopLeftRadius: '0'

                                            }}
                                            src={assessment.image_name}
                                            alt="Related Assessment"
                                            width="100%"
                                        />
                                        <CardContent style={{ padding: '10px 15px 10px', display: 'flex', flexDirection: 'column', height: '146px' }}>
                                            <Typography className='titleAssessment' style={{ flex: "4", fontWeight: '600', marginBottom: '5px', lineHeight: '1.1' }} variant="h6">
                                                {assessment.title}
                                            </Typography>
                                            <Typography
                                                variant="body2"
                                                style={{ lineHeight: '1.2', color: '#a3a0a0' }}
                                                dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(assessment.short_description) }}
                                            />
                                            <Box style={{ display: 'flex', justifyContent: 'end', marginTop: '8px' }}>




                                                {assessmentId !== assessment?.id && <Button
                                                    onClick={() => handleSwitchAssessment(assessment.id)}
                                                    variant="contained"
                                                    sx={{
                                                        background: '#00B673',
                                                        borderRadius: '4px', lineHeight: '1.1',
                                                        color: '#fff', marginRight: '8px', float: 'right',
                                                    }}>
                                                    {t("View More")}
                                                </Button>}
                                            </Box>
                                        </CardContent>
                                    </Card>
                                </Box>
                            </SwiperSlide>
                        ))}
                    </Swiper>

                </Card>}
 */}


        </Box>



    );

};

export default NeeAssessmentCourseDetails;

const useStyles = makeStyles((theme) => ({
    badgeShower: {
        background: '#ddd',
        padding: '4px 8px',
        borderRadius: '5px',
        borderCollapse: 'collapse',
        marginRight: '15px',
        display: 'inline-flex'
    },
    tableRow: {
        border: '1px solid #ddd',
        padding: '10px',
        textAlign: 'left',
        borderCollapse: 'collapse',
        textTransform: 'capitalize'
    },
    assessmentContainer: {
        width: '100%',
        maxWidth: '600px',
        background: 'white',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
        display: 'flex',
    },
}));
