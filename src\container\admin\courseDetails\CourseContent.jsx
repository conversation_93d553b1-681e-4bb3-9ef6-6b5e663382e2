/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable react/prop-types */
/* eslint-disable arrow-body-style */
/* eslint-disable prefer-destructuring */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { Breadcrumbs, Box, Button, Fade, Grid, IconButton, Modal, Paper, Typography, Tooltip } from '@mui/material';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import AddIcon from '@mui/icons-material/Add';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { makeStyles } from '@mui/styles';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import { useSelector } from 'react-redux';
import 'react-html5video/dist/styles.css';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation, Link as RouterLink } from 'react-router-dom';
import DeleteIcon from '@mui/icons-material/Delete';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import MoveIcon from '@mui/icons-material/TrendingFlatSharp';
import Cookies from 'js-cookie';
import axios from 'axios';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Popover from '@mui/material/Popover';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AssessmentIcon from '@mui/icons-material/Assessment';
import LoadingButton from '@mui/lab/LoadingButton';
import MenuItem from '@mui/material/MenuItem';
import swal from 'sweetalert';
import CloseIcon from '@mui/icons-material/Close';
import TrialModal from '../../../components/modal/TrialModal';
import AddSubModuleForm from '../../../components/Forms/CreateModule';
import DeleteAlert from '../../../components/modal/DeleteModal';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';
import adminServices from '../../../services/adminServices';
import CourseContentSkeleton from '../../../components/Skeleton/CourseContentSkeleton';
import CreateCourse from './CreateCourseForm';
import SnackBar from '../../../components/snackbar/snackbar';
import CaseStudies from './CaseStudies';
import clientAdminServices from '../../../services/clientAdmin/course/clientAdminServices';
import ImportSubModuleButton from './ImportSubModule';
import ScreensValidation from './ScreensValidation';
import MoveSubModule from './MoveSubModule';
import SubModuleAssessment from './SubModuleAssessment';
import SubModuleAssessmentUpdate from './SubModuleAssessmentupdate'
import '../../users/course/index.css';
import Iconify from '../../../components/Iconify';
import AddRererenceModel from './AddReferenceModel';

const CourseContent = () => {   
  const classes = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const isGoBack = true;
  const courseId = location.state;

  

  const [deleteAlert, setDeleteAlert] = useState(false);
  const [openSubModule, setOpenSubModule] = useState(false);
  const [openSubModuleMove, setOpenSubModuleMove] = useState(false);
  const [moveFromModule, setMoveFromModule] = useState('')
  const [expanded, setExpanded] = React.useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [modelTitle, setModelTitle] = useState('');
  const [formType, setFormType] = useState('simulation');
  const [formCategory, setFormCategory] = useState('subModule');
  const [modalBtnTxt, setModalBtnTxt] = useState('Create');
  const [moduleId, setModuleId] = useState('');
  const [subModuleId, setSubModuleId] = useState('');
  const [courseData, setCourseData] = useState(null);
  const [textFieldName, setTextFieldName] = useState('');
  const [courseDetails, setCourseDetails] = useState({});
  const [courseContent, setCourseContent] = useState([]);
  const [deleteType, setDeleteType] = useState(null);
  const [skeletonLoading, setSkeletonLoading] = useState(true);
  const [formValue, setFormValue] = useState(null);
  const [deleteModalTitle, setDeleteModalTitle] = useState('');
  const [courseStatus, setCourseStatus] = useState([]);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [buttonIndex, setButtonIndex] = React.useState(null);
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [snackbarTitle, setSnackbarTitle] = React.useState('');
  const [isWantMoreText, setIsWantMoreText] = useState(true);
  const [label, setLabel] = React.useState('');
  const [openTrialModal, setOpenTrialModal] = React.useState(false);
  const [trialModalLoading, setTrialModalLoading] = React.useState(false);
  const [progress, setProgress] = useState(0);
  const [openVideoModal, setOpenVideoModal] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [courses, setCourses] = useState([]);
  const [moveSubmoduleId, setMoveSubModuleId] = useState('')
  const [assessmentSubmoduleId, setAssessmentSubModuleId] = useState('')
  const [assessmentSubmoduleIdedit, setAssessmentSubModuleIdEdit] = useState('')
  
  const [openAssessment, setOpenAssessment] = useState(false);
  const [openAssessmentEdit, setOpenAssessmentEdit] = useState(false);

  const [openAddReference,setAddOpenReference]=useState(false);
  const [actionType, setActionType] = useState("");
  const [updateBtnLoading, setUpdateBtnLoading] = useState(false);
  const [deleteBtnLoading, setDeleteBtnLoading] = useState(false);
  const [videoTitle, setVideoTitle] = useState({
    title: '',
    video: null,
  });
  const [referenceValue, setReferenceValue] = useState({
    referenceFiles: [],
    referenceTexts: [],
    referenceFileViews: [],
    referenceFileNames: [],
    referenceId:''
  });
  console.log(referenceValue,"referenceValue");
  
  const [subModuleName,setSubModuleName]=useState('');
  const [details, setDetails] = useState([]);
  const [coursealldetails, setCourseAllDetails] = useState('');
  const [comingfrom, setComingfrom] = useState("");
  const [submodulesWithAssessments, setSubmodulesWithAssessments] = useState([]);

  const handleImportSuccess = () => {
    setOpenSnackbar(true);
    setSnackbarTitle(t('Import operation completed,loading submodules may take some time!!'));
    getCourseModules()
  };
  
  
  const user = useSelector((state) => state.userInfo);
  const [isAuthor,setisAuthor] = React.useState(false)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  
  
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  const ExpandMore = styled((props) => {
    // eslint-disable-next-line no-unused-vars
    const { expand, ...other } = props;
    return <IconButton {...other} />;
  })(({ theme, expand }) => ({
    transform: expanded === expand ? 'rotate(180deg)' : 'rotate(0deg)',
    marginLeft: 'auto',
    transition: theme.transitions.create('transform', {
      duration: theme.transitions.duration.shortest,
    }),
  }));

  useEffect(() => {
    setSkeletonLoading(true)
    getCourseContent();
    getCourseModules();
    getCourseStatusData();
  }, []);

  const RefreshDetails = () => {
    // setSkeletonLoading(true)
    // getCourseContent();
    getCourseModules();
    setComingfrom("")
    // getCourseStatusData();
    // getCourseModules
  }

  const handleExpandClick = (i) => {
    setExpanded(expanded === i ? -1 : i);
  };

  const handleCloseTrialModal = () => {
    setOpenTrialModal(false);
  };

  const handleCloseMoveSubModule = () =>{   
    setOpenSubModuleMove(false);    
  }
  const handleCloseAssessment = (assessmentCreated = false, updatedSubmoduleId = null) => {
    getCourseContent();
    setComingfrom("");
    
    if (assessmentCreated && updatedSubmoduleId) {
      setSubmodulesWithAssessments(prev => [...prev, updatedSubmoduleId]);
    }
    
    setOpenAssessment(false);
  }
  const handleCloseAssessmentEdit = () =>{  
    getCourseContent();
    setComingfrom("") 
    setOpenAssessmentEdit(false);    

  }

  
  const getDetailsByIdDetails = async(subModuleId,comingfrom1) => {
    
      const result = await adminServices.getDetailsById(subModuleId,comingfrom1);
      if (result.ok) {
        setDetails(result?.data)
      }  
  }



  const handleCloseAddReference=()=>{
    setAddOpenReference(false);
  }
 const submitReference = async (values) => {  
  const referenceFiles = values.references.map(ref => ref.referenceFile);
  const referenceFileViews = values.references.map(ref => ref.referenceFileView);
  const referenceFileNames = values.references.map(ref => ref.referenceFileName);
  const referenceTexts = values.references.map(ref => ref.description);
  const isFeatured = values.references.map(ref => ref.isFeatured);


  setBtnLoading(true);

  try {
    let referenceresponse;

    if (formType === "createRef") {
      const formData = new FormData();

      referenceFiles.forEach((file, index) => {
        formData.append('referenceFiles', file); 
        formData.append('referenceFileNames', referenceFileNames[index]);
        formData.append('referenceFileViews', referenceFileViews[index]); 
        formData.append('descriptions', referenceTexts[index]);
        formData.append('isFeatured', isFeatured[index]);
      });

      formData.append('refferenceText', values.refferenceText || ''); 
      formData.append('contentType', referenceFiles[0]?.type || '');

      referenceresponse = await adminServices.createReference(moduleId, subModuleName, formData);
    }

    else if (formType === "editRef") {
      const editFormData = new FormData();
      referenceFiles.forEach((file, index) => {
        editFormData.append('referenceFiles', file); 
        editFormData.append('referenceFileNames', referenceFileNames[index]);
        editFormData.append('referenceFileViews', referenceFileViews[index]); 
        editFormData.append('descriptions', referenceTexts[index]);
        editFormData.append('isFeatured', isFeatured[index]);
      });

      editFormData.append('refferenceText', values.refferenceText || ''); 
      editFormData.append('contentType', referenceFiles[0]?.type || '');
      if (actionType === "update") {
        setUpdateBtnLoading(true);
        getCourseModules();
        referenceresponse = await adminServices.editCourseSubModuleRef(moduleId, subModuleName, editFormData);
      } else if (actionType === "delete") {
        const alertRes = await swal({
          text: 'Are you sure you want to delete this Reference?',
          buttons: {
            confirm: {
              text: 'Okay',
              value: true,
              visible: true,
              className: '',
              closeModal: true,
            },
            cancel: {
              text: 'Cancel',
              value: null,
              visible: true,
              className: '',
              closeModal: true,
            },
          },
          closeOnClickOutside: false,
        });

        if (alertRes) {
          setDeleteBtnLoading(true);
          referenceresponse = await adminServices.deletreferenceDetails(referenceValue?.referenceId);
        }
      }
    }

    if (referenceresponse?.ok) {
      setAddOpenReference(false);
      setOpenSnackbar(true);
      setSnackbarTitle(referenceresponse.data.message);
      getCourseModules();
    } else {
      console.error('Error response:', referenceresponse);
    }

  } catch (error) {
    console.error(error);
  } finally {
    setBtnLoading(false);
    setUpdateBtnLoading(false);
    setDeleteBtnLoading(false);
  }
};


  // function will call when creating a module or submodule and updating a module or submodule
  
  const submitModuleForm = async (values) => {
    setProgress(0);
    setBtnLoading(true);
    // creating new module
    if (formType === 'module') {
      const data = {
        courseModuleName: values.courseTitle,
        isTrial: values.isTrial ? 'true' : 'false',
      };
      const modData = { ...data };
      if (values.videoFile) {
        modData.fileName = values.videoFile?.name;
        modData.contentType = values.videoFile?.type;
      }
      const courseResponse = await adminServices.createCourseModule(courseId, modData);
      if (courseResponse.ok) {
        // pre-sign url integration
        if (values.videoFile) {
          await axios
            .put(courseResponse.data?.preSignedUrl, values.videoFile, {
              headers: {
                'Content-Type': values.videoFile?.type,
                // 'x-amz-acl': 'public-read',
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setProgress(percentCompleted);
              },
            })
            .then((response) => {
              if (response.status === 200) {
                setOpenSubModule(false);
                getCourseModules();
              }
              setBtnLoading(false);
            });
          return;
        }
        setOpenSubModule(false);
        getCourseModules();
      } else {
        setOpenSnackbar(true);
        setSnackbarTitle(t('Please try again'));
      }
      setProgress(0);
      // creating new submodule
    } else if (formType === 'subModule') {
      const subData = {
        courseSubmoduleName: values.courseTitle,
        isTrial: values.isTrial ? 'true' : 'false',
        
      };
      const subModData = { ...subData };
      if (values.videoFile) {
        subModData.fileName = values.videoFile?.name;
        subModData.contentType = values.videoFile?.type;
      }
      const subModuleResponse = await adminServices.createCourseSubModule(moduleId, subModData,coursealldetails?.id);
      if (subModuleResponse.ok) {
        // pre-sign url integration
        if (values.videoFile) {
          await axios
            .put(subModuleResponse.data?.preSignedUrl, values.videoFile, {
              headers: {
                'Content-Type': values.videoFile?.type,
                // 'x-amz-acl': 'public-read',
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setProgress(percentCompleted);
              },
            })
            .then((response) => {
              if (response.status === 200) {
                getCourseModules();
                setOpenSubModule(false);
              }
              setBtnLoading(false);
            });
          return;
        }
        getCourseModules();
        setOpenSubModule(false);
      }
      setProgress(0);
    } else if (formType === 'EditSubmodule') {
      const subData = {
        courseSubmoduleName: values.courseTitle,
        isTrial: values.isTrial ? 'true' : 'false',
      };
      setFormCategory()
      const subModData = { ...subData };
      if (values.videoFile) {
        subModData.fileName = values.videoFile?.name;
        subModData.contentType = values.videoFile?.type;
      }
      const editModuleResponse = await adminServices.editCourseSubModuleName(moduleId, subModuleId, subModData,coursealldetails?.id);
      if (editModuleResponse.ok) {
        // pre-sign url integration
        if (values.videoFile) {
          await axios
            .put(editModuleResponse.data?.preSignedUrl, values.videoFile, {
              headers: {
                'Content-Type': values.videoFile?.type,
               //  'x-amz-acl': 'public-read',
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setProgress(percentCompleted);
              },
            })
            .then((response) => {
              if (response.status === 200) {
                setOpenSubModule(false);
                getCourseModules();
              }
            });
          setBtnLoading(false);
          return;
        }
        setOpenSubModule(false);
        getCourseModules();
      }
      setProgress(0);
    } else {
      // Update module details
      const modData = {
        courseModuleName: values.courseTitle,
        isTrial: values.isTrial ? 'true' : 'false',
      };
      const editData = { ...modData };
      if (values.videoFile) {
        editData.fileName = values.videoFile?.name;
        editData.contentType = values.videoFile?.type;
      }
      const editResponse = await adminServices.editCourseModuleName(courseId, moduleId, editData);
      if (editResponse.ok) {
        // pre-sign url integration
        if (values.videoFile) {
          await axios
            .put(editResponse.data?.video, values.videoFile, {
              headers: {
                'Content-Type': values.videoFile?.type,
                // 'x-amz-acl': 'public-read',
              },
              onUploadProgress: (progressEvent) => {
                setProgress(Math.round((progressEvent.loaded * 100) / progressEvent.total));
              },
            })
            .then((response) => {
              if (response.status === 200) {
                setOpenSubModule(false);
                getCourseModules();
              }
            });
          setBtnLoading(false);
          return;
        }
        setOpenSubModule(false);
        getCourseModules();
      }
      setProgress(0);
    }

    setBtnLoading(false);
  };

  const getCourseContent = async () => {
    const response = await adminServices.getCourseContent(courseId);
    if (response.ok) {
      
      let data = response.data
      
      
      if(data.authorList===null || data.authorList===undefined){
        data = {
            ...response.data,
            authorList:[]
        }
      }
      setCourseDetails(data);
    }
  };

  const getCourseModules = async () => {
    const result = await adminServices.getCourseModule(courseId);
    if (result.ok) {
      const data = result.data.sort((a, b) => a.positionIndex - b.positionIndex);
      
    
      const submodulesWithAssessmentsIds = [];
      data.forEach(module => {
        if (module.subModule && module.subModule.length > 0) {
          module.subModule.forEach(submodule => {
            if (submodule.hasAssessment || submodule.isIQTest) {
              submodulesWithAssessmentsIds.push(submodule.id);
            }
          });
        }
      });
      

      setSubmodulesWithAssessments(submodulesWithAssessmentsIds);
      setCourseContent(data);
      setSkeletonLoading(false);
    }
  };
  const handleAssessmentClick = (subModuleId) => {
    // setSubModuleId(subModuleId);
    setAssessmentSubModuleId(subModuleId)
    setOpenAssessment(true);
    setComingfrom("submodule")
  };

  const handleAssessment = (ModuleId) => {
    // setSubModuleId(subModuleId);
    setAssessmentSubModuleId(ModuleId)
    setOpenAssessment(true);
    setComingfrom("module")
  };

  const handleUpdateAssessmentClick = (subModuleId) => {
    setAssessmentSubModuleIdEdit(subModuleId)
    setOpenAssessmentEdit(true);
    setComingfrom("submodule")
    getDetailsByIdDetails(subModuleId,'submodule')
  };

  const handleUpdateAssessmentClick1 = (details) => {
    setComingfrom("module")
    setAssessmentSubModuleIdEdit(details.id)
    setOpenAssessmentEdit(true);
    setDetails(details)
    getDetailsByIdDetails(details.id,'module')

  };

  const handleDeleteSubModule = async () => {
    if (deleteType === 'SUBMODULE') {
      const deleteModuleRes = await adminServices.deleteSubModules(moduleId, subModuleId);
      if (deleteModuleRes.ok) {
        getCourseModules();
        setDeleteAlert(false);
      }
    } else if (deleteType === 'COURSE') {
      handleDeleteCourse();
    } else {
      const deleteCourseRes = await adminServices.deleteCourseModule(courseId, moduleId);
      if (deleteCourseRes.ok) {
        getCourseModules();
        setDeleteAlert(false);
      }
    }
  };

  const getCourseDetailsById = async () => {
    setOpenModal(true);
    const result7 = await adminServices.getCourseDetailsById(courseId);
    if (result7.ok) {
      let data = result7.data
      
      if(data.authorList===null){
        data = {
            ...result7.data,
            authorList:[]
        }
      }

      setCourseData(data);
    }
  };

  const handleSubmitMoveSubModule = async () =>{
    
    setOpenSubModuleMove(false)
    getCourseModules();
    setOpenSnackbar(true);
    setSnackbarTitle(t('Sub module moved successfully!'))
  }


  const handleSubmitCourseForm = async (values,fields) => {    
    setBtnLoading(true);
    const array = [];
    array.push(values.hashtag.map((res) => res.code));
    const formData = new FormData();
    formData.append('category', values.courseCategory.code);
    formData.append('title', values.courseTitle);
    formData.append('description', values.description);
    formData.append('hashTag', JSON.stringify(array[0]));
    formData.append('isTrial',  values.isTrial != null ? JSON.stringify(values.isTrial): false);
    formData.append('isfree', values.isfree != null ? JSON.stringify(values.isfree): false);
    formData.append("authors", values.authors);
    formData.append("short_desc",fields);
    formData.append('language_code', JSON.stringify(values.languagecode));
    formData.append('language_check', Boolean(values.languagecheck));
    formData.append('type',values.type);
    if(values?.subCategory?.code)
   {
     formData.append('subCategory', values?.subCategory?.code);
   }

    if (values.thumbImage) {
      formData.append('thumbImage', values.thumbImage);
    }
    if (values.coverageImage) {
      formData.append('coverageImage', values.coverageImage);
    }
    if (values.videoFile) {
      formData.append('fileName', values.videoFile?.name);
      formData.append('contentType', values.videoFile?.type);
    }

    try {
      const response = await adminServices.editCourseDetails(courseId, formData);
      if (response.ok) {
        if (values.videoFile) {
          await axios
            .put(response.data?.preSignedUrl, values.videoFile, {
              headers: {
                'Content-Type': values.videoFile?.type,
                // 'x-amz-acl': 'public-read',
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setProgress(percentCompleted);
              },
            })
            .then((response) => {
              if (response.status === 200) {
                setBtnLoading(false);
                setOpenModal(false);
                getCourseContent();
              } else {
                console.log(response);
                // props.snackBarControl('Please try again');
              }
            });
          setProgress(0);
          return;
        }
        getCourseContent();
      }
    } catch (error) {
      console.log(error);
      setBtnLoading(false);
    }

    setBtnLoading(false);
    setOpenModal(false);
  };

  // drag the item and position changing when dropped item
  const handleDrop = async (droppedItem) => {
    const courseModuleId = droppedItem.destination.droppableId;
    if (!droppedItem.destination) return;
    if (droppedItem.type === 'MODULE') {
      const updatedList = [...courseContent];
      // Remove dragged item
      const [reorderedItem] = updatedList.splice(droppedItem.source.index, 1);
      // Add dropped item
      updatedList.splice(droppedItem.destination.index, 0, reorderedItem);
      // Update State
      setCourseContent(updatedList);
      const positionArray = [];
      updatedList.map((res, index) => {
        const item = {
          id: res.id,
          positionIndex: index + 1,
        };
        positionArray.push(item);
        return item;
      });
      try {
        const positionRes = await adminServices.updateModulePositions(courseId, positionArray);
        if (positionRes.ok) {
          console.log(positionRes);
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      let resultArray = [];
      const updatedSubList = [...courseContent];
      updatedSubList.map((item, i) => {
        if (item.id === courseModuleId) {
          // Remove dragged item
          const [reorderedSubItem] = updatedSubList[i].subModule.splice(droppedItem.source.index, 1);
          // Add dropped item
          updatedSubList[i].subModule.splice(droppedItem.destination.index, 0, reorderedSubItem);
          // Update State
          resultArray = updatedSubList;
          setCourseContent(updatedSubList);
        }
        return item;
      });

      const positionArray = [];
      resultArray.map((res) => {
        if (res.id === courseModuleId) {
          res.subModule.map((subModule, indx) => {
            const item = {
              id: subModule.id,
              positionIndex: indx + 1,
            };
            positionArray.push(item);
            return item;
          });
        }
        return res;
      });
      try {
        const positionRes = await adminServices.updateSubModulePositions(courseModuleId, positionArray);
        if (positionRes.ok) {
          console.log(positionRes);
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const handleDeleteCourse = async () => {
    const deleteRes = await adminServices.deleteCourseById(courseId);
    if (deleteRes.ok) {
      navigate('/app/course');
    }
  };

  const getCourseStatusData = async () => {
    try {
      const res = await adminServices.getCourseStatus('course_status');
      if (res.ok) {
        setCourseStatus(res.data.course_status);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleCourseStatus = async (data, index) => {
    setButtonIndex(index);
    try {
      const item = await adminServices.updateCourseStatus(courseId, {
        status: data,
        type: courseDetails.type
      });
      if (item.ok) {
        setOpenSnackbar(true);
        setSnackbarTitle(item.data.message);
        getCourseContent();
        setAnchorEl(null);
      } else {
        setOpenSnackbar(true);
        setSnackbarTitle(item.data.message);
      }
    } catch (error) {
      console.log(error);
    }
    setButtonIndex(null);
  };

  const Breadcrumb = () => (
    <Breadcrumbs aria-label="breadcrumb" separator="›">
      <Typography
        sx={{ textDecoration: 'none' }}
        variant="body2"
        color="primary"
        component={RouterLink}
        to="/app/dashboard"
      >
        Dashboard
      </Typography>
      <Typography
        sx={{ textDecoration: 'none' }}
        variant="body2"
        color="primary"
        component={RouterLink}
        to="/app/course"
      >
        Course
      </Typography>
      <Typography variant="body2" color="textPrimary">
        Course details
      </Typography>
    </Breadcrumbs>
  );

  // delete the course introduction video
  const handleDeleteCourseVideo = () => {
    adminServices.deleteCourseVideo(courseId).then((response) => {
      if (response.ok) {
        getCourseContent();
        getCourseDetailsById();
      }
    });
  };

  const handleSubmitTrialModal = async (courseDetails) => {
    setTrialModalLoading(true);
    try {
      const res = await clientAdminServices.postTrialDetailsOnCourseId(JSON.stringify(courseDetails));
      if (res.ok) {
        setSnackbarTitle(t('Trial details modified successfully.'));
        setOpenSnackbar(true);
        setTrialModalLoading(false);
        setOpenTrialModal(false);
      } else {
        setSnackbarTitle(t('Failed to modify Trial details.'));
        setOpenSnackbar(true);
        setTrialModalLoading(false);
      }
    } catch (error) {
      setSnackbarTitle(t('Something went wrong please try agin.'));
      setOpenSnackbar(true);
      setTrialModalLoading(false);
      console.log(error);
    }
  };

  let colors = '';
  if (courseDetails?.status?.code === 'LIVE') {
    colors = 'green';
  } else if (courseDetails?.status?.code === 'EXPIRED') {
    colors = 'red';
  } else {
    colors = 'gray';
  }

  const ButtonGroup = () => {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box textAlign="left" mb={3} sx={{ display: 'flex' }}>
          <Typography style={{ color: '#878585e6' }}>Course ID : {courseId}</Typography>
          {courseDetails?.status && (
            <Typography
              sx={{
                width: 'min-content',
                fontWeight: '400',
                lineHeight: 'unset',
                backgroundColor: colors,
                textAlign: 'center',
                borderRadius: '4px',
                padding: '2px 10px',
                fontSize: '0.85rem',
                color: 'white',
                marginLeft: '1rem',
              }}
            >
              {courseDetails?.status?.description}
            </Typography>
          )}
          <Typography style={{ color: '#878585e6',marginLeft: '2rem' }}>Authors : [{(courseDetails.authorList)?.join(',')}]</Typography> 
          <Typography style={{ color: '#878585e6',marginLeft: '2rem' }}>Type: {courseDetails.type}</Typography>
        </Box>
        <Box textAlign="right" mb={3}>
          <Button variant="outlined" id='trialButtonId' sx={{ marginRight: '8px' }} onClick={() => setOpenTrialModal(true)}>
            Trial
          </Button>
          <Button
            variant="contained"
            id='editCourseButtonId'
            sx={{ backgroundColor: '#6D6969', marginRight: '8px' }}
            startIcon={<ModeEditIcon fontSize="small" />}
            onClick={() => getCourseDetailsById()}
          >
            Edit Course
          </Button>
          <IconButton aria-describedby={id} variant="contained" id='moreVertIconId' onClick={handleClick}>
            <MoreVertIcon />
          </IconButton>
          <Popover
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorReference="anchorPosition"
            anchorPosition={{ top: 175, left: 1300 }}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <MenuItem
              sx={{ mt: 1 }}
              onClick={() => {
                setDeleteType('COURSE');
                setDeleteAlert(true);
                setDeleteModalTitle('Are you sure you want delete this course?');
                setAnchorEl(null);
              }}
            >
              <DeleteIcon fontSize="small" id='deleteId' style={{ color: '#BCBCBC' }} />
              &nbsp; <Typography>Delete</Typography>
            </MenuItem>

            <div style={{ padding: '12px 12px' }}>
              {courseStatus &&
                courseStatus.map((item, index) => {
                  return (
                    <div key={index}>
                      {courseDetails.status && courseDetails.status.code === item.code ? null : (
                        <LoadingButton
                        key={item.code}
                        id='descriptionId'
                          onClick={() => {
                            handleCourseStatus(item.code, index);
                          }}
                          type="submit"
                          style={{
                            margin: '3px',
                            borderRadius: '50px',
                            minWidth: '140px',
                          }}
                          size="small"
                          variant="outlined"
                          loading={index === buttonIndex}
                        >
                          <Typography>{item.description}</Typography>
                        </LoadingButton>
                      )}
                    </div>
                  );
                })}
            </div>
          </Popover>
        </Box>
      </div>
    );
  };

  // eslint-disable-next-line no-unused-vars
  const handlePreviewSimulation = (subModuleId, module, resumeClic = false) => {
    navigate(`/app/simulation-preview?id=${subModuleId}&module=${module}`, {
      replace: true,
      state: courseDetails,
    });
  };

  const SubmoduleComponent = ({ module, subModule, moduleNumber }) => {
    return (
      <>
        <Droppable droppableId={module.id} type={`${moduleNumber}`}>
          {(provided) => (
            <div ref={provided.innerRef}>
              {subModule &&
                subModule.map((response, index) => {
                  console.log(response,"responseresponse");
                  
                  return (
                    <Draggable key={`${moduleNumber}${index}`} draggableId={`${moduleNumber}${index}`} index={index}>
                      {(provided) => (
                        <div ref={provided.innerRef} {...provided.draggableProps}>
                          <Box
                            className={classes.cardBox}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                            {...provided.dragHandleProps}
                          >
                            <Box>
                              <Typography sx={{ fontSize: '0.85rem' }}>
                                Sub Module
                                {index + 1}
                              </Typography>
                              <Typography
                                variant="subtitle1"
                                // component={LinkBtn}
                                // to={{
                                //   pathname: '/app/submodules',
                                // }}
                                sx={{ textDecoration: 'none !important' }}
                                color="primary"
                                onClick={() => {
                                  navigate('/app/submodules', { state: courseDetails });
                                  Cookies.set('subModule', JSON.stringify(response));
                                }}
                              >
                                {response.courseSubmoduleName}
                              </Typography>
                            </Box>
                            <div>
                              <Tooltip title="Simulation preview" arrow>
                                <Button
                                  size="small"
                                  id='simulateId'
                                  onClick={() => handlePreviewSimulation(response.id, module.courseModuleName)}
                                >
                                  {/* <Iconify icon="pajamas:live-preview" width={20} height={20} /> */}
                                  simulate
                                </Button>
                              </Tooltip>
                              { // eslint-disable-next-line prefer-template     
                                (user.role==='SUPER_ADMIN' || (courseDetails?.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
                               
                               
                               <Tooltip title="Move submodule to another module" arrow>
                                  <IconButton
                                            size="small"
                                            id='moveIconId'
                                            sx={{ marginRight: '10px' }}
                                            onClick={() => {
                                              setOpenSubModuleMove(true);
                                              setMoveFromModule(module.id)
                                              setMoveSubModuleId(response.id)
                                            }}
                                          >
                                            <MoveIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              }


                              { // eslint-disable-next-line prefer-template     
                                (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName)) &&
                                (response && response.isIQTest === false && 
                                 !submodulesWithAssessments.includes(response.id) && 
                                 !response.hasAssessment) ? (
                                  <Tooltip title="Create Submodule Assessments" arrow>
                                    <IconButton
                                      size="small"
                                      id='assessmentIconId'
                                      sx={{ marginRight: '10px' }}
                                      onClick={() => handleAssessmentClick(response.id)} >
                                      <AssessmentIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip> 
                                ) : (
                                  <Tooltip title="Edit Submodule Assessments" arrow>
                                    <IconButton
                                      size="small"
                                      id='assessmentIconId'
                                      sx={{ marginRight: '10px' }}
                                      onClick={() => handleUpdateAssessmentClick(response.id)} >
                                      <AssessmentIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip> 
                                )
                              }



                              {response.video && (
                                <Tooltip title="Includes video" arrow>
                                  <IconButton
                                    size="small"
                                     id='IconButtonForVideoId'
                                     onClick={() => {
                                      setOpenVideoModal(true);
                                      setVideoTitle({
                                        title: response.courseSubmoduleName,
                                        video: response.video,
                                      });
                                    }} 
                                  >
                                    <Iconify icon="pajamas:live-preview" width={20} height={20} />
                                  </IconButton>
                                </Tooltip>
                              )} 
                              {
                                (user.role === 'SUPER_ADMIN' || courseDetails.authorList?.includes(`${user.firstName} ${user.lastName}`)) &&
                                  
                                    response?.referenceData === null && response?.referenceData === null ? (
                                  <Tooltip title="Add Reference" arrow>
                                    <IconButton
                                      size='small'
                                      id='attachFileIconForCourseContentId'
                                      onClick={() => {
                                        setReferenceValue({
                                          referenceFiles: [],          
                                          referenceTexts: [],        
                                          referenceFileViews: [],
                                          referenceFileNames: [],
                                          referenceId:''
                                        });
                                        setModuleId(module.id);
                                        setAddOpenReference(true);
                                        setSubModuleName(response.courseSubmoduleName)
                                        setModalBtnTxt("Create")
                                        setFormType("createRef")
                                        setModelTitle('Add Reference');
                                      }}
                                    >
                                      <AttachFileIcon />
                                    </IconButton>
                                  </Tooltip>
                                ) : 
                                <Tooltip title="Edit Reference" arrow>
                                    <IconButton
                                    id='attachFileIconId'
                                      size='small'
                                      onClick={() => {
                                        setModuleId(module.id);
                                        setAddOpenReference(true);
                                        setSubModuleName(response.courseSubmoduleName)
                                        setModalBtnTxt('Update')
                                        setModelTitle('Edit Reference');
                                        console.log("onclick of edit ref", response)
                                        console.log("onclick of edit reference....>", module)
                                        console.log("data")
                                        setReferenceValue({
                                          referenceFiles: response?.referenceData && response?.referenceData && response?.referenceData?.length > 0 ? response?.referenceData?.map((data)=>data?.referenceUrl):[],                
                                          referenceTexts: response?.referenceData && response?.referenceData && response?.referenceData?.length > 0 ?  response?.referenceData?.map((data)=>data?.referenceText):[],      
                                          referenceFileViews: response?.referenceData && response?.referenceData && response?.referenceData?.length > 0 ?  response?.referenceData?.map((data)=>data?.referenceUrl):[],      
                                          referenceFileNames: response?.referenceData && response?.referenceData && response?.referenceData?.length > 0 ?  response?.referenceData?.map((data)=>data?.referenceUrl):[],
                                          isFeatured: response?.referenceData && response?.referenceData && response?.referenceData?.length > 0 ?  response?.referenceData?.map((data)=>data?.isFetched):[],
                                          referenceId:response?.id
                                        });
                                        setFormType("editRef")
                                      }}
                                    >
                                      <AttachFileIcon />
                                    </IconButton>
                                    </Tooltip>
                                }

                              <Tooltip title="Edit submodule name" arrow>
                                <IconButton
                                  size="small"
                                  id='modeEditIconId'
                                  onClick={() => {
                                    setOpenSubModule(true);
                                    // getCourseDetailsById();
                                    setFormCategory('subModule')
                                    setFormType('EditSubmodule');
                                    setTextFieldName('Sub Module Name');
                                    setModelTitle('Edit Sub module');
                                    setModalBtnTxt('Update');
                                    setLabel('Include this sub module in trial course?');
                                    setisAuthor(// eslint-disable-next-line prefer-template     
                                    (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName)))
                                    setSubModuleId(response.id);
                                    setFormValue({
                                      courseTitle: response?.courseSubmoduleName,
                                      isTrial: response?.isTrial,
                                      videoFilePreview: response?.video,
                                    });
                                    setModuleId(module.id);
                                    setCourseAllDetails(courseDetails)
                                                                      }}
                                >
                                  <ModeEditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>

                              { // eslint-disable-next-line prefer-template     
                              (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName))&&<Tooltip title="Delete submodule" arrow>
                                <IconButton
                                  size="small"
                                  id='deleteIconForSubmoduleId'
                                  onClick={() => {
                                    setDeleteType('SUBMODULE');
                                    setDeleteAlert(true);
                                    setModuleId(module.id);
                                    setSubModuleId(response.id);
                                    setDeleteModalTitle('Are you sure you want delete this submodule?');
                                  }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>}
                            </div>
                          </Box>
                        </div>
                      )}
                    </Draggable>
                  );
                })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </>
    );
  };

  // delete module videos
  const handleDeleteVideo = async () => {
    // let response = false;
    const alertRes = await swal({
      text: 'Are you sure you want to delete this video?',
      buttons: {
        confirm: {
          text: 'Okay',
          value: true,
          visible: true,
          className: '',
          closeModal: true,
        },
        cancel: {
          text: 'Cancel',
          value: null,
          visible: true,
          className: '',
          closeModal: true,
        },
      },
      closeOnClickOutside: false,
    });

    if (alertRes) {
      if (formType === 'EditModule') {
        const deleteResponse = await adminServices.handleDeleteModuleVideo(moduleId);
        if (deleteResponse.ok) {
          getCourseModules();
          setOpenSnackbar(true);
          setSnackbarTitle(t('Video deleted successfully'));
          // return true;
        }
        if (!deleteResponse.ok) {
          setOpenSnackbar(true);
          setSnackbarTitle(t('Something went wrong please try again later'));
        }
      } else {
        const deleteResponse = await adminServices.handleDeleteSubmoduleVideo(subModuleId);
        if (deleteResponse.ok) {
          getCourseModules();
          setOpenSnackbar(true);
          setSnackbarTitle(t('Video deleted successfully'));
          // return true;
        }
        if (!deleteResponse.ok) {
          setOpenSnackbar(true);
          setSnackbarTitle(t('Something went wrong please try again later'));
        }
      }
    }
    return true;
  };

  const { t } = useTranslation('translation');
 
  return (
    <Page title="Course-content">
      <div>
        <PageHeader
          pageTitle={courseDetails.title}
          goBack={isGoBack}
          buttonComponent={<ButtonGroup />}
          breadcrumbs={<Breadcrumb />}
        />

        {skeletonLoading ? (
          <CourseContentSkeleton />
        ) : (
          <>
            <Paper
              className={classes.paperContainer}
              sx={{ background: '#F9F9F9', border: '1px solid #BCBCBC', padding: '12px' }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sx={6} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Course Description
                  </Typography>
                  <Typography
                    className={isWantMoreText ? classes.showMoreText : null}
                    variant="body2"
                    dangerouslySetInnerHTML={{ __html: courseDetails.description }}
                  />
                  {courseDetails.description && courseDetails.description.length > 700 && (
                    <Typography
                      className={classes.readMore}
                      color="primary"
                      onClick={() => setIsWantMoreText(!isWantMoreText)}
                    >
                      {isWantMoreText ? 'Read more' : 'Read less'}
                    </Typography>
                  )}
                </Grid>
                <Grid item key="thumb-image" xs={12} sm={courseDetails.introVideo !== null ? 6 : false}
                 md={courseDetails.introVideo !== null ? 3 : 6} 
                 lg={courseDetails.introVideo !== null ? 3 : false} 
                 xl={courseDetails.introVideo !== null ? 3 : false}
                 >



                  <Typography variant="subtitle1" gutterBottom>
                    Thumb Image
                  </Typography>
                  <Box borderRadius={6} pb={1}>
                    <img
                      className={classes.thumbImg}
                      src={courseDetails.thumbImage}
                      width="350"
                      id='courseDetailsThumbImageId'
                      height="160"
                      alt="pic"
                    />
                  </Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Course Category
                  </Typography>
                  <Box pt={1}>
                    <Typography variant="body2" className={classes.thumbBox}>
                      {courseDetails.category && courseDetails.category.description}
                    </Typography>
                  </Box>
                </Grid>

                {courseDetails.introVideo !== null && (
                  <Grid item xs={12} sm={false} md={3} xl={false}>
                    <Typography variant="subtitle1" gutterBottom>
                      Introduction Video
                    </Typography>
                   

                    <video width="95%" controls controlsList="nodownload">
                      <source src={courseDetails.introVideo} type="video/mp4" />
                      <track src={courseDetails.introVideo} kind="captions" label="english_captions" />
                    </video>

                   
                    <Typography variant="subtitle2" gutterBottom>
                      Keyword
                    </Typography>
                    <Box pt={1} sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                      {courseDetails.hashTag &&
                        courseDetails.hashTag.map((res) => (
                          <Typography variant="body2" color="secondary" className={classes.hashTagBox}>
                            {res.description}
                          </Typography>
                        ))}
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Paper>

            <Box
              display={'flex'}
              justifyContent="space-between"
              alignItems="center"
              mt={3}
              pb={3}
              mb={2}
              sx={{ borderBottom: '1px solid #00B673 !important', paddingBottom: '12px !important' }}
            >
              <Typography variant="h4">{"Course Content"}</Typography>
              <div style={{ display: 'flex', alignItems: 'center' }}>

              { // eslint-disable-next-line prefer-template  
               (user.role==='SUPER_ADMIN' || (courseDetails?.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
              courseDetails && courseDetails.isCourse  === false?
                 <Button
               variant="contained"
               id='addcourseassId'
               startIcon={<AddIcon />}
               onClick={() =>handleAssessment(courseDetails.id)} 
               sx={{ marginRight: '10px' }}
             >
               Create Assessment
             </Button> 
             :
             <Button
               variant="contained"
               id='addcourseassId'
               startIcon={<ModeEditIcon />}
               onClick={() => handleUpdateAssessmentClick1(courseDetails)} 
               sx={{ marginRight: '10px' }}
             >
               Edit Assessment
             </Button> 
             
             }  
              { // eslint-disable-next-line prefer-template     
              (user.role==='SUPER_ADMIN' || (courseDetails?.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
                <Button
                variant="contained"
                id='addModuleId'
                startIcon={<AddIcon />}
                onClick={() => {
                  setOpenSubModule(true);
                  setTextFieldName('Module Name');
                  setModelTitle('Add Module');
                  setFormType('module');
                  setFormCategory('');
                  setModalBtnTxt('Create');
                  setisAuthor(// eslint-disable-next-line prefer-template     
                  (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName)))
                  setFormValue('');
                  setLabel('Include this module in trial course?');
                  setCourseAllDetails(courseDetails)
                }}
                sx={{ marginRight: '10px' }}
              >
                Add Module
              </Button> }
              { // eslint-disable-next-line prefer-template     
              (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
              <ImportSubModuleButton courseId={courseId} userId={user.id} courses={courses}  onImportSuccess={handleImportSuccess} courseDetails={courseDetails} > Import Submodule</ImportSubModuleButton>}
              </div>
            </Box>

            <div>
              <DragDropContext onDragEnd={handleDrop}>
                <Droppable droppableId="list-container" type="MODULE">
                  {(provided) => (
                    <div className="list-container" {...provided.droppableProps} ref={provided.innerRef}>
                      {courseContent &&
                        courseContent.map((res, index) => (
                          
                          <Draggable
                            key={res.positionIndex}
                            draggableId={res.positionIndex.toString()}
                            index={index}
                            type="TASK"
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                //  {...provided.dragHandleProps}
                                {...provided.draggableProps}
                              >

                                <Card className={classes.cardItem}>
                                  <CardActions disableSpacing>
                                    <div
                                      style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        paddingLeft: '16px',
                                        width: '100%',
                                        justifyContent: 'space-between',
                                      }}
                                    >
                                      <Box
                                        onClick={() => handleExpandClick(index)}
                                        sx={{ minWidth: '60%' }}
                                        {...provided.dragHandleProps}
                                      >
                                        <Typography sx={{ fontSize: '0.85rem' }}>
                                          Module
                                          {index + 1}
                                        </Typography>
                                        <Typography variant="subtitle1" sx={{ fontSize: '1.1rem' }}>
                                          {res.courseModuleName}
                                        </Typography>
                                      </Box>

                                      <Box style={{ display: 'flex', justifyContent: 'center',marginTop: '20px' }}>
                                        {res.video && (
                                          <Tooltip title="Watch video" arrow>
                                            <IconButton
                                              size="small"
                                              id='courseModuleIconId'
                                              onClick={() => {
                                                setOpenVideoModal(true);
                                                setVideoTitle({
                                                  title: res.courseModuleName,
                                                  video: res.video,
                                                });
                                              }}
                                            >
                                              <Iconify icon="pajamas:live-preview" width={20} height={20} />
                                            </IconButton>
                                          </Tooltip>
                                        )}
                                      <Tooltip title="Edit">
                                        <IconButton
                                          size="small"
                                          id='modeEditIconcourseDetailsId'
                                          onClick={() => {
                                            setModuleId(res.id);
                                            setOpenSubModule(true);
                                            setFormType('EditModule');
                                            setTextFieldName('Module Name');
                                            setModelTitle('Edit Module');
                                            setisAuthor(// eslint-disable-next-line prefer-template     
                                            (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName)))
                                            setModalBtnTxt('Update');
                                            setFormCategory('');
                                            setLabel('Include this module in trial course?');
                                            setFormValue({
                                              courseTitle: res.courseModuleName,
                                              isTrial: res.isTrial,
                                              videoFilePreview: res.video,
                                            });

                                            setCourseAllDetails(courseDetails)
                                          }}
                                        >
                                          <ModeEditIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                        { // eslint-disable-next-line prefer-template     
                                        (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
                                        <Tooltip title="Delete">
                                          <IconButton
                                            size="small"
                                            id='deleteIconModuleId'
                                            sx={{ marginRight: '10px' }}
                                            onClick={() => {
                                              setDeleteType('MODULE');
                                              setDeleteAlert(true);
                                              setModuleId(res.id);
                                              setDeleteModalTitle('Are you sure you want delete this module?');
                                            }}
                                          >
                                            <DeleteIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>}
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                        { // eslint-disable-next-line prefer-template     
                                        (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
                                        <Tooltip title="Add Sub Module">
                                          <IconButton
                                            sx={{ fontSize: 'small' }}
                                            id='addIconModuleCourseId'
                                            onClick={() => {
                                              setModuleId(res.id);
                                              setOpenSubModule(true);                                             
                                              setFormType('subModule');
                                              setFormCategory('subModule');
                                              setTextFieldName('Sub Module Name');
                                              setModelTitle('Add Sub Module');
                                              setModalBtnTxt('Create');                   
                                              setisAuthor(// eslint-disable-next-line prefer-template     
                                              (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName)))
                                              setFormValue('');
                                              setLabel('Include this sub module in trial course?');
                                              setCourseAllDetails(courseDetails)
                                            }}
                                            
                                          >
                                            <AddIcon />
                                          </IconButton>
                                        </Tooltip>}
                                        <div>
                                        { // eslint-disable-next-line prefer-template    
                                        (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName)) &&

                                        <ScreensValidation moduleId={res.id} type={courseDetails?.type}  />
                                          }
                                        
                                        </div>
                                        </div>
                                          
                                            <ExpandMore
                                                expand={index}
                                                Tooltip="Show More"
                                                onClick={() => handleExpandClick(index)}
                                                aria-expanded={expanded}
                                                aria-label="show more"
                                                sx={{ marginLeft: '16px'}}
                                                id='showMoreIconId'
                                              >
                                              <ExpandMoreIcon />
                                            </ExpandMore>
                                          
                                      </Box>
                                    </div>
                                  </CardActions>

                                  <Collapse in={expanded === index} timeout="auto" unmountOnExit>
                                    <CardContent>
                                      <SubmoduleComponent moduleNumber={index} module={res} subModule={res.subModule} />
                                    </CardContent>
                                  </Collapse>
                                </Card>
                              </div>
                            )}
                          </Draggable>
                        ))}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
            
            { // eslint-disable-next-line prefer-template     
              (user.role==='SUPER_ADMIN' || (courseDetails.authorList)?.includes(user.firstName + ' '+ user.lastName))&&
            <CaseStudies courseDetails={courseDetails} />}
          </>
        )}
      </div>
      <MoveSubModule
        open={openSubModuleMove}
        onClose={handleCloseMoveSubModule}
        onSubmit={handleSubmitMoveSubModule}
        from={moveFromModule}
        modules={courseContent}
        submoduleId= {moveSubmoduleId}
      />
      <SubModuleAssessment
        open={openAssessment}
        onClose={handleCloseAssessment}
        submoduleId= {assessmentSubmoduleId}
        comingfrom={comingfrom}
        // submitForm={handleCreateAssessment}
      />
   <SubModuleAssessmentUpdate
        open={openAssessmentEdit}
        onClose={handleCloseAssessmentEdit}
        // submitForm={handleCreateAssessment}
        submoduleId= {assessmentSubmoduleIdedit}
        alldetails={details}
        comingfrom={comingfrom}
      />


      <AddRererenceModel
        open={openAddReference}
        onClose={handleCloseAddReference}
        modelWidth="xs"
        buttonText={modalBtnTxt}
        isAuthor={isAuthor}
        formCategory={formCategory}
        handleSubmit={submitReference}
        data={referenceValue}
        modelTitle={modelTitle}
        loading={btnLoading}
        formType={formType}
        setActionType={setActionType}
        deleteBtnLoading={deleteBtnLoading}
        updateBtnLoading={updateBtnLoading}
      />
      <AddSubModuleForm
        open={openSubModule}
        modelTitle={modelTitle}
        closeModel={() => {
          setOpenSubModule(false);
          setBtnLoading(false);
        }}
        textFieldName={textFieldName}
        handleSubmitForm={submitModuleForm}
        modelWidth="xs"
        buttonText={modalBtnTxt}
        isAuthor={isAuthor}
        loading={btnLoading}
        formCategory={formCategory}
        data={formValue}
        label={label}
        progress={progress}
        handleVideoDelete={() => handleDeleteVideo()}
        courseType={courseDetails.type}
        coursealldetails={coursealldetails}
      />

      <CreateCourse
        open={openModal}
        modelClose={() => setOpenModal(false)}
        submitForm={handleSubmitCourseForm}
        loading={btnLoading}
        data={courseData}
        title="Edit Course"
        formType="EDIT"
        deleteVideo={() => handleDeleteCourseVideo()}
        progress={progress}
      />

      <DeleteAlert
        open={deleteAlert}
        title={deleteModalTitle}
        confirm={handleDeleteSubModule}
        close={() => setDeleteAlert(false)}
      />

      <TrialModal
        open={openTrialModal}
        title="Set Trial Modules"
        close={handleCloseTrialModal}
        courseId={courseId}
        submit={handleSubmitTrialModal}
        loading={trialModalLoading}
      />

      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />

      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={openVideoModal}
        // onClose={handleClose}
        closeAfterTransition
        sx={{
          backgroundColor: '#0a00008c !important',
          '.css-moysu4': {
            border: 'none !important',
          },
        }}
      >
        <Fade in={openVideoModal}>
          <Box className={classes.vediobox}>
            <Typography variant="h6" color="#fff" gutterBottom>
              {videoTitle.title}
            </Typography>
            <video width="520" controls style={{ width: '100%' }} autoPlay controlsList="nodownload">
              <source src={videoTitle.video} type="video/mp4" />
              <track src="captions_en.vtt" kind="captions" label="english_captions" />
            </video>
            <IconButton
              sx={{
                position: 'absolute',
                top: '24px',
                background: '#f6f6f6',
                // '.css-3u56ba-MuiButtonBase-root-MuiIconButton-root:hover': {
                //   backgroundColor: 'rgb(255 255 255 / 67%) !important',
                // },
              }}
              id='CloseIconForVideoId'
              className={classes.closeBtn}
              size="small"
              onClick={() => setOpenVideoModal(false)}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Fade>
      </Modal>
    </Page>
  );
};

const useStyles = makeStyles(() => ({
  thumbImg: {
    width: '100%',
  },
  thumbBox: {
    width: 'max-content',
    background: '#fafafa',
    padding: '4px 8px',
    border: '1px solid #707070',
    borderRadius: '4px',
  },
  cardBox: {
    background: '#E6E6E6',
    padding: '12px',
    borderRadius: '4px',
    marginBottom: '8px',
  },
  editButton: {
    background: '#BCBCBC !important',
  },
  vediobox: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    // width: '60%',
    height: 'auto',
    bgcolor: 'background.paper',
    padding: '20px',
    boxShadow: 24,
    p: 4,
  },
  closeBtn: {
    '&:hover': {
      backgroundColor: 'rgb(255 255 255 / 67%)',
    },
  },
  hashTagBox: {
    width: 'max-content',
    background: '#fafafa',
    padding: '4px 8px',
    marginRight: '8px',
    marginBottom: '8px',
    border: '1px solid #707070',
    borderRadius: '4px',
  },
  cardItem: {
    background: '#F9F9F9',
    marginBottom: '24px',
    border: '1px solid #e1e1e1ab',
  },
  showMoreText: {
    display: '-webkit-box',
    WebkitLineClamp: 11,
    overflow: 'hidden',
    WebkitBoxOrient: 'vertical',
  },
  readMore: {
    width: 'max-content',
    cursor: 'pointer',
  },
}));

export default CourseContent;