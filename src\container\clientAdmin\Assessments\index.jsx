/* eslint-disable react/jsx-key */
import React, { useEffect, useState } from 'react';
import { Grid, Container, IconButton, Typography, Card, CardContent, CardMedia, Chip, Box, Stack, Button } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import GridOnIcon from '@mui/icons-material/GridOn';
import ViewListIcon from '@mui/icons-material/ViewList';
import { makeStyles } from '@mui/styles';
import { useNavigate } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import Page from '../../../components/Page';
import Cards from '../../../components/cards/UserCard';
import PageHeader from '../../../components/PageHeader';
import clientAdminServices from '../../../services/clientAdmin/course/clientAdminServices';
import CardSkeleton from '../../../components/Skeleton/cardSkeleton';


export default function Index() {
    const classes = useStyles();
    const navigate = useNavigate();
    const [assessments, setAssessments] = useState([]);
    const [loading, setLoading] = useState(true);

    const handleCardClickItem = (data) => {
        navigate('/app/client-assessment-content', { state: data });
    };

    const getUser = async () => {
        try {
            const response = await clientAdminServices.getAssessments();
            if (response.ok) {
                setAssessments(response.data);
                setLoading(false);
            }
        } catch (error) {
            console.log(error);
        }
    };
    useEffect(() => {
        getUser();
    }, []);

    const ButtonGrp = () => (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}>
            <IconButton>
                <SearchIcon fontSize="medium" style={{ color: '#BCBCBC' }} />
            </IconButton>
            <IconButton>
                <FilterAltIcon fontSize="medium" style={{ color: '#BCBCBC' }} />
            </IconButton>
            <IconButton>
                <GridOnIcon fontSize="medium" style={{ color: '#00B673' }} />
            </IconButton>
            <IconButton>
                <ViewListIcon fontSize="medium" style={{ color: '#BCBCBC' }} />
            </IconButton>
        </div>
    );

    return (
        <div>
            <Page title="Assessment-list">
                <PageHeader pageTitle="Assessments " breadcrumbs={<ButtonGrp />} />
                <Container maxWidth={false} sx={{ padding: '0 !important' }}>
                    <Grid container spacing={2}>
                        {loading ? (
                            <>
                                {[1, 2, 3, 4, 5, 6].map(() => (
                                    <Grid item xs={12} sm={4} md={4}>
                                        <CardSkeleton />
                                    </Grid>
                                ))}
                            </>
                        ) : (
                            <>

                                    <Grid container spacing={2}>
                                        {assessments.map((assessment) => (
                                            <Grid item xs={12} sm={6} md={4} key={assessment.id}>
                                                <Card sx={{ borderRadius: 3, boxShadow: 3 }}onClick={() => handleCardClickItem(assessment)}>
                                                    {assessment.image_name && assessment.image_name !== 'null' ? (
                                                        <CardMedia
                                                            component="img"
                                                            height="160"
                                                            image={assessment.image_name}
                                                            alt={assessment.title}
                                                        />
                                                    ) : (
                                                        <Box
                                                            sx={{
                                                                height: 160,
                                                                backgroundColor: '#f5f5f5',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                color: '#888'
                                                            }}
                                                        >
                                                            No Image
                                                        </Box>
                                                    )}

                                                    <CardContent>
                                                        <Stack spacing={1}>
                                                            <Typography variant="h6">{assessment.title}</Typography>

                                                            <Stack direction="row" spacing={1}>
                                                                <Chip label={assessment.type} color="primary" size="small" />
                                                                <Chip
                                                                    label={assessment.is_published ? 'Published' : 'Unpublished'}
                                                                    color={assessment.is_published ? 'success' : 'error'}
                                                                    icon={assessment.is_published ? <CheckCircleIcon /> : <CancelIcon />}
                                                                    size="small"
                                                                />
                                                            </Stack>
                                                            <Box
                                                                dangerouslySetInnerHTML={{
                                                                    __html: assessment.short_description
                                                                }}
                                                                sx={{ fontSize: 14, color: '#444' }}
                                                            />

                                                            <Typography variant="body2" color="text.secondary">
                                                                Enrolled Users: {assessment.enrolledUsers}
                                                            </Typography>
                                                            <Typography variant="body2" color="text.secondary">
                                                                Licenses Allocated: {assessment.noOfLicenseAllocated}
                                                            </Typography>
                                                        </Stack>
                                                    </CardContent>
                                                </Card>
                                            </Grid>
                                        ))}
                                    </Grid>


                                {assessments.length === 0 && (
                                    <Typography marginTop={4} variant="h6" textAlign="center" sx={{ width: '100%' }}>
                                        No Data
                                    </Typography>
                                )}
                            </>
                        )}
                    </Grid>
                </Container>
            </Page>
        </div>
    );
}

const useStyles = makeStyles(() => ({
    cardHover: {
        // transition: 'all .5s ease-out',
        '&:hover': {
            // boxShadow: '0 12px 20px 0 rgb(32 40 45 / 8%)',
            transition: 'transform .5s',
            // transform: 'translateY(-5px)',
        },
    },
}));
