/* eslint-disable no-async-promise-executor */
/* eslint-disable no-alert */
/* eslint-disable react/button-has-type */
/* eslint-disable no-irregular-whitespace */
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Link, CircularProgress, TextField } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import * as XLSX from 'xlsx';
import { useSelector } from 'react-redux';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import './styles.css';

const FileUpload = () => {
  const userInfo = useSelector((state) => state.userInfo && state.userInfo);
  const [questions, setQuestions] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const [excelfile, setExcelfile] = useState([]);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState('');
  const questionsPerPage = 5;
  const { t } = useTranslation('translation');
  console.log(errors, "errors");

  const handleChangeExcel = async (event) => {
    setErrors('')
    const file = event.currentTarget.files[0];

    if (!file) return;

    setExcelfile(file);

    try {
      const extractedQuestions = await processExcel(file);
      setQuestions(extractedQuestions);
    } catch (error) {
      console.error("Error processing Excel file:", error.message);
      // Optional: show an alert or toast here
    }
    event.target.value = null;
  };


  const processExcel = (file) => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(firstSheet, { defval: '' });

        if (!jsonData.length) {
          throw new Error('No data found in the Excel file');
        }
        const formattedQuestions = jsonData.map((row) => ({
          question_text: row.question_text || '',
          question_type: row.question_type || '',
          level: row.level || '',
          options: row.options || '',
          cognitive_skills: row.cognitive_skills || '',
          passage: row.passage || '',
          justification: row.justification || '',
          correct_answer: row.correct_answer || '',
          study_material:row.study_material || ''
        }));        

        resolve(formattedQuestions);
      } catch (err) {
        reject(err);
      }
    };

    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };

    reader.readAsArrayBuffer(file);
  });


  // const sendQuestionsToBackendNew = async () => {
  //   if (excelfile) {
  //     setLoading(true)
  //     const data = new FormData();
  //     data.append('questionFile', excelfile);
  //     try {
  //       const response = await adminServices.SatQuestionupload(data, userInfo?.id);
  //       if (response) {
  //         console.error('responseeee...', response);
  //         if (response?.data?.message === 'Validation failed for some questions') {
  //           setErrors(response?.data)
  //           setQuestions([])
  //           setLoading(false)
  //           setExcelfile([])
  //         }
  //         else {
  //           setLoading(false)
  //           setErrors('')
  //           setSnackbarTitle(t('Questions Uploaded successfully'));
  //           setOpenSnackbar(true);
  //           setExcelfile([])
  //           setQuestions([])
  //         }

  //       }
  //     } catch (error) {
  //       console.error('Error uploading questions:', error);

  //       setLoading(false)
  //       setErrors(error)
  //     }
  //   }
  //   else {
  //     setLoading(false)
  //     alert("Please upload file")
  //   }

  // };

 const sendQuestionsToBackendNew = async () => {

    if (!excelfile) { 
        setLoading(false);
        setSnackbarTitle(t('Please upload a Valid file'));
        setOpenSnackbar(true);
        return; 
    }

    if (Array.isArray(excelfile) && excelfile.length === 0) {
        setLoading(false);
        setSnackbarTitle(t('Please upload a Valid file'));
        setOpenSnackbar(true);
        return; 
    }

    setLoading(true);
    const data = new FormData();
    data.append('questionFile', excelfile);

    try {
        const response = await adminServices.SatQuestionupload(data, userInfo?.id);
        if (response) {
            console.error('responseeee...', response);

            if (response?.data?.message === 'Validation failed for some questions') {
                setErrors(response?.data);
                setQuestions([]);
                setLoading(false);
                setExcelfile([]);
                setSnackbarTitle(t('Please add all the Mandatory fields')); 
                setOpenSnackbar(true); 
            } else if (response?.data?.message === 'Question file is required') {
                setErrors(response?.data); 
                setLoading(false);
                setSnackbarTitle(t('Question file is required')); 
                setOpenSnackbar(true);
                setExcelfile([]); 
            }
            else { 
                setLoading(false);
                setErrors(''); 
                setSnackbarTitle(t('Questions Uploaded successfully'));
                setOpenSnackbar(true);
                setExcelfile([]);
                setQuestions([]);
            }
      }
    } catch (error) {
      console.error('Error uploading questions:', error);
      setLoading(false);
      const errorMessage = error.response?.data?.message || error.message || 'An unknown error occurred during upload.';
      setErrors(error);
      setSnackbarTitle(t(`Error: ${errorMessage}`));
      setOpenSnackbar(true);
    }
  };

  const totalPages = Math.ceil(questions.length / questionsPerPage);
  const currentQuestions = questions.slice(
    (currentPage - 1) * questionsPerPage,
    currentPage * questionsPerPage
  );

  const goToPage = (page) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };


  return (
    <Page title="Add Multiple Questions">
      <PageHeader pageTitle="Add Multiple Questions" />

      <h4 style={{ marginBottom: '6px' }}>Upload File (xlsx, xls, xlsm, xlsb)</h4>
      {/* <div {...getRootProps()} id="dropzoneSection" style={{
        cursor: 'pointer', border: '1px solid rgba(0, 0, 0, 0.22)',
        padding: '5px 12px', width: '550px', borderRadius: '5px', display: 'flex'
      }}>

        <input id='Uploadprops' style={{ display: 'block !important' }} {...getInputProps()} />

      </div> */}


      {loading && (
        <div style={{ textAlign: 'center', margin: '20px 0' }}>
          <CircularProgress />
          <p>Processing file...</p>
        </div>
      )}

      {/* {errors && <Typography style={{color:'red'}}> An issue with these {errors} questions and options</Typography>} */}

      {/* {error && (
        <SnackBar
          open
          snackbarTitle={error}
          close={() => setError(null)}
          severity="error"
        />
      )} */}

      {/* {loading === false ?
        <Button id='uploadbutton' variant="outlined" color="primary" onClick={() => sendQuestionsToBackend(questions)} style={{ marginLeft: '0px', marginTop: '25px', marginBottom: '10px' }}>
          Upload Questions
        </Button>
        :
        <Button id='loadinguploadbutton' variant="outlined" color="primary" style={{ marginLeft: '10px', marginTop: '10px' }}>
          Loading...
        </Button>
      } */}

      <TextField
        style={{ width: '550px', display: 'block' }}
        id="outlinedBasic"
        name="userFile"
        // onBlur={handleBlur}
        inputProps={{
          accept: '.xlsx, .xls, .xlsm, .xlsb',
          style: { color: 'transparent' }
        }}
        onChange={(event) => {
          handleChangeExcel(event)

        }}
        type="file"
        variant="outlined"
      />
      {loading === false ?
        <Button id='uploadbutton' variant="outlined" color="primary" onClick={sendQuestionsToBackendNew} style={{ marginLeft: '0px', marginTop: '25px', marginBottom: '10px' }}>
          Upload Questions
        </Button>
        :
        <Button id='loadinguploadbutton' variant="outlined" color="primary" style={{ marginLeft: '10px', marginTop: '10px' }}>
          Loading...
        </Button>
      }

      {/* {questions && questions?.length > 0 && */}
      {/* <Button variant="outlined" color="primary" id='downloadquestions' onClick={downloadQuestions} style={{ marginLeft: '20px', marginTop: '25px', marginBottom: '10px' }}>Download Sample format</Button> */}
     
      {process.env.REACT_APP_ENV !== 'prod' && 
      <Link
        id="downloadtemplate"
        href="https://dh0utea3ai9xq.cloudfront.net/assessmentInfo/SampleInstructions.xlsx"
        // style={{ textDecoration: 'none' }}
        style={{ textDecoration: 'none' ,marginLeft: '20px', marginTop: '25px', marginBottom: '10px' }}
        color="secondary"
        
      >
        Download Sample format
      </Link>}

{process.env.REACT_APP_ENV === 'prod' &&<Link
        id="downloadtemplate"
        href="https://testmybucket.s3.us-east-1.amazonaws.com/assessmentInfo/SampleEnglishInstruction.xls"
        // style={{ textDecoration: 'none' }}
        style={{ textDecoration: 'none' ,marginLeft: '20px', marginTop: '25px', marginBottom: '10px' }}
        color="secondary"
        
      >
        Download Sample format
      </Link>}

      {questions.length > 0 && (

        <>

          <div>
            <div style={{ overflowX: 'auto', width: '100%' }}>
              <table
                id="QuestionListtable"
                style={{ width: '100%', borderCollapse: 'collapse', minWidth: '1000px' }}
              >
                <thead>
                  <tr>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '50px' }}>No</th>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '300px' }}>Question</th>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '100px' }}>Options</th>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '420px' }}>Question Type</th>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '100px' }}>Level</th>

                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '180px' }}>Correct Answer</th>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '350px' }}>Justification</th>
                    <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '350px' }}>Study Material</th>
                  </tr>
                </thead>
                <tbody>
                  {currentQuestions.map((q, index) => (
                    <tr key={index}>
                      <td style={{ padding: '10px', border: '1px solid #ddd', textAlign: "center" }}>
                        {(currentPage - 1) * questionsPerPage + index + 1}
                      </td>
                      <td style={{ padding: '10px', border: '1px solid #ddd', }}>{q.question_text}</td>
                      <td style={{ padding: '10px', border: '1px solid #ddd' }}>{q.options}</td>
                      <td style={{ padding: '10px', border: '1px solid #ddd', textAlign: "center" }}>{q.question_type}</td>
                      <td style={{ padding: '10px', border: '1px solid #ddd', textAlign: "center" }}>{q.level}</td>
                      <td style={{ padding: '10px', border: '1px solid #ddd' }}>{q.correct_answer}</td>
                      <td style={{ padding: '10px', border: '1px solid #ddd' }}>{q.justification}</td>
                      <td style={{ padding: '10px', border: '1px solid #ddd' }}>{q.study_material}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>


            <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Button
                variant="outlined"
                onClick={() => goToPage(currentPage - 1)}
                disabled={currentPage === 1}
                style={{ cursor: 'pointer', }}
              >
                <ArrowBackIosIcon style={{ fontSize: '16px', fontWeight: '800' }} />
                {/* <ArrowBackIcon/> */}
              </Button>
              <span style={{ margin: '0 10px' }}>
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outlined"
                onClick={() => goToPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                style={{ height: '26px', cursor: 'pointer' }}
              >
                <NavigateNextIcon style={{ fontSize: '25px', fontWeight: '400' }} />
              </Button>
            </div>
          </div>
        </>
      )}



      {errors && errors?.failedQuestions && errors?.failedQuestions?.length > 0 &&
        <div>
          <div style={{ overflowX: 'auto', width: '100%' }}>
            <table
              id="QuestionListtable"
              style={{ width: '100%', borderCollapse: 'collapse', minWidth: '1000px' }}
            >
              <thead>
                <tr>
                  <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '50px' }}>No</th>
                  <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '300px' }}>Question</th>

                  <th style={{ padding: '10px', border: '1px solid rgb(201 199 199)', minWidth: '350px' }}>Failures</th>
                </tr>
              </thead>
              <tbody>
                {errors?.failedQuestions?.map((q, index) => (
                  <tr key={index}>
                    <td style={{ padding: '10px', border: '1px solid #ddd', }}>{index + 1}</td>
                    <td style={{ padding: '10px', border: '1px solid #ddd', }}>{q.question}</td>

                    <td style={{ padding: '10px', border: '1px solid #ddd', textAlign: 'left' }}>
                      {q.issues?.map((issue, i) => (
                        <div key={i} style={{ marginBottom: '4px', color: 'red' }}>
                          • {issue}
                        </div>
                      ))}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

        </div>}
      <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />

    </Page>
  );
};

export default FileUpload;