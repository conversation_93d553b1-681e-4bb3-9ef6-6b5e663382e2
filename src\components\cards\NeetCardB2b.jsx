/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React, {  useState } from 'react';
import {  CardContent, Typography, Button, Box, Grid, Chip, Stack, Tooltip } from "@mui/material";
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from '@mui/styles';
import Doctor from "../../assets/Images/doctor.svg";
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
import BackgroundImg from '../../assets/Images/NEET_Assess1.png';
import {
  languagecodevalue,
  openSubscriptionModal,
  setOpenSubscriptionModalDetails,
  openSnackbar,
  setSubscribedCourses, ComingFrom, ComingFromSub
} from '../../store/reducer';
import trialAndStripeSubscriptionService from '../../services/trialAndStripeSubscriptionService';

export default function SatCard(props) {
    
  const userRole = useSelector((state) => state.userInfo && state.userInfo.role);
  const dispatch = useDispatch()
  const classes = useStyles();
  const navigate = useNavigate();
  const [loading,setLoading]  = useState(false)
  const userInfo = useSelector((state) => state.userInfo && state.userInfo);
function convertComplexity(level) {
  switch (level.toLowerCase()) {
    case 'easy':
      return 'Beginner';
    case 'medium':
      return 'Intermediate';
    case 'hard':
      return 'Advanced';
    default:
      return level;
  }
}

  const handleClickTrialButton = async (planId, enrollmentType, assessmentId, details) => {
    setLoading(true)
    const body = {
      name: details.title,
      description: details.short_description,
      SATtype: details.type,
      level: convertComplexity(details.complexity_level)
    }
    try {
      const res = await trialAndStripeSubscriptionService.postTrialAssessmentEnrollmentDetails(
        JSON.stringify({ planId, enrollmentType, assessmentId, authUserId: userInfo.id, body })
      );
      if (res.ok) {
    setLoading(false)
       dispatch(openSnackbar(`Subscribed to ${details?.title} Successfully.`));
       handleNavigateNEETAssessmentNew();
        // const data = {
        //   id: props?.assessment_details?.id,
        //   // from:location.state?.from
        // }
        // navigate("/auth/AssessmentOverview", { state: data})
        // navigate("/auth/AssessmentOverview", { state: assessmentDetails?.assessment_details?.id })
      }
    } catch (error) {
        setLoading(false)
      console.log(error);
    }
  };
  const handleNavigateNEETAssessmentNew = () => {
    const data = {
      id: props.data.id,
      from: "NEET",
      details:props.data
    }
    navigate('/app/NEETAssessmentDetails', { state: data })
  }

  const handleStartSat = () => {
    if (userRole === 'AUTH_USER') {
      dispatch(languagecodevalue('en'))

      navigate("/app/SatOverview", {
        state: {
          id: props?.data?.id,
          from: "SAT",
          data: props?.data
        }
      });
    }
    else {
      dispatch(languagecodevalue('en'))
      navigate("/auth/SatOverview", {
        state: {
          id: props?.data?.id,
          from: "SAT",
          data: props?.data
        }
      });
    }
  }

  return (
    <Box style={{ borderRadius: '8px',overflow: 'hidden',    maxWidth: '95%',
    boxShadow: '0px 3px 1px -2px rgba(145, 158, 171, 0.2),0px 2px 2px 0px rgba(145, 158, 171, 0.14),0px 1px 5px 0px rgba(145, 158, 171, 0.12)'}}>


          <Box className={classes.CardViewBackground}
            sx={{
            //   background: "linear-gradient(to right, #6C63FF, #AB47BC)",
              backgroundImage: `url(${BackgroundImg})`,
              backgroundSize: 'contain',
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
              height: '160px',
              backgroundRepeat: 'no-repeat',
              transform: 'scale(1.2)'
            }}
          >
            <Doctor sx={{ fontSize: 40, color: "white" }} />
            {(userRole === 'AUTH_USER') ? null : (
            <Box
              sx={{
                position: "absolute",
                top: 10,
                right: 10,
                backgroundColor: "#6A3FE9",
                color: "white",
                padding: "3px 8px !important",
                fontSize: 12,
                fontWeight: "bold",
                borderRadius: 1,
                // textDecoration: "line-through",
              }}
            >
              <Typography component="span" sx={{ textDecoration: "line-through", mr: 0.5, color: "#dddddd" }}>
                $49.99
              </Typography>
              <Typography component="span" sx={{ textDecoration: "none", color: "#fff" }}>
                Free
              </Typography>
            </Box>
            )}
          </Box>

          {/* Content */}
          <CardContent sx={{ padding: "15px 12px" }}>
            <Stack direction="row" spacing={1} mb={1}>
              <Chip label="Beginner" size="small" sx={{
                background: '#c6f3d4', color: "#003d15",
                borderRadius: '2px', fontWeight: '500',
                textTransform:'capitalize'
              }} />
              <Chip label="NEET" size="small" sx={{
                background: '#dbeafe', color: "#2b87ff",
                borderRadius: '2px', fontWeight: '500'
              }} />
            </Stack>
            {props.data?.title?.length > 25 ?
              (<Tooltip title={props.data?.title} arrow>
                <Typography variant="h6" fontWeight="bold" id="TitleName">
                  {props.data?.title}
                </Typography>
              </Tooltip>)
              :
             ( <Typography variant="h6" fontWeight="bold" id="TitleName">
                {props.data?.title}
              </Typography>)
            }
            
            <Typography className={classes.HighlightedSection} variant="body2" color="text.secondary" mt={0.5} sx={{    }}>
              {props.data?.highlighted_section}
             </Typography>
           


            {/* <Box mt={2} display="flex" alignItems="center">
              <Rating value={4} precision={0.5} readOnly size="small" />
              <Typography variant="body2" color="text.secondary" ml={1}>
                4.0 (120 reviews)
              </Typography>
            </Box> */}

            <Box mt={1} display="flex" alignItems="center">
              <PlayCircleOutlineIcon fontSize="small" sx={{ mr: 0.5 }} />
              <Typography variant="body2">No of Attempts : 05</Typography>
            </Box>

            <Stack direction="row" spacing={1} mt={3}>

              {loading &&
                <Button
                  fullWidth
                  loading
                  variant="contained"
                  sx={{
                    borderRadius: "7px",
                    height: "38px",
                    background: "#437bfc",
                    color: "#fff"
                  }}
                >
                  Please wait
                </Button>
              }
              {
              (!loading && props.data?.is_subscribed) || userRole === 'AUTH_USER' ? (
    <Button
      fullWidth
      variant="outlined"
      color="primary"
      sx={{
        borderRadius: "7px",
        padding: '6px 0 !important',
        border: '1px solid rgb(47, 105, 240)',
        background: "rgb(47, 105, 240)",
        color: "#fff",
        '&:hover': {
          background: "#fff",
          color: "rgb(47, 105, 240)",
          padding: '6px 0 !important',
          border: '1px solid rgb(47, 105, 240)',
        },
      }} onClick={handleNavigateNEETAssessmentNew}>
     Start Now
    </Button>
  ) : null
              }
                {!loading && !props.data?.is_subscribed && userRole !== 'AUTH_USER' &&<Button fullWidth variant="contained" color="primary" sx={{
                  borderRadius: "7px",
                  background: "#437bfc", color: "#fff", height: "38px", '&:hover': {
                    background: "#fff",
                    color: "#437bfc",
                    border: '1px solid #437bfc',
                  }
                }} onClick={() => handleClickTrialButton(props?.data?.subscriptionplanid, 'free', props?.data?.id, props?.data)}>
                  Subscribe
                </Button>}
            </Stack>
          </CardContent>

        {/* </Grid>
      </Grid> */}
    </Box>
  );
}
const useStyles = makeStyles((theme) => ({
  trial: {
    color: 'white',
    padding: '0px 6px 0px 6px ',
    background: 'grey',
  },
  HighlightedSection: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    webkitLineClamp: '2',
    webkitBoxOrient: 'vertical',
    height: '44px'
  },
  CardView: {
    minWidth: '300px',
    maxWidth: '320px',
    paddingLeft: 0,
    paddingTop: 0,
    marginBottom: "40px",
    marginLeft: "20px",
    '@media (max-width:999px)': {
      marginBottom: '30px',
      marginLeft: "10px",
    },
    '@media (max-width:500px)': {
      maxWidth: '250px !important',
      minWidth: '250px !important',
    }
  },
  // CardViewBackground: {

  // }
  cardDescription: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#fff',
    height: '120px'
  },
  shieldDesign: {
    border: '1px solid #c1c1c1',
    background: '#dddddd69',
    borderRadius: '50%',
    padding: '5px',
    height: '33px',
    width: '33px',
  },
  coursetitle: {
    overflow: 'hidden',
    // color: 'black',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    [theme.breakpoints.down('md')]: {
      overflow: 'auto',
      WebkitLineClamp: 'initial',
      WebkitBoxOrient: 'initial',
    },


  },
  card: {
    border: '0.5px solid #DFDFDF',
    boxShadow: '0px 3px 6px  #0000001A',
    borderRadius: '6px',
    backgroundImage: `url(${BackgroundImg})`,
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'bottom right',
    // cursor: 'pointer',
    marginTop: '16px',
    marginLeft: '10px',
    width: '95%',
    // width: '85%', // Reduced from 90% to 85%
    // height: 'auto', // You can add this if needed
    // maxHeight: '300px', // Add maxHeight to limit overall card height
  },
  date: {
    marginTop: '15px',
  },
  button: {
    backgroundColor: '#F8F5F4',
    padding: '4px 6px',
    borderRadius: '6px',
    width: 'max-content',
    maxWidth: '165px',
    fontSize: '12px',
    overflow: 'hidden',
    display: 'block',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis'
  },
  title: {
    marginTop: '10px',
    fontWeight: 'bold',
    fontSize: '15px',
  },
  cardTitle: {
    fontWeight: 'bold',
    fontSize: '15px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '-webkit-line-clamp': 1,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
  },
  cardTitlenew: {
    fontWeight: 'bold',
    fontSize: '15px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    minHeight: '50px',
    maxHeight: '50px',
  },
  logo: {
    boxShadow: '0px 3px 22px #00000029',
    padding: '4px',
    backgroundColor: '#fff',
    borderRadius: '6px',
    marginTop: '10px',
  },
  description: {
    minHeight: '38px',
    fontSize: '0.8rem !important',
    overflow: 'hidden !important',
    fontFamily: 'Inter',
    textOverflow: 'ellipsis',
    '-o-text-overflow': 'ellipsis',
    '-ms-text-overflow': 'ellipsis',
    '-moz-binding': "url('ellipsis.xml#ellipsis')",
    '-ms-webkit-line-clamp': 2,
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    '& span': {
      fontSize: '0.8rem !important',
      color: 'black !important',
      backgroundColor: 'unset !important',
      fontFamily: 'Inter !important',
    },
    '& p': {
      '&:nth-child(1)': {
        display: 'block !important',
      },
      '&:nth-child(even)': {
        display: 'none ',
      },
      '&:nth-child(odd)': {
        display: 'none ',
      },
    },
  },
  enrolledUser: {
    backgroundColor: '#EBFFF8',
    borderRadius: '6px',
    padding: '4px 12px',
    fontSize: '12px',
  },
  active: {
    backgroundColor: 'green',
    borderRadius: '6px',
    padding: '4px 12px',
  },
  inActive: {
    backgroundColor: 'yellow',
    borderRadius: '6px',
    padding: '4px 12px',
  },
  Expired: {
    backgroundColor: '#FF8282',
    borderRadius: '6px',
    padding: '4px 12px',
  },
  trialButton: {
    width: 110,
    borderRadius: '6px',
    fontSize: '12px',
    backgroundColor: 'white',
    '@media (max-width: 1400px)': {
      padding: '6px 3px',
      lineHeight: '1',
    },
    '&:hover': {
      backgroundColor: 'white',
    },
    marginRight: '8px',
  },
  // CourseButton: {
  //     width: 110,
  //     color: '#00B673 ! important',
  //     border: '1px solid #00B673 ! important',
  //     borderRadius: '6px',
  //     fontSize: '12px',
  //     // marginLeft: '1rem',
  //     backgroundColor: 'white',
  //     '&:hover': {
  //         backgroundColor: 'white',
  //     },
  // },
  clamptext: {
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'normal',
    minHeight: '2.4em',
    lineHeight: '1.2em'
    //    min-height: 2.4em;                 
    //    line-height: 1.2em;  
  },

  CourseButton: {
    width: 110,
    color: '#6a6e6b', // initial text color
    border: '1px solid #6a6e6b', // initial border color
    borderRadius: '6px',
    fontSize: '12px',
    backgroundColor: 'white',
    '&:hover': {
      color: '#00B673', // text color on hover
      border: '1px solid #00B673', // border color on hover
      backgroundColor: 'white',
    },
  },
  subscribeButton: {
    width: 110,
    color: '#00B673 ! important',
    border: '1px solid #00B673 ! important',
    borderRadius: '6px',
    fontSize: '12px',
    // marginLeft: '1rem',
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: 'white',
    },
  },
}));