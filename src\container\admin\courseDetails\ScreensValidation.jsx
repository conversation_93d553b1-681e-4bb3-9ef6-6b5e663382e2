/* eslint-disable no-nested-ternary */
/* eslint-disable react/prop-types */
import React, { useMemo, useState } from 'react';
import Button from '@mui/material/Button';
import CheckIcon from '@mui/icons-material/Check'; 
import { Tooltip } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import DialogModal from '../../../components/modal/DialogModal';
import adminServices from '../../../services/adminServices';

function ScreensValidation({ moduleId,type }) {
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loadingModules, setLoadingModules] = useState(false);
  const [data, setData] = useState([]);
  const [moduleName, setModuleName] = useState('');

  console.log(data,"datadata",type);

  useMemo(()=>{
    if(type === 'combination'){
      const filtered = data.filter(item =>  item.video_count === '0');
      setData(filtered)
    }
  },[type])
  

  const handleOpenDialog = async () => {
    setIsDialogOpen(true);
    setLoadingModules(true);
    try {
      const response = await adminServices.getModuleDataValidation(moduleId,type);
      
      if (!response.ok) {
        throw new Error('Failed to fetch data');
      }
      const jsonData = await response.data;
      
      setModuleName(jsonData.length > 0 ? jsonData[0].moduleName : '');
      const finalData = type === 'combination'
      ? jsonData.filter(item => item.action_count === '0' && item.video_count === '0')
      : jsonData;

    setData(finalData);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoadingModules(false); 
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
  };

  return (
    <div>
      <Tooltip title='Validate Screens'>
        <Button
          type="button"
          variant="contained"
          startIcon={<CheckIcon />}
          sx={{ background: '#6D6969', marginLeft: '10px' }}
          onClick={handleOpenDialog}
        />
      </Tooltip>
      
      <DialogModal
        open={isDialogOpen}
        fullWidth
        maxWidth="md"
        PaperProps={{ style: { maxHeight: '80vh', height: '80vh', overflowY: 'scroll' } }}
        handleClose={handleCloseDialog}
      >
        <div>
          {!loadingModules && data && data.length > 0 ? (
            <>
              <h2 style={{ fontSize: '1.2rem', marginBottom: '0.5rem', textAlign: 'center' }}>
                Validating {moduleName}
              </h2>
              <table style={{ width: '100%', fontSize: '0.9rem', textAlign: 'center' }}>
  <thead>
    <tr>
      <th>Submodule Name</th>
      <th>Topic Name</th>
      <th>Screen Details</th>
    </tr>
  </thead>
  <tbody>
    {data.map((row, index) => (
      <tr key={index}>
        {row.message ? (
          <td colSpan={3}>
            <span style={{ color: 'red' }}>{row.message}</span>
          </td>
        ) : (
          <>
            <td>{row.subModuleName}</td>
            <td>{row.topicName}</td>
            <td>
              {row.screen && typeof row.screen === 'object' && row.screen.message ? (
                <span>{row.screen.message}</span>
              ) : (
                <>
                  {row.action_count === '0' && row.video_count === '0' ? (
                    <span style={{ color: 'red' }}>Action or Video screen missing</span>
                  ) : row.action_count === '0' ? (
                    <span style={{ color: 'red' }}>Action screen missing</span>
                  ) : row.video_count === '0' ? (
                    <span style={{ color: 'red' }}>Video screen missing</span>
                  ) : (
                    <ul>
                      {row.screen?.map((screen, idx) => (
                        <li key={idx}>{screen.id}</li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </td>
          </>
        )}
      </tr>
    ))}
  </tbody>
</table>

            </>
          ) : (
            <p style={{ textAlign: 'center', fontSize: '1.2rem', marginTop: '20px' }}>
              {loadingModules ? <CircularProgress /> : 'There are no missing screens, please proceed with the next module!!'}
            </p>
          )}
        </div>
      </DialogModal>
    </div>
  );
}

export default ScreensValidation;