/* eslint-disable consistent-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable arrow-body-style */
import React, { useEffect, useState } from 'react';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';
import { makeStyles } from '@mui/styles';
import { Box, Button, Typography, InputAdornment, Paper, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, TextField } from '@mui/material';
import MUIDataTable from 'mui-datatables';
import moment from 'moment'
import AddIcon from '@mui/icons-material/Add';
import { useSelector, useDispatch } from 'react-redux';
import { Visibility, Search } from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import Page from '../../../components/Page';
import PageHeader from '../../../components/PageHeader';
import BasicModal from '../../../components/modal/BasicModel';
import AddUser from './AddUser';
import ImportUser from './ImportUser';
import userServices from '../../../services/clientAdmin/userServices';
import clienServices from '../../../services/clientAdmin/course/clientAdminServices';
import adminServices from '../../../services/adminServices';
import DeleteAlert from '../../../components/modal/DeleteModal';
import SnackBar from '../../../components/snackbar/snackbar';
import { getIndividualClientTable } from '../../../Redux/Action'
import {EmptyUserDetails} from '../../../store/reducer'


export default function UserTable() {
  const classes = useStyles();
  const dispatch = useDispatch()
  const navigate = useNavigate();
  const details = useSelector((state) => state?.clientTableDetails);
  const clintId = useSelector((state) => state?.userInfo && state?.userInfo?.id);

  const [modalOpen, setModalOpen] = React.useState(false);
  const [importModalOpen, setImportModalOpen] = React.useState(false);
  const [mode, setMode] = React.useState('');
  const [deleteAlert, setDeleteAlert] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [deleteUserId, setDeleteUserId] = useState(null);
  const [editUserId, setEditUserId] = useState(null);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [usersData, setUsersData] = React.useState([]);
  const [userId, setUserId] = React.useState('');
  const [errorData, setErrorData] = React.useState([]);
  const [errorDataTableHide, setErrorDataTableHide] = React.useState(false);
  const [courseList, setCourseList] = useState([]);
  const [assessmentList, setAssessmentList] = useState([]);

  const [languageCode, setLanguage] = useState([]);

  const [loading, setLoading] = useState(true);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const handleModalOpen = () => {
    setModalOpen(true);
  };
  const handleModalClose = () => {
    setModalOpen(false);
  };

  const handleErrorData = (data, shown) => {
    setErrorData(data);
    setErrorDataTableHide(shown);
  };

  const handleImportModalOpen = () => {
    setImportModalOpen(true);
  };
  const handleImportModalClose = () => {
    setImportModalOpen(false);
  };

  const getUsersDetails = async () => {
    dispatch(getIndividualClientTable(page, rowsPerPage, search))
    // try {
    //   const response = await userServices.getUsersDetails(page, rowsPerPage, search);
    //   if (response.ok) {
    //     setUsersData(response.data);
    //     setLoading(false);
    //     setUserId(response.data.id);
    //   } else {
    //     setLoading(false);
    //   }
    // } catch (error) {
    //   console.log(error);
    // }
  };
  console.log(details, "details");


  useEffect(() => {
    setUsersData(details.data);
  }, [details])


  const getCourseList = async () => {
    try {
      // const response = await clienServices.getCourse();
      const response = await adminServices.getUserSubscriptionData(clintId)
      console.log(response, 'response');

      if (response.ok) {
        setCourseList(response.data);
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
    }
  };
    const getAssessmentList = async () => {
    try {
      // const response = await clienServices.getCourse();
      const response = await adminServices.getUserAssessmentSubscriptionData(clintId)
      console.log(response, 'response');

      if (response.ok) {
        setAssessmentList(response.data);
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
    }
  };


  useEffect(() => {
    getAssessmentList();
    getUsersDetails();
    getCourseList();
    getLanguageCode();
  }, [search, page, rowsPerPage]);

  const handleDeleteModalOpen = (userId) => {
    setDeleteAlert(true);
    setDeleteUserId(userId);
  };

  const handleEditModalOpen = (userId) => {
    setMode('edit');
    handleModalOpen(true);
    setEditUserId(userId);
  };

  const snakbarHandle = (message) => {
    setOpenSnackbar(true);
    setSnackbarTitle(message);
  };

  const handleDeleteProject = async () => {
    try {
      const response = await userServices.deleteUserDetailsByID(deleteUserId);
      if (response.ok) {
        getUsersDetails();
        setOpenSnackbar(true);
        setSnackbarTitle(response.data.message);
        setDeleteAlert(false);
        setLoading(false);
      } else if (response.status === 400) {
        setOpenSnackbar(true);
        setSnackbarTitle(response.data.message);
      }
    } catch (error) {
      console.log(error);
    }
  };



  const handleSearchChange = (e) => {
    setSearch(e.target.value);
  };

  const ButtonGroup = () => {
    return (
      <>
        <Box style={{ display: 'flex', justifyContent: 'end', alignItems: 'center' }}>



          <Button
            sx={{ marginRight: '10px' }}
            variant="contained"
            onClick={() => {
              handleModalOpen(true);
              setMode('create');
            }}
          >
            <AddIcon /> Add User
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              handleImportModalOpen(true);
            }}
          >
            <AddIcon />
            Import User
          </Button>
        </Box>
      </>
    );
  };

  const handleNavigation = (id) => {
    dispatch(EmptyUserDetails())
    navigate(`/app/UserProfile?id=${id}`);
  };


  const getLanguageCode = async () => {
    try {
      const response = await adminServices.getLanguageCode();
      if (response.ok) {
        setLanguage(response.data);

        return true;
      }
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <Page title="User">

      <PageHeader pageTitle={'Users'} buttonComponent={<ButtonGroup />} />
      <Box sx={{ position: "relative" }}>
        <TextField type="search" key="search-field" sx={{
          position: 'absolute', bottom: '18px',
          left: 'Calc(100% - 550px)', marginRight: '20px'
        }} value={search} id="outlined-basic" label="Search for User" onChange={handleSearchChange}
          variant="outlined" />
      </Box>
      <Paper>
        <TableContainer>
          <Table>
            <TableHead sx={{ backgroundColor: '#f2f4f8' }}>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Added On</TableCell>
                <TableCell>Courses</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {usersData && usersData?.length > 0 && usersData.map((data, idx) => (
                <TableRow key={data?.id}>
                  <TableCell>{data.name}</TableCell>
                  <TableCell>
                    <span style={{
                      color: data.status === "ACTIVE" ? 'green' : 'red',
                      fontWeight: 'bold'
                    }}>
                      {data.status}
                    </span>
                  </TableCell>
                  <TableCell>{data.status === "ACTIVE" ? moment(data.lastUpdatedDate).format('MM-DD-YYYY HH:mm:ss'):'-'}</TableCell>
                  <TableCell>{moment(data.createdDate).format('MM-DD-YYYY HH:mm:ss')}</TableCell>
                  <TableCell>{data.course?.length || 0}</TableCell>
                  <TableCell align="center">
                    <IconButton color="primary" onClick={() => handleNavigation(data.id)}>
                      <Visibility />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          component="div"
          count={details?.total}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Paper>

      <BasicModal
        openModel={modalOpen}
        title={mode === 'create' ? 'Add User' : 'Edit User'}
        closeModel={() => setModalOpen(false)}
        children={
          <AddUser
            courseList={courseList}
            assessmentList={assessmentList}
            languageCode={languageCode}
            mode={mode}
            getUsersDetails={() => getUsersDetails()}
            handleModalClose={() => handleModalClose()}
            id={editUserId}
            snackBarControl={snakbarHandle}
          />
        }
      />

      <BasicModal
        openModel={importModalOpen}
        title={'Import User'}
        closeModel={() => setImportModalOpen(false)}
        children={
          <ImportUser
            handleImportModalClose={() => handleImportModalClose()}
            getUsersDetails={() => getUsersDetails()}
            snackBarControl={snakbarHandle}
            errorData={handleErrorData}
          />
        }
      />
    </Page>
  );
}

const useStyles = makeStyles({
  name: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    maxWidth: '155px',
    '&:hover': {
      textOverflow: 'clip',
      whiteSpace: 'normal',
      wordBreak: 'break-all',
    },
  },
  emailText: {
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    maxWidth: '215px',
    '&:hover': {
      textOverflow: 'clip',
      whiteSpace: 'normal',
      wordBreak: 'break-all',
    },
  },
});
