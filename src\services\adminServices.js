import client from './apiClient';



const getLookUpData = (key,clientId) => client.get(`/lookup?key=${key}&clientId=${clientId}`);

const getSubcategoryLookUp = (key, code) => client.get(`/lookup?key=${key}&&code=${code}`);

const getLookUpStateData = (key, id,code) => client.get(`/lookup?key=${key}&Id=${id}&code=${code}`);

const getAuthors = () => client.get(`/user/getAuthors`);
const getLanguageCode = () => client.get(`/user/getLanguageCode`);
const getLoginPageImage = () => client.get(`/user/getLoginImage`);



const getClientList = () => client.get(`/client`);
const getContentWriterList = () => client.get(`/individual/user/contentwriter`);
const postContentWriterList = (data) =>client.post(`/individual/user/postcontentwriter`,data);
const putContentWriterList = (data,id) =>client.put(`/individual/user/updateContentcreater?id=${id}`,data)
const deleteContentWriterList = (id) =>client.delete(`/individual/user/deleteContentWriter?Id=${id}`)
const getIndividualList = (page, pageCount, sortOrder, sort, search) =>
  client.get(
    `/individual/user?page=${page}&pageCount=${pageCount}&sortOrder=${sortOrder}&sort=${sort}&search=${search}`
  );

  const getReferenceFileDetails = (courseSubModuleId,submodulename) => client.get(`/course/submodule/getRefFile?courseSubModuleId=${courseSubModuleId}&submodulename=${submodulename}`)
  
const getCourseLists = (page, search, pageCount) =>
  client.get(`/course?&page=${page}&search=${search}&pageCount=${pageCount}`);

const getAllCourseList = () =>
  client.get(`/course/screen/allCourseList`);

const deleteCourseVideo = (id) => client.delete(`/course/video?courseId=${id}`);

const getCourseDetailsById = (courseId) => client.get(`/course?courseId=${courseId}`);

const getCourseDetailsByIdOne = (clientId) => client.get(`/client?clientId=${clientId}`);

const getCourseContent = (id) => client.get(`/course?courseId=${id}`);

const getCourseModule = (courseId) => client.get(`/course/module/submodule?courseId=${courseId}`);

const deleteCourseModule = (courseId, moduleId) =>
  client.delete(`/course/module?courseId=${courseId}&&courseModuleId=${moduleId}`);

const getCourseModuleTopics = (subModuleId) =>
  client.get(`/course/subModuleTopics/screen?courseSubModuleId=${subModuleId}`);

const editCourseDetailsByIdOne = (clientId, data) => client.put(`/client?clientId=${clientId}`, data);

const editCourseModuleName = (courseId, moduleId, data) =>
  client.put(`/course/module?courseId=${courseId}&&courseModuleId=${moduleId}`, data);

const editCourseSubModuleName = (moduleId, submoduleId, data,courseId) =>
  client.put(`/course/subModule?courseModuleId=${moduleId}&&courseSubModuleId=${submoduleId}&&courseId=${courseId}`, data);

const createCourse = (data) => client.post(`/course`, data);

const editCourseDetails = (courseId, data) => client.put(`/course?courseId=${courseId}`, data);

const createCourseModule = (courseId, data) => client.post(`/course/module?courseId=${courseId}`, JSON.stringify(data));

const createCourseSubModule = (moduleId, data,courseId) => client.post(`/course/subModule?courseModuleId=${moduleId}&&courseId=${courseId}`, data);

const createCourseTopic = (subModuleId, data,courseId) =>
  client.post(`/course/subModuleTopics?courseSubModuleId=${subModuleId}&&courseId=${courseId}`, JSON.stringify(data));

const deleteSubModules = (moduleId, subModuleId) =>
  client.delete(`/course/subModule?courseModuleId=${moduleId}&&courseSubModuleId=${subModuleId}`);

const editSubModuleTopicName = (moduleId, topicId, data,courseId) =>
  client.put(
    `/course/subModuleTopics?courseSubModuleId=${moduleId}&&courseSubModuleTopicId=${topicId}&&courseId=${courseId}`,
    JSON.stringify(data)
  );

const deleteSubModuleTopic = (moduleId, topicId) =>
  client.delete(`/course/subModuleTopics?courseSubModuleId=${moduleId}&&courseSubModuleTopicId=${topicId}`);

const createIntroScreenDetails = (topicId, data,courseId) => client.post(`/course/screen/intro?topicId=${topicId}&&courseId=${courseId}`, data);

const updateIntroScreenDetails = (topicId, screenId, data,courseId) =>
  client.put(`/course/screen/intro?topicId=${topicId}&&screenId=${screenId}&&courseId=${courseId}`, data);

const updateActionScreenDetails = (topicId, screenId, data, courseId) =>
  client.put(`/course/screen/action?topicId=${topicId}&&screenId=${screenId}&courseId=${courseId}`, data);

const updateVideoScreenDetails = (topicId, screenId, data, courseId) =>
  client.put(`/course/screen/video?topicId=${topicId}&&screenId=${screenId}&courseId=${courseId}`, data);

const updateSuccessScreenDetails = (topicId, screenId, data,courseId) =>
  client.put(`/course/screen/success?topicId=${topicId}&&screenId=${screenId}&&courseId=${courseId}`, data);

const createActionScreenDetails = (topicId, data, courseId) =>
  client.post(`/course/screen/action?topicId=${topicId}&courseId=${courseId}`, data);

const createVideoScreenDetails = (topicId, data, courseId) =>
  client.post(`/course/screen/video?topicId=${topicId}&courseId=${courseId}`, data);

const createSuccessScreenDetails = (topicId, data,courseId) => client.post(`/course/screen/success?topicId=${topicId}&&courseId=${courseId}`, data);

const getIntroScreenDetailsById = (topicId, screenId) =>
  client.get(`/course/screen/intro?topicId=${topicId}&&screenId=${screenId}`);

const getActionScreenDetailsById = (topicId, screenId, actionType) =>
  client.get(`/course/screen/action?topicId=${topicId}&&screenId=${screenId}&&actionType=${actionType}`);

const getVideoScreenDetailsById = (topicId, screenId) =>
  client.get(`/course/screen/video?topicId=${topicId}&&screenId=${screenId}`);

const getSuccessScreenDetailsById = (topicId, screenId) =>
  client.get(`/course/screen/success?topicId=${topicId}&&screenId=${screenId}`);

const deleteScreenById = (topicId, screenId) =>
  client.delete(`/course/screen/?topicId=${topicId}&&screenId=${screenId}`);

const postClientDetails = (data) => client.post(`/client`, data);

const editClientDetailsByID = (clientId, data) => client.put(`/client?clientId=${clientId}`, data);

const getClientDetalsById = (clientId) => client.get(`/client?clientId=${clientId}`);

const getDashBoardValues = () => client.get(`/dashboard`);

const getDashBoardChart = (fromDate, toDate) =>
  client.get(`/dashboard/individual/userDetails?fromDate=${fromDate}&toDate=${toDate}`);

const getSATDashBoard = (searchedDetails,page,rowsPerPage,sortOption ) => client.get(`/dashboard/SATData?search=${searchedDetails}&page=${page}&rowsPerPage=${rowsPerPage}&sortOption=${sortOption}`);

const getLookUpDetails = (key) => client.get(`/lookup?key=${key}`);

const deleteClientDetailsByID = (clientId) => client.delete(`/client?clientId=${clientId}`);

const postCreateAdmin = (clientId, data) => client.post(`/client/user?clientId=${clientId}`, data);

const getClientInfo = (clientId) => client.get(`/client?clientId=${clientId}`);

const getusers = (clientId,page,rowsPerPage) => client.get(`/client/users?clientId=${clientId}&&page=${page}&size=${rowsPerPage}&search=`);
const deleteUserDetails = (clientId, userId) => client.delete(`/client/user?clientId=${clientId}&&userId=${userId}`);
const handleClientStatus = (clientId, data) =>
client.patch(`/client/clientStatus?clientId=${clientId}`, JSON.stringify(data));
const handleUserStatus = (clientId, userId, data) =>
client.patch(`/client/userStatus?clientId=${clientId}&&userId=${userId}`, JSON.stringify(data));
const profileUpdate = (data) => client.put('/authenticate/profile', data);
const getSubscriptionData = (clientId) => client.get(`/subscription/details?clientId=${clientId}`);
const getUserSubscriptionData = (clientId) => client.get(`/subscription/userdetails?clientId=${clientId}`);
const getLLMInteractionLog = () => client.get(`/subscription/llmInteractionLog`);
const getLLMInteractionLogByUserId = (userId) => client.get(`/subscription/llmInteractionLogByUserId?userId=${userId}`);
const editLLMInteractionLogReviewStatus = (userId,status) => client.put(`/subscription/llmInteractionLogEditReviewStatus?userId=${userId}&status=${status}`);
const deleteLLMInteractionLog = (userId) => client.delete(`/subscription/llmInteractionLogEditReviewStatus?userId=${userId}`);
const getUserAssessmentSubscriptionData = (clientId) => client.get(`/subscription/userassessmentdetails?clientId=${clientId}`);



const postSubsrciptionplan = (clientId, data) =>
  client.post(`/subscription/client?clientId=${clientId}`, JSON.stringify(data));

const editSubsrciptionplan = (id, data) => client.put(`/subscription/client?id=${id}`, JSON.stringify(data));

const deleteSubsrciptionplanByID = (id) => client.delete(`/subscription/client?id=${id}`);

const getSubscriptionCoursePlansByID = (id) => client.get(`/subscription/client?id=${id}`);

const handleSubscriptionStatus = (subscriptionCoursePlanId, data) =>
  client.patch(`/subscription/coursePlan?subscriptionCoursePlanId=${subscriptionCoursePlanId}`, JSON.stringify(data));

const deleteActionScreenKeys = (id) => client.delete(`/course/screen/action/key?keyId=${id}`);

const createActionScreenKeys = (topicId, screenId, data) =>
  client.post(`/course/screen/action/key?topicId=${topicId}&&screenId=${screenId}`, data);

const createActionScreenKeysForCaseStudy = (screenId, data) =>
  client.post(`/course/screen/action/key?screenId=${screenId}&&topicId`, data);

const deleteCourseById = (id) => client.delete(`/course?courseId=${id}`);

const updateModulePositions = (courseId, data) =>
  client.put(`/course/module/positionIndex?courseId=${courseId}`, JSON.stringify(data));

const updateTopicPositions = (id, data) =>
  client.put(`/course/subModuleTopics/positionIndex?courseSubModuleId=${id}`, JSON.stringify(data));

const updateCourseStatus = (courseId, data) =>
  client.patch(`/course/status?courseId=${courseId}`, JSON.stringify(data));

const getCourseStatus = (key) => client.get(`/lookup?key=${key}`);

const updateSubModulePositions = (moduleId, data) =>
  client.put(`/course/subModule/positionIndex?courseModuleId=${moduleId}`, JSON.stringify(data));

const updateSubModuleParent = (from , to, submoduleId) =>
    client.put(`/course/subModule/updateSubModuleParent?from=${from}&&to=${to}&&submoduleId=${submoduleId}`)

const updateScreenPositions = (topicId, data) =>
  client.put(`/course/screen/positionIndex?topicId=${topicId}`, JSON.stringify(data));

const courseTrial = (data) => client.post(`/course/trial`, data);

const getAdminUserList = (courseId, clientId) => client.get(`/lookup/users?courseId=${courseId}&clientId=${clientId}`);

const enrollUserToCourse = (clientId, courseId, data) =>
  client.post(`/user/enroll?clientId=${clientId}&courseId=${courseId}`, JSON.stringify(data));

const enrollNewUser = (clientId, courseId, data) =>
  client.post(`/user/course/enroll?clientId=${clientId}&courseId=${courseId}`, JSON.stringify(data));
const handleDeleteModuleVideo = (id) => client.delete(`/course/module/video?courseModuleId=${id}`);

const getChatResult = (data) => 
  client.post(`/user/getChatResponse`, JSON.stringify(data));

const handleDeleteSubmoduleVideo = (id) => client.delete(`/course/subModule/video?subModuleId=${id}`);

const handleDeleteScreenAudio = (id) => client.delete(`/course/screen/audio?screenId=${id}`);
const getModuleDataValidation=(moduleId,type)=>client.patch(`/course/mstatus?moduleId=${moduleId}&&type=${type}`);

const getEnrolledUser = (courseId, clientId, clientPlanId) =>
  client.get(`/user/enroll?courseId=${courseId}&clientId=${clientId}&clientPlanId=${clientPlanId}`);

const deleteEnrolledUser = (planId, courseId) => client.delete(`/user/enroll?planId=${planId}&userId=${courseId}`);

const postCloningCourses = (courseId, ownerId, authors, data) =>
  client.post(`/course/clone?courseId=${courseId}&authors=${authors}&userId=${ownerId}`, JSON.stringify(data));

const importModules = (courseId, fromCourseId,ownerId, moduleData) =>
  client.post(`/course/import?tocourseId=${courseId}&fromcourseId=${fromCourseId}&authuserid=${ownerId}`, JSON.stringify(moduleData));

const createReference=(moduleId,subModuleName,data)=>
  client.post(`/course/submodule/addRefFile?courseModuleId=${moduleId}&subModuleName=${subModuleName}`,data);
const editCourseSubModuleRef = (moduleId, subModuleName, data) =>
  client.put(`/course/subModule/editRef?courseModuleId=${moduleId}&&subModuleName=${subModuleName}`, data);
const deletreferenceDetails=(subModuleId,referenceId)=>
  client.delete(`course/subModule/deleteRef?subModuleId=${subModuleId}&referenceid=${referenceId}`);

const updateisCompleteResult =(data) =>
  client.post(`user/videoResult`, data);

const createIntroTemplate=(templatename,data)=>
  client.post(`/course/screen/addTemplate?templatename=${templatename}`,data);
const getIntroTemplate = () => client.get(`/course/screen/getIntroTemplate`);

const putIntroTemplate=(templateId,data)=>
  client.put(`/course/screen/editTemplate?templateId=${templateId}`,data);

const deleteTemplate=(templateId)=>
  client.delete(`/course/screen/deleteTemplate?templateId=${templateId}`);

const createQuestions=(data)=>
  client.post(`/course/createQuestions`,data);

const getQuestionData = (values) => client.get('/course/getquestions', { values })

const getQuestionDataMCQ = (values) => client.get('/course/getquestionsMcq', { values })

const getQuestionDataMCQNew = (searchQuery) => client.get(`/course/getquestionsMcq?values[]=${searchQuery}`)



const createCourseAssessMent = (subModuleId,from, data) =>  
client.post(`/course/postassessment?courseSubModuleId=${subModuleId}&from=${from}`, JSON.stringify(data));

const getDetailsById = (subModuleId,from) => client.get(`/course/getquestionsbasedoncourseId?courseSubModuleId=${subModuleId}&from=${from}`);

const UpdateCourseAssessMentBasedonId = (subModuleId,from, data) => client.put(`/course/updateassessment?courseSubModuleId=${subModuleId}&from=${from}`, data);

const genericAssessMent = (data) =>  client.post(`/course/postgenericassessment`, data);
const CognitiveSkillGA = (data) =>  client.post(`/course/CognitiveSkillGA`, data);
const getCognitiveSkillGA = (search,page,limit) =>  client.get(`/course/getCognitiveSkillGA?search=${search}&page=${page}&limit=${limit}`);
const CognitiveSkillGAUpdate = (id,data) =>  client.put(`/course/updateCognitiveSkillGA?Id=${id}`,data);
const getSATAssessmentAdmin = (search,page,limit) =>  client.get(`/course/getSATAssessmentAdmin?search=${search}&page=${page}&limit=${limit}`);
const getNEETAssessmentAdmin = (search,page,limit) =>  client.get(`/course/getNEETAssessmentAdmin?search=${search}&page=${page}&limit=${limit}`);
const getNEETSubjectList = () =>  client.get(`/course/getNEETSubjectList`);
const getNEETChaptersList = (id) =>  client.get(`/course/getNEETChaptersList?id=${id}`);


const getGenericAssessment = (search,category,page,rowsPerPage) =>  client.get(`/course/getgenericassessments?search=${search}&category=${category}&page=${page}&size=${rowsPerPage}`);

const updategenericAssessMent = (data,editid) => client.put(`/course/updategenericassessment?id=${editid}`,data)
const deleteGenericAssessment=(assessmentId)=> client.delete(`/course/deletegeneralassessment?Id=${assessmentId}`);
const getBasicDetails = (language) => client.get(`/individual/user/getDropdownValue?language=${language}`)
const getBannerDetails = () => client.get('/individual/user/getBannerDetails')
const getLearnerViewing = (language) => client.get(`/individual/user/getLearnerViewingDetails?language=${language}`)
const UpdateGeneralAssessmentStatus = (id,data,userMessage,type) => client.put(`/course/updateassessmenttoggle?id=${id}&is_published=${data}&type=${type}`,userMessage);
const getAllQuestion = (page, pageCount, search,questionType) => client.get(`/course/getAllQuestions?size=${page}&pageCount=${pageCount}&search=${search}&question_type=${questionType}`);
const getAllNEETQuestion = (page, pageCount, search, type) => client.get(`/course/getAllNEETQuestions?size=${page}&pageCount=${pageCount}&search=${search}&type=${type}`);
const deleteQuestion = (questionId) => client.put(`/course/deleteQuestionByID?questionId=${questionId}`); 
const Updateusercreation = (id,data) => client.put(`/individual/user/updatedetails?Id=${id}`,data)
// const getUserProfileDetails = (id) => client.get(`/individual/user/userProfile/${id}`);
const getUserProfileDetails = (id) => client.get(`/individual/user/getUserProfileDetails?userId=${id}`)
const postDropDownValue = (data) =>  client.post(`/individual/user/postDropDownValue`,data);
const postEmailTemplate = (data) =>  client.post(`/individual/user/postEmailTemplate`,data);
const putdropDownValue = ( data) =>  client.put(`/individual/user/putDropDownValue`, data);
const deleteDropDownValue = ( tableType,id) =>  client.delete(`individual/user/deleteDropDownValue?questionId=${id}&type=${tableType}`);
const postCategoryValue = (data) =>  client.post(`/individual/user/postCategoryValue`,data);
const putCategoryValue = ( data) =>  client.put(`/individual/user/putCategoryValue`, data);
const postSUbCategoryValue = (data) =>  client.post(`/individual/user/postSubCategoryValue`,data);
const putSubCategoryValue = ( data,id) =>  client.put(`/individual/user/putSubCategoryValue?id=${id}`, data);
const deleteCategoryValue = ( id) =>  client.delete(`individual/user/deleteCategoryValue?questionId=${id}`);
const deleteSubCategoryValue = ( id) =>  client.delete(`individual/user/deleteSubCategoryValue?questionId=${id}`);

const updateQuestion = (questionId, data) => client.put(`/course/updateQuestionByid?questionId=${questionId}`,data);
const getAllGeneralAssessment1 = () => client.get(`/course/getallgeneralassessment1`);
const getSerchedCourse = (data) => client.get(`/user/enroll/course/searchedCourse?search=${data}`)
const postTagValue = (data) =>  client.post(`/individual/user/postTagValue`,data);
const deleteTagValue = ( id) =>  client.delete(`individual/user/deleteTagValue?questionId=${id}`);
const putTagValue = ( data) =>  client.put(`/individual/user/putTagValue`, data);
const getCategoryLists = () => client.get('/individual/user/getCategoryValue');
const getTagLists = () => client.get('/individual/user/getTagValue');
const UpdateSATQuestionsBasedOnId = (questionId, data) => client.put(`/course/UpdateSATQuestionsBasedOnId?questionId=${questionId}`,data);
const postAssessmentStatus = (data) => client.post('/individual/user/postassessmentstatus',data);
const postAssessmentSubmit = (data) => client.post('/individual/user/assessmentresult',data);
const getAssessmentDetails = (id,assessmentId) => client.get(`/individual/user/getallassessmentdetailsbyId?assessmentId=${assessmentId}&UserId=${id}`);
const getGenericAssessmentDropDown = () =>  client.get(`/course/getgenericassessmentsdropdown`);
const getSummaryById = (id,assessmentId,attemptCount) => client.get(`/individual/user/getSummaryById?assessmentId=${assessmentId}&UserId=${id}&attemptCount=${attemptCount}`);
const getAssessmentDetailsById = (id) => client.get(`/individual/user/getAllAssessmentByUserId?UserId=${id}`);
const deleteAssessmentById = (userid,assessmentId) => client.put(`/individual/user/deleteAllAssessmentByUserId?UserId=${userid}&&assessmentId=${assessmentId}`);
const getAssessmentCountById = (userid,assessmentId) => client.get(`/individual/user/assessmentresultcount?UserId=${userid}&&assessmentId=${assessmentId}`);
const deleteGeneralAssessment=(assessmentId)=> client.delete(`/course/deleteCognitiveSkillGAT?Id=${assessmentId}`);
const getCourseBasedOnId = (Id) => client.get(`/lookup/client/getCourseForAssessmentSelection?id=${Id}`);
const getStartAssessmentData = (assessmentId) => client.get(`/individual/user/getStartAssessment?id=${assessmentId}`);
const getAssessmentData = (assessmentId) => client.get(`/course/editDataCognitiveSkillGA?id=${assessmentId}`);
const createSatQuestion = (data) => client.post('/course/createSATQuestions',data);
const getSATDetails = (page,pageCount,search) => client.get(`/course/getAllSATQuestions?size=${pageCount}&pageCount=${page}&search=${search}`);
const deleteSATDetails=(id,type)=> client.put(`/course/deleteSATQuestionByID?questionId=${id}&type=${type}`);
const SatCreation = (data) => client.post('/course/SATAssessment',data);
const SatQuestionupload = (data,id) => client.post(`/course/createSATQuestionsupload?id=${id}`,data);
const NEETCreation = (data) => client.post('/course/NEETAssessment',data);
const deleteNEETAssessment=(id)=> client.put(`/course/deleteNEETAssessment?id=${id}`);
const getNEETAssessmentStartData = (assessmentId) => client.get(`/individual/user/getNEETAssessmentTestQuestions?id=${assessmentId}`);
const getSatQuestion = (type,searchData,page) => client.get(`/course/getQuestionByQuestionType?type=${type}&search=${searchData}&page=${page}`);
const getStartSATAssessment = (assessmentId,userId,type) => client.get(`/individual/user/getStartSATAssessment?id=${assessmentId}&userId=${userId}&type=${type}`);
const postSATResult = (data) => client.post('/individual/user/SATResult',data);
const getSatDetailsById = (assessmentId) => client.get(`/course/editDataCognitiveSkillGA?id=${assessmentId}`);
const updateSatAssessment = (data,id) => client.put(`/course/updateSATAssessment?id=${id}`,data);
const IRTSatAssessment = (id) => client.post(`/course/updateIRT?id=${id}`);
const getNEETDetailsById = (assessmentId) => client.get(`/course/geteditDataNEET?id=${assessmentId}`);
const updateNEETAssessment = (id,data) => client.put(`/course/updateNEETAssessment?id=${id}`,data);
const updateNEETAssessmentData = (id,data) => client.put(`/course/updateNEETAssessmentData?id=${id}`,data);
const postNEETResult = (data) => client.post('/individual/user/NEETResult',data);



const getSatAnalysis = (assessmentId,userId,attemptCount,type,index) => client.get(`/individual/user/getAnalysis?id=${assessmentId}&userId=${userId}&attemptCount=${attemptCount}&type=${type}&index=${index}`);
const getAnalysisTestScore = (assessmentId,userId,attemptCount) => client.get(`/individual/user/SATTestScore?id=${assessmentId}&userId=${userId}&attemptCount=${attemptCount}`);
const getSatAssessmentCountById = (userid,assessmentId) => client.get(`/individual/user/SATAssessmentCount?UserId=${userid}&&assessmentId=${assessmentId}`);
const postImag = (data) => client.post('/course/screen/uploadImage',data);
const getSatAssessmentResume = (userid, percentage,assessmentId,type,QuestionId) => client.get(`/individual/user/SATResultget?UserId=${userid}&&percentage=${percentage}&&assessmentId=${assessmentId}&&type=${type}&&QuestionId=${QuestionId}`);
const updateSatAssessmentModule = (data,id,authUserId) => client.put(`/course/updateSATAssessmentModule?id=${id}&authUserId=${authUserId}`,data);
const deleteSATAssessmentModule = ( moduleId,assessmentId, questionIdToRemove) => client.delete(`/course/deleteSATAssessmentModule?id=${assessmentId}&moduleId=${moduleId}`,questionIdToRemove);
const getQuestionDetails=(id)=> client.get(`/course/getQuestionDetails?id=${id}`);
const ExcelUploads = (data) => client.post(`/course/screen/uploadFile`,data);
const getQuestions=(id)=> client.get(`/course/getQuestions?id=${id}`);
 const ChatBotUploads = (data) => client.post(`/course/screen/uploadChatBoatFiles`,data);
const updateNEETAssessmentModule = (id,data,authUserId) => client.put(`/course/updateNEETAssessmentModule?id=${id}&authUserId=${authUserId}`,data);


const getMultipleQuestionDetails=(data)=> client.get(`/course/multiplequestiondetailsget?data=${data}`);
const getQuestionFullDetails=(data)=> client.get(`/course/questiondetailsget?data=${data}`);
const screenlevelTest=(data)=> client.post(`/individual/user/screenLevelResult`,data);
const UpdateGeneralAssessmentMaintenance=(id,data,type)=>  client.put(`/course/updateassessmenttoggle?id=${id}&is_published=${data}&type=${type}`)
const UpdateChatStatus = (id,userId,data,suggestion) => client.put(`/individual/user/updateChatStatus?Id=${id}&&userId=${userId}&&data=${data}&&suggestion=${suggestion}`)
const getNEETAssessmentData = (search,page,limit) =>  client.get(`/course/getNEETAssessmentData?search=${search}&page=${page}&limit=${limit}`);
const NEETAssessmentCreation = (data) => client.post('/course/createNEETAssessment',data);
  

const createPassage = (data) => client.post('/course/createPassage',data);  
const getPassage= (page,size) =>  client.get(`/course/getPassage?page=${page}&size=${size}`);
const deletePassage=(id)=> client.put(`/course/deletePassageById?id=${id}`);




export default {
  getQuestions,
  updateisCompleteResult,
  createIntroTemplate,
  getIntroTemplate,
  putIntroTemplate,
  deleteTemplate,
  Updateusercreation,
  createQuestions,
  getModuleDataValidation,
  getLookUpData,
  getSubcategoryLookUp,
  getClientList,
  getIndividualList,
  createCourse,
  getCourseLists,
  deleteCourseVideo,
  getChatResult,
  getCourseContent,
  getCourseModule,
  getCourseDetailsById,
  getReferenceFileDetails,
  getCourseModuleTopics,
  deleteCourseModule,
  editCourseDetails,
  createCourseModule,
  editCourseModuleName,
  editCourseSubModuleName,
  createCourseSubModule,
  createCourseTopic,
  deleteSubModules,
  editSubModuleTopicName,
  createActionScreenDetails,
  createVideoScreenDetails,
  createSuccessScreenDetails,
  getVideoScreenDetailsById,
  getActionScreenDetailsById,
  getIntroScreenDetailsById,
  getSuccessScreenDetailsById,
  deleteScreenById,
  deleteSubModuleTopic,
  getLookUpStateData,
  createIntroScreenDetails,
  postClientDetails,
  editClientDetailsByID,
  getClientDetalsById,
  getLookUpDetails,
  deleteClientDetailsByID,
  postCreateAdmin,
  getClientInfo,
  getusers,
  deleteUserDetails,
  updateIntroScreenDetails,
  updateVideoScreenDetails,
  updateSuccessScreenDetails,
  handleClientStatus,
  handleUserStatus,
  profileUpdate,
  getSubscriptionData,
  postSubsrciptionplan,
  deleteSubsrciptionplanByID,
  editSubsrciptionplan,
  getSubscriptionCoursePlansByID,
  updateActionScreenDetails,
  handleSubscriptionStatus,
  getCourseDetailsByIdOne,
  editCourseDetailsByIdOne,
  deleteActionScreenKeys,
  createActionScreenKeys,
  deleteCourseById,
  getDashBoardValues,
  getDashBoardChart,
  updateModulePositions,
  updateTopicPositions,
  updateCourseStatus,
  getCourseStatus,
  updateSubModulePositions,
  updateSubModuleParent,
  updateScreenPositions,
  createActionScreenKeysForCaseStudy,
  courseTrial,
  postCloningCourses,
  getAdminUserList,
  enrollUserToCourse,
  enrollNewUser,
  handleDeleteModuleVideo,
  handleDeleteSubmoduleVideo,
  getEnrolledUser,
  deleteEnrolledUser,
  handleDeleteScreenAudio,
  getAuthors,
  getLanguageCode,
  importModules,
  createReference,
  editCourseSubModuleRef,
  deletreferenceDetails,
  getQuestionData,
  getQuestionDataMCQ,
  getQuestionDataMCQNew,
  createCourseAssessMent,
  getDetailsById,
  UpdateCourseAssessMentBasedonId,
  genericAssessMent,
  getAllQuestion,
  updateQuestion,
  deleteQuestion,
  getGenericAssessment,
  updategenericAssessMent,
  deleteGenericAssessment,
  postContentWriterList,
  putContentWriterList,
  deleteContentWriterList,
  getContentWriterList,
  getBasicDetails,
  getUserProfileDetails,
  postEmailTemplate,
  postDropDownValue,
  deleteDropDownValue,
  putdropDownValue,
  postCategoryValue,
  putCategoryValue,
  deleteCategoryValue,
  deleteSubCategoryValue,
  postTagValue,
  getAllGeneralAssessment1,
  getSerchedCourse,
  getBannerDetails,
  getLearnerViewing,
  getAllCourseList,
  getCategoryLists,
  getTagLists,
  deleteTagValue,
  putTagValue,
  UpdateGeneralAssessmentStatus,
  postAssessmentStatus,
  postAssessmentSubmit,
  getAssessmentDetails,
  getGenericAssessmentDropDown,
  getSummaryById,
  getAssessmentDetailsById,
  deleteAssessmentById,
  getAssessmentCountById,
  CognitiveSkillGA,
  getCognitiveSkillGA,
  CognitiveSkillGAUpdate,
  deleteGeneralAssessment,
  getCourseBasedOnId,
  getStartAssessmentData,
  getAssessmentData,
  createSatQuestion,
  getSATDetails,
  deleteSATDetails,
  SatCreation,
  SatQuestionupload,
  getSatQuestion,
  getStartSATAssessment,
  getSATAssessmentAdmin,
  postSATResult,
  getSatDetailsById,
  updateSatAssessment,
  IRTSatAssessment,
  getSatAnalysis,
  getAnalysisTestScore,
  getSatAssessmentCountById,
  postImag,
  getSatAssessmentResume,
  UpdateSATQuestionsBasedOnId,
  updateSatAssessmentModule,
  deleteSATAssessmentModule,
  getQuestionDetails,
  ExcelUploads,
  getMultipleQuestionDetails,
  getSATDashBoard,
  getQuestionFullDetails,
  screenlevelTest,
  getUserSubscriptionData,
  postSUbCategoryValue,
  putSubCategoryValue,
  getLLMInteractionLog,
  getLLMInteractionLogByUserId,
  editLLMInteractionLogReviewStatus,
  deleteLLMInteractionLog,
  ChatBotUploads,
  UpdateGeneralAssessmentMaintenance,
  NEETCreation,
  getNEETAssessmentAdmin,
  getNEETDetailsById,
  updateNEETAssessmentModule,
  updateNEETAssessment,
  getUserAssessmentSubscriptionData,
  getNEETSubjectList,
  getNEETChaptersList,
  UpdateChatStatus,
  NEETAssessmentCreation,
  createPassage,
  getPassage,
  deletePassage,
  getNEETAssessmentData,
  updateNEETAssessmentData,
  getAllNEETQuestion,
  deleteNEETAssessment,
  getLoginPageImage,
  getNEETAssessmentStartData,
  postNEETResult
};
