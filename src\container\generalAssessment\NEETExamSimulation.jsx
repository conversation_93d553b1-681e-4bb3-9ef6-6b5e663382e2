/* eslint-disable no-unused-vars */
/* eslint-disable react/no-danger */
/* eslint-disable consistent-return */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import { Box, Grid, Typography, Button, Paper, Tooltip, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, IconButton } from '@mui/material'; import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Popover from '@mui/material/Popover';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import BookmarkBorderIcon from "@mui/icons-material/BookmarkBorder";
import "react-circular-progressbar/dist/styles.css";
import { useDispatch, useSelector } from 'react-redux';
import LaunchIcon from '@mui/icons-material/Launch';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import CoffeeIcon from "@mui/icons-material/Coffee";
import InsightsIcon from "@mui/icons-material/Insights";
import PsychologyIcon from "@mui/icons-material/Psychology";
import Page from '../../components/Page';
import adminServices from '../../services/adminServices';
import LottieLoading from '../../components/LottieLoading';
import Snackbar from '../../components/snackbar/snackbar';
import TestComplete from '../../assets/Images/TestComplete.png'
import { FromGeneralAssessmentView } from '../../store/reducer'
import brakeTimeImg from "../../assets/Images/breakTime.gif";
import './index.css';

const SatAssessment = ({ fromIndex, details, onCallBack, resumeData, resumeLength }) => {
  const location = useLocation()
  const assessmenttitle = location.state?.details?.title
  const assessmentId = location?.state?.id
  const dispatch = useDispatch()
  const allassessmentdetails = useSelector((state) => state);
  const { userInfo } = useSelector((state) => state);
  const [isLoading, setIsLoading] = useState(false);
  const [allDetails, setAllDetails] = useState('');
  const [questions, setQuestions] = useState([]);
  const [questionsall, setQuestionsall] = useState([]);

  const [currentIndex, setCurrentIndex] = useState(0);
  const navigate = useNavigate();
  const [isTimeout, setIsTimeout] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [openQuestionList, setOpenQuestionList] = useState(false);
  const [questionTimes, setQuestionTimes] = useState([]);
  const [startTime, setStartTime] = useState('');
  const [questionType, setQuestionType] = useState('Physics');
  const [time, setTime] = useState(0);

  const [gridLeft, setGridLeft] = useState(6);
  const [gridRight, setGridRight] = useState(6);
  const [parsedOptions, setParsedOptions] = useState([]);
  const [submodule, setSubmodule] = useState(1);
  const [testView, setTestView] = useState(false);
  const [resumeTime, setResumeTime] = useState(false);
  const [timeLeft, setTimeLeft] = useState(600);
  const [correctAnswer, setCorrectAnswer] = useState([]);

  const [isopenSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarTitle, setSnackbarTitle] = useState('');
  const [showModuleComplete, setShowModuleComplete] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [markedForReview, setMarkedForReview] = useState({
    physics: {},
    chemistry: {},
    biology: {}
  });
  const [breakTimeLeft, setBreakTimeLeft] = useState(600);
  const [overlayZIndex, setOverlayZIndex] = useState('none');
  const [questionListAnchorEl, setQuestionListAnchorEl] = useState(null);
  const [showCongratulationsModal, setShowCongratulationsModal] = useState(false);

  const getCurrentTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');

    return `${hours}:${minutes}:${seconds}`;
  };

  useEffect(() => {
    const handleBeforeUnload = () => {
      dispatch(FromGeneralAssessmentView(false));
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    const handlePopState = () => {
      dispatch(FromGeneralAssessmentView(false));
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);


  // useEffect(() => {
  //   if(questionType === 'Biology'){
  //     setTime(5360)
  //   }
  //   else{
  //     setTime(2680)
  //   }
  //     setIsLoading(true);
  //     const que = questionsall && questionsall?.filter((data) => data.question_type?.toLowerCase() === questionType.toLowerCase());      
  //     setQuestions(que)
  //     setTimeout(() => {
  //       setIsLoading(false);
  //     }, 4000);
  // }, [questionType, questionsall])

  useEffect(() => {
    setIsLoading(true);
    const que = questionsall && questionsall[questionType.toLowerCase()] || [];
    setQuestions(que);
    setTimeout(() => {
      setIsLoading(false);
    }, 4000);
  }, [questionType, questionsall])



  const onStartTest = () => {
    setStartTime(Date.now());
    setTestView(true)
    setTime(10800)
    dispatch(FromGeneralAssessmentView(true));
  }
  const onBack = () => {
    dispatch(FromGeneralAssessmentView(false))
    navigate('/auth/NEETAssessmentDetails', {
      state: location.state
    });
  }

  const handleChangeType = (type) => {
    setCurrentIndex(0)
    setIsLoading(true);
    setQuestionType(type)
    // setMarkedForReview({});


    setTimeout(() => {
      setIsLoading(false);
    }, 4000);
  }

  const { t } = useTranslation('translation');

  const handleOpenQuestionList = (event) => {
    setQuestionListAnchorEl(event.currentTarget);
    setOverlayZIndex('block');
  };

  const handleCloseQuestionList = () => {
    setQuestionListAnchorEl(null);
    setOverlayZIndex('none');
  };

  const getNeetAssessmentDetails = async (type) => {

    const result = await adminServices.getNEETAssessmentStartData(assessmentId);
    setCurrentIndex(0)
    if (result.ok) {
      setQuestionsall(result?.data[0]?.result)
    }
  };

  useEffect(() => {
    getNeetAssessmentDetails('Neet');
  }, []);

  const currentQuestion = questions && questions[currentIndex];

  const handlePostUserResult = async () => {
    setOpenConfirmDialog(false);
    setIsLoading(true)
    if (questionType === "Physics") {
      setCurrentIndex(0)
      setIsLoading(false)
      setQuestionType("Chemistry")
    }
    else if (questionType === "Chemistry") {
      setCurrentIndex(0)
      setIsLoading(false)
      setQuestionType("Biology")
    }
    else {
      // const totalTimeTaken = Object.values(questionTimes).reduce((acc, time) => acc + time, 0);
      // const formattedSelectedAnswers = questions && questions?.length > 0 && questions.map((question, index) => ({
      //   questionId: question.id,
      //   index,
      //   selectedAnswer: selectedAnswers[index] || null,
      //   timeTaken: questionTimes[index] || 0,
      //   response_recorded: correctAnswer[index] || null,
      //   multiplier: question?.cognitive_skill_id,
      //   questionTag: question?.question_tag || null
      // }));

      const totalTimeTaken = Object.keys(questionTimes).reduce((acc, key) => acc + questionTimes[key], 0);

      const formattedSelectedAnswers = questions && questions.length > 0 && questions.map((question) => ({
        questionId: question.id,
        selectedAnswer: selectedAnswers[question.id] || null,
        timeTaken: questionTimes[question.id] || 0,
        response_recorded: correctAnswer[question.id] || null,
        multiplier: question?.cognitive_skill_id,
        questionTag: question?.question_tag || null,
      }));
      const body = {
        assessment_id: assessmentId,
        user_id: userInfo?.id,
        user_result: formattedSelectedAnswers,
        date_started: getCurrentTime(),
        date_completed: getCurrentTime(),
        difference_date: totalTimeTaken,
      };
      const result = await adminServices.postNEETResult(body);
      if (result.ok) {
        setMarkedForReview({ physics: {}, chemistry: {}, biology: {} });
        setShowCongratulationsModal(true);
        dispatch(FromGeneralAssessmentView(false))
        setTimeout(() => {
          navigate('/auth/NEETAssessmentDetails', {
            state: location.state
          });
        }, 2000);


      }
    }


  }



  const handlePostUserResultBackground = async () => {
    setOpenConfirmDialog(false);
    setIsLoading(true)
      const totalTimeTaken = Object.keys(questionTimes).reduce((acc, key) => acc + questionTimes[key], 0);

      const formattedSelectedAnswers = questions && questions.length > 0 && questions.map((question) => ({
        questionId: question.id,
        selectedAnswer: selectedAnswers[question.id] || null,
        timeTaken: questionTimes[question.id] || 0,
        response_recorded: correctAnswer[question.id] || null,
        multiplier: question?.cognitive_skill_id,
        questionTag: question?.question_tag || null,
      }));
      const body = {
        assessment_id: assessmentId,
        user_id: userInfo?.id,
        user_result: formattedSelectedAnswers,
        date_started: getCurrentTime(),
        date_completed: getCurrentTime(),
        difference_date: totalTimeTaken,
      };
      const result = await adminServices.postNEETResult(body);
      if (result.ok) {
        setMarkedForReview({ physics: {}, chemistry: {}, biology: {} });
        setShowCongratulationsModal(true);
        dispatch(FromGeneralAssessmentView(false))
        setTimeout(() => {
          navigate('/auth/NEETAssessmentDetails', {
            state: location.state
          });
        }, 2000);


      }
    
  }
  const getAccurateElapsed = () => {
    const now = Date.now();
    let elapsed = 0;

    if (backgroundStartRef.current) {
      elapsed += backgroundStartRef.current - lastUpdateRef.current;
      elapsed += now - backgroundStartRef.current;
      backgroundStartRef.current = null;
    } else {
      elapsed = now - lastUpdateRef.current;
    }

    lastUpdateRef.current = now;
    return Math.floor(elapsed / 1000);
  };

  const lastUpdateRef = useRef(Date.now());
  const backgroundStartRef = useRef(null);
  const timerRef = useRef(null);

  const updateTimer = () => {
    const elapsedSeconds = getAccurateElapsed();
    setTime(prev => {
      const newTime = prev - elapsedSeconds;

      if (newTime <= 0) {
        if (!isPaused && testView) {
          setIsTimeout(true);

          handlePostUserResultBackground();

        }
        return 0;
      }
      return newTime;
    });
  };

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        clearInterval(timerRef.current);
        backgroundStartRef.current = Date.now();
      } else {
        updateTimer();
        startTimer();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isPaused, testView]);

  const startTimer = () => {
    if (!isPaused && time > 0) {
      timerRef.current = setInterval(updateTimer, 1000);
    }
  };

  useEffect(() => {
    lastUpdateRef.current = Date.now();
    startTimer();

    return () => {
      clearInterval(timerRef.current);
    };
  }, [isPaused, time]);


  const handleNavigateAnalysis = () => {
    if (allassessmentdetails.userInfo.role === 'AUTH_USER') {
      navigate(`/app/SATAnalysis?id=${assessmentId}`, {
        state: {
          id: assessmentId,
          comingFrom: 'ComingfromAnalysis',
          fromindex: fromIndex,
          data: details
        }
      })
    } else {
      navigate(`/auth/SATAnalysis?id=${assessmentId}`, {
        state: {
          id: assessmentId,
          comingFrom: 'ComingfromAnalysis',
          fromindex: fromIndex,
          data: details
        }
      })
    }
    // dispatch(FromGeneralAssessmentView(false));
  }

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatTimeTimer = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };


  useEffect(() => {
    if (resumeTime) {
      if (timeLeft > 0) {
        const timer = setInterval(() => {
          setTimeLeft((prevTime) => prevTime - 1);
        }, 1000);
        return () => clearInterval(timer);
      }
      if (timeLeft === 0) {
        setTimeout(() => {
          setShowModuleComplete(false);
          handleNextModule();

        }, [100]);
      }
    }
  }, [timeLeft]);



  // const handleNext = () => {
  //   const endTime = Date.now();
  //   const timeSpent = (endTime - startTime) / 1000;
  //   const currentQuestion = questions[currentIndex];
  //   setStartTime(Date.now());
  //   setQuestionTimes((prev) => ({
  //     ...prev,
  //     [currentIndex]: (prev[currentIndex] || 0) + timeSpent,
  //   }));


  //   if (currentIndex < questions.length - 1) {
  //     setCurrentIndex(currentIndex + 1);
  //   }
  // };

  // const handleBack = () => {
  //   if (currentIndex > 0) {
  //     const endTime = Date.now();
  //     const timeSpent = (endTime - startTime) / 1000;

  //     setQuestionTimes((prev) => ({
  //       ...prev,
  //       [currentIndex]: (prev[currentIndex] || 0) + timeSpent,
  //     }));

  //     setStartTime(Date.now());
  //     setCurrentIndex(currentIndex - 1);
  //   }
  // };

  const handleNext = () => {
    const endTime = Date.now();
    const timeSpent = (endTime - startTime) / 1000;
    const currentQuestion = questions[currentIndex];
    const questionId = currentQuestion?.id;
    setStartTime(Date.now());
    setQuestionTimes((prev) => ({
      ...prev,
      [questionId]: (prev[questionId] || 0) + timeSpent,
    }));

    if (currentIndex < questions.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handleBack = () => {
    if (currentIndex > 0) {
      const endTime = Date.now();
      const timeSpent = (endTime - startTime) / 1000;

      const currentQuestion = questions[currentIndex];
      const questionId = currentQuestion?.id;

      setQuestionTimes((prev) => ({
        ...prev,
        [questionId]: (prev[questionId] || 0) + timeSpent,
      }));

      setStartTime(Date.now());
      setCurrentIndex(currentIndex - 1);
    }
  };


  useEffect(() => {
    const handleBeforeUnload = (event) => {
      event.preventDefault();
      event.returnValue = "Are you sure you want to leave? Your progress will not be saved.";
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);




  // const handleOptionSelect = (option, index) => {
  //   const correctAnswers = currentQuestion?.options?.correctAnswer;
  //   const isCorrect = correctAnswers?.[index] === true;
  //   setCorrectAnswer((prev) => ({
  //     ...prev,
  //     [currentIndex]: isCorrect ? 'correct' : 'incorrect',
  //   }));
  //   setSelectedAnswers((prev) => ({
  //     ...prev,
  //     [currentIndex]: option,
  //   }));
  // };

  const handleOptionSelect = (option, index) => {
    const currentQuestion = questions[currentIndex];
    const questionId = currentQuestion?.id;

    const correctAnswers = currentQuestion?.options?.correctAnswer;
    const isCorrect = correctAnswers?.[index] === true;

    setCorrectAnswer((prev) => ({
      ...prev,
      [questionId]: isCorrect ? 'correct' : 'incorrect',
    }));

    setSelectedAnswers((prev) => ({
      ...prev,
      [questionId]: option,
    }));
  };


  const handleQuestionSelect = (index) => {
    const endTime = Date.now();
    const timeSpent = (endTime - startTime) / 1000;

    setQuestionTimes((prev) => ({
      ...prev,
      [currentIndex]: (prev[currentIndex] || 0) + timeSpent,
    }));

    setStartTime(Date.now());
    setCurrentIndex(index);
    handleCloseQuestionList();
  };



  const handleSubmit = async () => {
    setOpenConfirmDialog(true);

  };

  const handleBookmarkTrue = (index) => {
    setMarkedForReview(prevState => ({
      ...prevState,
      [questionType.toLowerCase()]: {
        ...prevState[questionType.toLowerCase()],
        [index]: true
      }
    }));
  };



  const handleBookmarkFalse = (index) => {
    setMarkedForReview(prevState => ({
      ...prevState,
      [questionType.toLowerCase()]: {
        ...prevState[questionType.toLowerCase()],
        [index]: false
      }
    }));
  };

  // const handleBookmarkTrue = (index) => {
  //   setMarkedForReview((prevState) => ({
  //     ...prevState,
  //     [index]: true,
  //   }));
  // };

  // const handleBookmarkFalse = (index) => {
  //   setMarkedForReview((prevState) => ({
  //     ...prevState,
  //     [index]: false,
  //   }));
  // };

  useEffect(() => {
    if (currentQuestion) {
      setParsedOptions(currentQuestion?.options.mcqOptions);
    }
  }, [currentQuestion]);


  useEffect(() => {
    let breakTimer;
    if (resumeTime && testView === true && questionType === 'English') {
      if (breakTimeLeft > 0) {
        breakTimer = setInterval(() => {
          setBreakTimeLeft(prev => {
            if (prev <= 1) {
              clearInterval(breakTimer);
              setTime(2680);
              handleNextModule();
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
      return () => clearInterval(breakTimer);
    }
  }, [resumeTime, testView]);

  const handleNextModule = () => {
    setTime(2680)
    setQuestionType('Physcis');
    getNeetAssessmentDetails('Neet');
    setStartTime(Date.now())
    setSubmodule(1);
    setResumeTime(false);
    setBreakTimeLeft(600);

  }

  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
    setOverlayZIndex('block');
  };

  const handleClose = () => {
    setAnchorEl(null);
    setOverlayZIndex('none');
  };

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;


  return (
    <Page title={'Begin Course'} style={{ marginTop: '8px', padding: '0px', paddingTop: '56px !important' }}>
      <Box className="overLay" style={{
        opacity: '1', transition: 'opacity 225ms cubic-bezier(0.4, 0, 0.2, 1)', position: 'fixed',
        justifyContent: 'center', right: '0', bottom: '0', top: '0', left: '0',
        display: overlayZIndex,
        backgroundColor: 'rgb(12 12 12 / 35%)', zIndex: '1101', width: '100vw',
        height: '100vh'
      }} />
      {testView === false &&
        <Box className="practiceTimeInstructions" sx={{ mx: "auto", p: 2, textAlign: "center" }}>
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
            {assessmenttitle && assessmenttitle}
          </Typography>

          <Card variant="outlined" sx={{ textAlign: "left" }}>
            <CardContent>
              <Box style={{ display: 'flex', alignItems: 'center', marginTop: '5px' }}>
                <HourglassTopIcon className='iconDesign' />
                <Typography style={{ display: 'inline' }} variant="h6" className='topicHeading' fontWeight="bold">{"Timings"}</Typography>
              </Box>
              <Typography style={{ paddingLeft: '50px' }} variant="body2">
                {"The"} <strong>{assessmenttitle && assessmenttitle}</strong> {"exam is timed. The test is for"} <strong>{"180 minutes"}</strong>  {"duration. There will be no breaks in between the test."}
              </Typography>
              {/* <Box style={{ display: 'flex', alignItems: 'center', marginTop: '15px' }}>
                <CoffeeIcon className='iconDesign' />
                <Typography style={{ display: 'inline' }} variant="h6" className='topicHeading' fontWeight="bold">{"Break time"}</Typography>
              </Box>
              <Typography style={{ paddingLeft: '50px' }} variant="body2">
                {"You will have"} <strong>{"10 minutes"}</strong> {"of break time after the two modules of ‘Reading and Writing’ section are completed. The instructions to follow during this break time can be found on that screen."}
              </Typography> */}
              <Box style={{ display: 'flex', alignItems: 'center', marginTop: '15px' }}>
                <InsightsIcon className='iconDesign' />
                <Typography style={{ display: 'inline' }} variant="h6" className='topicHeading' fontWeight="bold">{"Scores and Analytics"}</Typography>
              </Box>
              <Typography style={{ paddingLeft: '50px' }} variant="body2">
                {"For each correct response, the candidate will get 4 marks. For each incorrect response, one mark will be deducted from the total scores. Your score is calculated once you complete the test. The analytics of your performance in the overall test will be displayed at the end of the test."}
              </Typography>
              <Box style={{ display: 'flex', alignItems: 'center', marginTop: '15px' }}>
                <PsychologyIcon className='iconDesign' />
                <Typography style={{ display: 'inline' }} variant="h6" className='topicHeading' fontWeight="bold">{"Make much of your practice"}</Typography>
              </Box>
              <Typography style={{ paddingLeft: '50px' }} variant="body2">
                {"Please consider this practice NEET test as an actual assessment. We suggest you create an environment that supports the actual test environment so that you are mentally prepared."}
              </Typography>
            </CardContent>
          </Card>
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 3 }}>
            <Button variant="contained" style={{ backgroundColor: '#497ffc', color: '#fff', borderRadius: '6px' }}
              onClick={onBack}
            >
              {"Back"}
            </Button>
            <Button variant="contained" style={{ backgroundColor: '#497ffc', color: '#fff', borderRadius: '6px' }}
              onClick={onStartTest}
            >
              {"Start Test"}
            </Button>
          </Box>
        </Box>}

      {!resumeTime && testView === true &&
        <>
          <Box sx={{ display: "flex", flexDirection: "column", backgroundColor: "#d2daff" }}>
            {(isLoading) ? <br /> :
              <Box id="topicHead" sx={{ display: 'flex', flexDirection: { md: 'row', sm: "column", xs: "column" }, maxHeight: '60px', margin: '6px 0 8px' }}>
                <Box sx={{ width: { md: '40%', sm: '99vw' } }}>
                  <Typography
                    variant="text"
                    sx={{
                      textTransform: "none",
                      fontSize: "17px",
                      float: 'left',
                      top: '-2px',

                      position: 'relative',
                      color: '#000',
                      fontWeight: '500',
                      paddingLeft: '40px'
                    }}
                    onClick={handleClick}>
                    {"Directions"}<KeyboardArrowDownIcon style={{ top: '6px', position: 'relative', color: '#4a4a4a', fontSize: '24px' }} />
                  </Typography>


                  <Popover

                    id={id}
                    open={open}
                    PaperProps={{
                      sx: {
                        width: questionType === 'English' ? 'auto' : '75vw',
                        maxWidth: '90vw',
                      },
                    }}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'left',

                    }}
                  >
                    <Typography sx={{ p: 2 }}>
                      The questions in this module will test all levels of your math skills.
                      <br /><br />
                      The use of a simple calculator is permitted for all questions. You can click on the calculator icon to start using the calculator.
                      <br /><br />
                      If the question does not specify, you should assume:
                      <br />
                      &nbsp;&nbsp;&bull; All variables and expressions represent real numbers.
                      <br />
                      &nbsp;&nbsp;&bull; The figures provided are drawn to scale.
                      <br />
                      &nbsp;&nbsp;&bull; All figures lie in a plane.
                      <br />
                      &nbsp;&nbsp;&bull; The domain of a given function "f" is the set of all real numbers "x" for which "f(x)" is a real number.
                      <br /><br />
                      Every question in this module is a multiple-choice based question and has a single correct answer.
                      You are expected to solve each problem and choose the correct one.
                      <br /><br />
                      The timer is set on the server after your test starts, and the test will be auto submitted after the timer runs out.
                      You can submit the test within the test if you are satisfied with your answers.
                    </Typography>
                    <Button style={{
                      float: 'right', marginRight: '20px',
                      marginBottom: '10px',
                      border: '1px solid'
                    }} onClick={handleClose}>Close</Button>
                  </Popover>


                </Box>
                <Box sx={{ maxHeight: { md: '60px', sm: '70px' }, width: { md: '20%', sm: '99vw', justifyItems: 'center' } }}>
                  <Typography variant="h6" sx={{ fontWeight: "bold", position: 'relative', top: '5px' }}>{formatTimeTimer(time)}</Typography>
                </Box>
                <Box style={{ flex: 2, textAlign: 'end', marginTop: '5px' }}>
                  <Button
                    onClick={() => handleChangeType('Physics')}
                    style={{
                      backgroundColor: questionType === 'Physics' ? '#497ffc' : '#f5f5f5',
                      color: questionType === 'Physics' ? '#fff' : '#666',
                      border: questionType === 'Physics' ? '1px solid #497ffc' : '2px solid #ddd',
                      borderRadius: '6px',
                      fontWeight: questionType === 'Physics' ? 'bold' : 'normal',
                      padding: '0px 12px',
                      margin: '0 2px',
                      transition: 'all 0.3s ease',
                      boxShadow: questionType === 'Physics' ? '0 2px 8px rgba(73, 127, 252, 0.3)' : 'none',
                      transform: questionType === 'Physics' ? 'translateY(-1px)' : 'none'
                    }}
                  >
                    Physics
                  </Button>
                  <Button
                    onClick={() => handleChangeType('Chemistry')}
                    style={{
                      backgroundColor: questionType === 'Chemistry' ? '#497ffc' : '#f5f5f5',
                      color: questionType === 'Chemistry' ? '#fff' : '#666',
                      border: questionType === 'Chemistry' ? '1px solid #497ffc' : '2px solid #ddd',
                      borderRadius: '6px',
                      fontWeight: questionType === 'Chemistry' ? 'bold' : 'normal',
                      padding: '0px 12px',
                      margin: '0 2px',
                      transition: 'all 0.3s ease',
                      boxShadow: questionType === 'Chemistry' ? '0 2px 8px rgba(73, 127, 252, 0.3)' : 'none',
                      transform: questionType === 'Chemistry' ? 'translateY(-1px)' : 'none'
                    }}
                  >
                    Chemistry
                  </Button>
                  <Button
                    onClick={() => handleChangeType('Biology')}
                    style={{
                      backgroundColor: questionType === 'Biology' ? '#497ffc' : '#f5f5f5',
                      color: questionType === 'Biology' ? '#fff' : '#666',
                      border: questionType === 'Biology' ? '1px solid #497ffc' : '2px solid #ddd',
                      borderRadius: '6px',
                      fontWeight: questionType === 'Biology' ? 'bold' : 'normal',
                      padding: '0px 12px',
                      margin: '0 2px',
                      transition: 'all 0.3s ease',
                      boxShadow: questionType === 'Biology' ? '0 2px 8px rgba(73, 127, 252, 0.3)' : 'none',
                      transform: questionType === 'Biology' ? 'translateY(-1px)' : 'none'
                    }}
                  >
                    Biology
                  </Button>

                </Box>
              </Box>}
            <Grid
              container
              justifyContent="center"
              alignItems="center"
              style={{ height: questionType === 'English' ? 'Calc(100vh - 208px)' : 'Calc(100vh - 172px)', textAlign: "center", alignItems: 'baseline' }}
            >
              {
                isLoading ?
                  <Grid sx={{
                    padding: 1, backgroundColor: "#ffffff",
                    paddingTop: '200px',
                    width: '100%',
                    paddingRight: '0px',
                    marginTop: '60px',
                    paddingleft: '0px',
                    height: 'Calc(100vh - 180px)',
                    overflowX: 'hidden',
                    overflowY: 'auto',
                  }}>
                    <LottieLoading loading={isLoading} />
                    {showModuleComplete && (
                      <Box
                        sx={{
                          position: 'fixed',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          zIndex: 9999,
                          padding: '20px',
                          textAlign: 'center'
                        }}
                      >
                        <Typography variant="h4" sx={{ color: '#909bd7', marginBottom: 2, fontWeight: '500' }}>
                          This Module Is Over
                        </Typography>
                        <Typography variant="body1" sx={{ marginBottom: 2, fontWeight: '500' }}>
                          All your work has been saved
                        </Typography>
                        <Typography variant="body1" sx={{ marginBottom: 1, fontWeight: '500' }}>
                          You'll move on automatically in just a moment.
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'red' }}>
                          Do not refresh this page or quit the app.
                        </Typography>
                      </Box>
                    )}
                  </Grid> :
                  currentQuestion && (
                    <Box
                      sx={{
                        width: {
                          xs: "90%", sm: "80%", md: "70%", lg: "68%", xl: "85%"
                        },
                        marginTop: '0px',
                        backgroundColor: "#e9f0fa",
                        borderRadius: 1,
                        textAlign: "center",
                        boxShadow: 2,
                        maxHeight: {
                          xs: '370px',
                          sm: 'calc(70vh - 100px)'
                        },
                        overflowY: 'auto',
                        '@media (max-height: 1000px)': {
                          maxHeight: '95%',
                          overflowY: 'auto'
                        },
                        '@media (min-height: 1000px)': {
                          maxHeight: '100%'
                        }
                      }}
                      style={{ padding: '25px', }}
                    >
                      <Box
                        sx={{
                          padding: '4px',
                          backgroundColor: "#f0f0f0",
                          borderRadius: '2px',
                          textAlign: "center",
                          borderBottom: '3px dashed #000000c9'

                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            gap: 1,
                          }}
                        >
                          <Box
                            sx={{
                              display: "inline-block",
                              padding: "4px 10px !important",
                              backgroundColor: "black",
                              borderRadius: "2px",
                              marginBottom: 0,
                            }}
                          >
                            <Typography variant="subtitle1" sx={{ fontWeight: "bold", color: "white" }}>
                              {currentIndex + 1}
                            </Typography>
                          </Box>

                          <Button
                            onClick={() => {
                              if (markedForReview[questionType.toLowerCase()]?.[currentIndex]) {
                                handleBookmarkFalse(currentIndex);
                              } else {
                                handleBookmarkTrue(currentIndex);
                              }
                            }}
                            className="MarkBtn"
                            sx={{
                              minWidth: "auto",
                              color: '#1976d2',
                            }}
                          >
                            {markedForReview[questionType.toLowerCase()]?.[currentIndex] ? (
                              <BookmarkIcon color="#1976d2" />
                            ) : (
                              <BookmarkBorderIcon color="#1976d2" />
                            )}
                            {markedForReview[questionType.toLowerCase()]?.[currentIndex] ? 'Marked for Review' : 'Mark for Review'}
                          </Button>
                        </Box>
                      </Box>
                      <Typography className='questionViewer'
                        dangerouslySetInnerHTML={{ __html: currentQuestion?.question_text }}
                        sx={{
                          marginTop: '5px',
                          width: "100%",
                          textAlign: 'start',
                          fontWeight: '400',
                          fontSize: { xs: "16px", md: "18px" },
                          marginBottom: 1,
                          userSelect: 'none'
                        }}
                        onCopy={(e) => e.preventDefault()}
                        onContextMenu={(e) => e.preventDefault()}
                      />

                      {parsedOptions && parsedOptions?.map((option, index) => (
                        <Button id="optionsBtnSAT"
                          key={index}
                          variant="contained"
                          fullWidth
                          sx={{
                            marginTop: 1,
                            backgroundColor: "white",
                            color: "black",
                            justifyContent: "start",
                            border: selectedAnswers[currentQuestion?.id] === option
                              ? "2px solid #4caf50"
                              : "1px solid #ccc",
                            "&:hover": {
                              backgroundColor: "#f1f1f1",
                            },
                            fontSize: { xs: "12px", md: "14px" },
                          }}
                          style={{
                            padding: '12px 20px',
                            borderRadius: '4px', fontWeight: '400'
                          }}
                          onClick={() => handleOptionSelect(option, index)}
                        >
                          <span style={{
                            border: '1px solid #aaa',
                            borderRadius: '50%',
                            padding: '7px',
                            height: '30px',
                            width: '32px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginRight: '12px',
                          }}>{String.fromCharCode(97 + index)}</span>
                          <span dangerouslySetInnerHTML={{ __html: option }} />
                        </Button>
                      ))}
                    </Box>
                  )}
            </Grid>

            <Popover
              open={Boolean(questionListAnchorEl)}
              anchorEl={questionListAnchorEl}
              onClose={handleCloseQuestionList}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'center',
              }}
              transformOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
              }}
              PaperProps={{
                sx: {
                  width: '550px',
                  padding: '32px',
                  paddingTop: '20px',
                  overflow: 'hidden'
                }
              }}
            >
              <Box>
                <Box sx={{
                  display: "flex",
                  flexWrap: 'wrap',
                  justifyContent: "space-between",
                  marginBottom: 2,
                  borderBottom: '2px solid #b1b1b1',
                  alignItems: 'baseline',
                  paddingBottom: '12px'
                }}>
                  <Box sx={{ display: "flex", alignItems: "center", marginBottom: "10px" }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: "#B3B3B3", borderRadius: "4px", marginRight: 1 }} />
                    <Typography variant="body2">{"Not Answered"}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: "#4caf50", borderRadius: "4px", marginRight: 1 }} />
                    <Typography variant="body2">{"Answered"}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: "#2196F3", borderRadius: "4px", marginRight: 1 }} />
                    <Typography variant="body2">{"Current"}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box
                      sx={{
                        backgroundColor: '#f1f1f1',
                        width: "20px",
                        height: "20px",
                        fontWeight: "bold",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        cursor: "pointer",
                        borderRadius: '4px',
                        color: '#FFFFFF',
                        position: "relative",
                        marginRight: 1
                      }}
                    >
                      <BookmarkIcon
                        sx={{
                          position: "absolute",
                          top: "0",
                          right: "0",
                          fontSize: "16px",
                          color: "#FE780F",
                          transform: "translate(30%, -25%)",
                        }}
                      />
                    </Box>
                    <Typography variant="body2">{"Marked for Review"}</Typography>
                  </Box>
                </Box>

                <Grid container spacing={2} sx={{ maxHeight: '390px', overflowY: 'auto' }}>
                  {questions && questions?.length > 0 && questions.map((q, index) => {
                    const isSelected = currentIndex === index;
                    const isAnswered = Object.prototype.hasOwnProperty.call(selectedAnswers, q.id);
                    const isMarkedForReview = markedForReview[questionType.toLowerCase()]?.[index];

                    let backgroundColor = "#B3B3B3";
                    if (isAnswered) backgroundColor = "#4caf50";
                    if (isSelected) backgroundColor = "#2196F3";

                    return (
                      <Grid item xs={2} key={index}>
                        <Box
                          onClick={() => handleQuestionSelect(index)}
                          sx={{
                            backgroundColor,
                            width: "40px",
                            height: "40px",
                            fontSize: "14px",
                            fontWeight: "bold",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            cursor: "pointer",
                            borderRadius: '4px',
                            color: '#FFFFFF',
                            boxShadow: 'rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px',
                            position: "relative",
                            "&:hover": { backgroundColor: "#1976d2", color: "white" }
                          }}
                        >
                          {isMarkedForReview && (
                            <BookmarkIcon
                              sx={{
                                position: "absolute",
                                top: "0",
                                right: "0",
                                fontSize: "26px",
                                color: "#FE780F",
                                transform: "translate(30%, -25%)",
                              }}
                            />
                          )}
                          {index + 1}
                        </Box>
                      </Grid>
                    );
                  })}
                </Grid>
                <p style={{
                  height: '5px',
                  display: 'flex',
                  justifyContent: 'center',
                  margin: '0'
                }}>
                  <span className="reviewBtn"
                    style={{
                      position: 'relative',

                    }}><br /></span>
                </p>
              </Box>
            </Popover>


            <Box sx={{ backgroundColor: "#e9f0fa", display: "flex", justifyContent: "space-between", height: '60px', padding: '10px 5px !important' }}>
              <Typography>{userInfo?.name}</Typography>
              <Button variant="contained" id="ViewQuestionAll" style={{ backgroundColor: "#434040", borderRadius: '4px', marginLeft: '40px' }} disabled={isPaused} onClick={handleOpenQuestionList}>
                {"View all questions"}
              </Button>
              <Box sx={{ padding: 1, backgroundColor: "#e9f0fa", display: "flex", justifyContent: "space-between" }}>
                <Button
                  variant="contained"
                  onClick={handleBack}
                  disabled={currentIndex === 0 || isPaused}
                  style={{
                    marginRight: '10px', background: (currentIndex === 0 || isPaused) ? '#d3d3d3' : '#1976d2',
                    borderRadius: '4px', color: "#fff"
                  }} >
                  {"Back"}
                </Button>
                {currentIndex === questions?.length - 1 ? (
                  <Button variant="contained" style={{ background: '#1976d2', borderRadius: '4px', color: "#fff" }} onClick={handleSubmit} disabled={isPaused} >{"Submit"}</Button>
                ) : (
                  <Button variant="contained" style={{ background: '#1976d2', borderRadius: '4px', color: "#fff" }} onClick={() => handleNext()} disabled={isPaused}>{"Next"}</Button>
                )}
              </Box>
            </Box>
          </Box>
        </>
      }
      <Snackbar open={isopenSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />
      <Dialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" style={{ paddingBottom: '0px !important' }} sx={{ pb: 0 }} textAlign={'center'}>
          {"Do you really want to submit"}

        </DialogTitle>
        <DialogContent style={{ borderTop: '1px solid rgb(163, 163, 163)', borderBottom: '1px solid rgb(163, 163, 163)', overflow: 'hidden' }}>

          <Box id="QuestionModulePop"
            sx={{
              margin: 'auto',

              backgroundColor: 'white',
              padding: '20px !important',
              paddingTop: '15px !important',
              overflow: 'hidden'

            }}
          >


            <Box sx={{ display: "flex", flexWrap: 'wrap', justifyContent: "space-between", marginBottom: 2, borderBottom: '2px solid #b1b1b1', alignItems: 'baseline', paddingBottom: '12px !important', }}>
              <Box sx={{ display: "flex", alignItems: "center", marginBottom: "10px" }}>
                <Box sx={{ width: 20, height: 20, backgroundColor: "#B3B3B3", borderRadius: "4px", marginRight: 1 }} />
                <Typography variant="body2">{"Not Answered"}</Typography>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ width: 20, height: 20, backgroundColor: "#4caf50", borderRadius: "4px", marginRight: 1 }} />
                <Typography variant="body2">{"Answered"}</Typography>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ width: 20, height: 20, backgroundColor: "#2196F3", borderRadius: "4px", marginRight: 1 }} />
                <Typography variant="body2">{"Current"}</Typography>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    backgroundColor: '#f1f1f1',
                    width: "20px",
                    height: "20px",
                    fontWeight: "bold",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    cursor: "pointer",
                    borderRadius: '4px',
                    color: '#FFFFFF',
                    position: "relative",
                    marginRight: 1
                  }}
                >
                  <BookmarkIcon
                    sx={{
                      position: "absolute",
                      top: "0",
                      right: "0",
                      fontSize: "16px",
                      color: "#FE780F",
                      transform: "translate(30%, -25%)",
                    }}
                  />
                </Box>
                <Typography variant="body2">{"Marked for Review"}</Typography>
              </Box>
            </Box>
            <Grid container spacing={2} sx={{ maxHeight: '390px', overflowY: 'auto' }}>
  {questions && questions.length > 0 && questions.map((q, index) => {
    const isSelected = currentIndex === index;
    const isAnswered = Object.prototype.hasOwnProperty.call(selectedAnswers, q.id); // updated logic
    const isMarkedForReview = markedForReview?.[questionType?.toLowerCase()]?.[index]; // updated logic

    let backgroundColor = "#B3B3B3";
    if (isAnswered) backgroundColor = "#4caf50";
    if (isSelected) backgroundColor = "#2196F3";

    return (
      <Grid item xs={2} key={index}>
        <Box
          onClick={() => handleQuestionSelect(index)}
          sx={{
            backgroundColor,
            width: "40px",
            height: "40px",
            fontSize: "14px",
            fontWeight: "bold",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            cursor: "pointer",
            borderRadius: '4px',
            color: '#FFFFFF',
            boxShadow: 'rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px',
            position: "relative",
            "&:hover": { backgroundColor: "#1976d2", color: "white" }
          }}
        >
          {isMarkedForReview && (
            <BookmarkIcon
              sx={{
                position: "absolute",
                top: "0",
                right: "0",
                fontSize: "26px",
                color: "#FE780F",
                transform: "translate(30%, -25%)",
              }}
            />
          )}
          {index + 1}
        </Box>
      </Grid>
    );
  })}
</Grid>

            {/* <Grid container spacing={2} sx={{ maxHeight: '390px', overflowY: 'auto' }}>
              {questions && questions.length > 0 && questions.map((q, index) => {
                const isSelected = currentIndex === index;
                const isAnswered = Object.prototype.hasOwnProperty.call(selectedAnswers, index);
                const isMarkedForReview = markedForReview?.[index];
                let backgroundColor = "#B3B3B3";
                if (isAnswered) backgroundColor = "#4caf50";
                if (isSelected) backgroundColor = "#2196F3";

                return (
                  <Grid item xs={2} key={index}>
                    <Box
                      onClick={() => handleQuestionSelect(index)}
                      sx={{
                        backgroundColor,
                        width: "40px",
                        height: "40px",
                        fontSize: "14px",
                        fontWeight: "bold",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        cursor: "pointer",
                        borderRadius: '4px',
                        color: '#FFFFFF',
                        boxShadow: 'rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px',
                        position: "relative",
                        "&:hover": { backgroundColor: "#1976d2", color: "white" }
                      }}
                    >
                      {isMarkedForReview && (
                        <BookmarkIcon
                          sx={{
                            position: "absolute",
                            top: "0",
                            right: "0",
                            fontSize: "26px",
                            color: "#FE780F",
                            transform: "translate(30%, -25%)",
                          }}
                        />
                      )}
                      {index + 1}
                    </Box>
                  </Grid>
                );
              })}
            </Grid> */}

            <p style={{
              height: '5px',
              display: 'flex',
              justifyContent: 'center',
              margin: '0'
            }}>
              <span className="reviewBtn"
                style={{
                  position: 'relative',

                }}><br /></span>
            </p>
          </Box>
        </DialogContent>
        <DialogActions>
          <Box style={{ display: 'flex', justifyContent: 'space-between', flex: 2, padding: '0 20px', marginBottom: '10px' }}>
            <Button
              variant="outlined"
              onClick={() => setOpenConfirmDialog(false)}
              color="primary"
            >
              {"Back"}
            </Button>
            <Button
              variant="outlined"
              onClick={() => handlePostUserResult()}
              color="primary"
            >
              {"Submit"}
            </Button>
          </Box>


        </DialogActions>
      </Dialog>

      <Dialog
        open={showCongratulationsModal}
        disableEscapeKeyDown
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '12px',
            padding: '20px',
            textAlign: 'center',
            minWidth: '400px'
          }
        }}
      >
        <DialogContent sx={{ padding: '20px' }}>
          <div style={{ marginTop: '0px', paddingBottom: '10px' }}>
            <div className="flex justify-center mb-4">
              <div className="relative inline-block">
                <img style={{ justifySelf: 'center', width: '60px', marginBottom: '10px' }}
                  src={TestComplete}
                  alt="Timeout Icon"
                  className="w-16 h-16"
                />
              </div>
            </div>

            <h2 className="text-xl font-bold text-blue-600" id="canvas" style={{
              textAlign: 'center',
              marginTop: '5px', fontWeight: '700', color: "#6c98fd !important"
            }}>Congratulations!</h2>
            <p className="text-gray-600 mt-2" style={{
              textAlign: 'center', marginTop: '10px'
            }}>
              You have successfully completed the Test.
            </p>

          </div>

          <LottieLoading />
        </DialogContent>
      </Dialog>


      {isLoading &&
        <>
          <Grid sx={{
            padding: 1, backgroundColor: "#ffffff",
            paddingTop: '200px',
            width: '100%',
            paddingRight: '0px',
            marginTop: '60px',
            paddingleft: '0px',
            height: 'Calc(100vh - 180px)',
            overflowX: 'hidden',
            overflowY: 'auto',
          }}>
            <LottieLoading loading={isLoading} />
            {showModuleComplete && (
              <Box
                sx={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 9999,
                  padding: '20px',
                  textAlign: 'center'
                }}
              >
                <Typography variant="h4" sx={{ color: '#909bd7', marginBottom: 2, fontWeight: '500' }}>
                  This Module Is Over
                </Typography>
                <Typography variant="body1" sx={{ marginBottom: 2, fontWeight: '500' }}>
                  All your work has been saved
                </Typography>
                <Typography variant="body1" sx={{ marginBottom: 1, fontWeight: '500' }}>
                  You'll move on automatically in just a moment.
                </Typography>
                <Typography variant="body1" sx={{ color: 'red' }}>
                  Do not refresh this page or quit the app.
                </Typography>
              </Box>
            )}
          </Grid>
        </>
      }

    </Page>
  );
};

export default SatAssessment;
