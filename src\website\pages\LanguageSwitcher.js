/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageIcon from '@mui/icons-material/Language';
import { useDispatch, useSelector } from 'react-redux';
import './style.css';

import { languagecodevalue } from '../../store/reducer';

const LanguageSwitcher = ({ isDisabled }) => {
  const { i18n } = useTranslation();
  const dispatch = useDispatch();
  const allcourseDetails = useSelector((state) => state);
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [disabled, setDisable] = useState(false);

  console.log(selectedLanguage,"selectedLanguage");
  
  
  const handleLanguageChange = (e) => {
    const newLanguage = e.target.value;
    // console.log(newLanguage,"newLanguage");
    dispatch(languagecodevalue(newLanguage));
    
    i18n.changeLanguage(newLanguage);
    // alert("1");
    document.body.classList.remove('lang-kn', 'lang-en');
    document.body.classList.add(`lang-${newLanguage}`);
    
    // localStorage.setItem('selectedLanguage', newLanguage);
    // // alert("2");
    // console.log("Language changed to:", newLanguage);
  };

  useEffect(() => {
    if (allcourseDetails.languagecode) {
      setSelectedLanguage(allcourseDetails.languagecode);
      i18n.changeLanguage(allcourseDetails.languagecode);
      // alert("3");
      document.body.classList.remove('lang-kn', 'lang-en');
      document.body.classList.add(`lang-${allcourseDetails.languagecode}`);
      // alert("4");
    }
  }, [allcourseDetails.languagecode, i18n]);

  
  // const storedLanguage = localStorage.getItem('selectedLanguage');

  // useEffect(() => {
  //   if (storedLanguage) {

  //     setSelectedLanguage(storedLanguage);
  //     i18n.changeLanguage(storedLanguage);
  
  //     document.body.classList.remove('lang-ka', 'lang-en');
  //     document.body.classList.add(`lang-${storedLanguage}`);
  //   }
  // }, [storedLanguage]);


  return (
    <>
      <div className="language-switcher languageSwitcher">
       
       
     <LanguageIcon 
          className="language-icon" 
          style={{ 
            position: 'absolute', 
            top: '8px', 
            left: '5px',
            color: '#fe7000d1',
            opacity: disabled ? 0.5 : 1
          }} 
        />
        <select 
          className="languageSelect" 
          style={{
            paddingLeft: '35px',
            opacity: disabled ? 0.7 : 1,
            cursor: disabled ? 'not-allowed' : 'pointer' 
          }} 
          onChange={handleLanguageChange} 
          value={selectedLanguage}
        >
          {allcourseDetails.languagecodevalue && allcourseDetails.languagecodevalue?.length > 0 && allcourseDetails.languagecodevalue.map((opt) => (
            <option 
              key={opt.value}
              style={{cursor: opt.value === "en" || opt.value === "kn" ? 'pointer' : ''}}
              value={opt.value} 
              disabled={!(opt.value === "en" || opt.value === "kn")}
            >
              {opt.code.charAt(0).toUpperCase() + opt.code.slice(1)}
            </option>
          ))}
        </select>
      </div>
    </>
  );
};

export default LanguageSwitcher;
