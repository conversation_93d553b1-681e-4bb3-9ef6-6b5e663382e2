.editor {
    counter-reset: line;
    border: 1px solid #ced4da;
    border-radius: 8px;
}

.editor #codeArea {
    outline: none;
    padding-left: 60px !important;
}

.editor pre {
    padding-left: 60px !important;
}

.ql-snow .ql-tooltip {
    position: revert;
}

.editor .editorLineNumber {
    position: absolute;
    left: 0px;
    color: #cccccc;
    text-align: right;
    width: 40px;
    font-weight: 100;
}

.dropHours>div {
    padding: 7px 15px;
}

.dropTextArea {
    min-height: 144px !important;
}

.GACognitivesection label {
    top: -9px !important;
}

.AssessmentTime label {
    top: -11px !important;
    margin-top: 4px;
}

.FormCheck {
    display: flex;
    flex-direction: row;
}

.FormCheck label {
    display: inline;
    width: 30%;
}

[data-color-mode*="dark"],
[data-color-mode*="dark"] body {
    --color-canvas-subtle: #161b22;
}

[data-color-mode*="light"],
[data-color-mode*="light"] body {
    --color-canvas-subtle: #f6f8fa;
}

/* 
table#u_body table:first-child{
    display: inline-block;
    font-size: 20px;
    font-weight: bold;
}

table#u_body table:first-child tbody a[target*="_blank"] {
    animation: slideInRight 1.5s ease-in-out;
} */

.delay-effect table#u_body table:nth-child(2), .delay-effect table#u_body table:nth-child(3), 
.delay-effect table#u_body table:nth-child(4), .delay-effect table#u_body table:nth-child(5),
.delay-effect table#u_body table:nth-child(6), .delay-effect table#u_body table:nth-child(7){
 visibility: hidden;

}
.delay-effect table#u_body table:first-child, 
#simulationSec table[role="presentation"] div > table[role="presentation"]:first-child {

  animation: delayEffect 0.8s ease-in-out forwards;
  animation-delay: 0s;

}


.delay-effect table#u_body table:not(:first-child),
.delay-effect table[role="presentation"] table:not(:first-child) {
    visibility: hidden;
    opacity: 0;
}


.delay-effect table#u_body table:nth-child(2),
.delay-effect table[role="presentation"] table:nth-child(2) {
    animation: delayEffect 0.8s ease-in-out forwards;
    animation-delay: 0.5s;
}


.delay-effect table#u_body table:nth-child(3),
.delay-effect table[role="presentation"] table:nth-child(3) {
    animation: delayEffect 0.8s ease-in-out forwards;
    animation-delay: 1.3s; /* Previous delay + animation duration */
}


.delay-effect table#u_body table:nth-child(4),
.delay-effect table[role="presentation"] table:nth-child(4) {
    animation: delayEffect 0.8s ease-in-out forwards;
    animation-delay: 2.1s;
}


.delay-effect table#u_body table:nth-child(5),
.delay-effect table[role="presentation"] table:nth-child(5) {
    animation: delayEffect 0.8s ease-in-out forwards;
    animation-delay: 2.9s;
}


.delay-effect table#u_body table:nth-child(6),
.delay-effect table[role="presentation"] table:nth-child(6) {
    animation: delayEffect 0.8s ease-in-out forwards;
    animation-delay: 3.7s;
}


.delay-effect table#u_body table:nth-child(7),
.delay-effect table[role="presentation"] table:nth-child(7) {
    animation: delayEffect 0.8s ease-in-out forwards;
    animation-delay: 4.5s;
}
#dropzoneSection {
height: 45px;
display: flex;
/* justify-content: start; changed to flex-end due to getting warning */
justify-content: flex-start;
align-items: center;
}
#dropzoneSection input[type="file"] {
    display: block !important;
    font-size: 15px;
}
#QuestionListtable tbody tr:nth-child(odd) td {
  background-color: #f5f5f5;
}
#QuestionListtable thead th{
  background: rgba(0, 0, 0, 0.15);
}
#outlinedBasic  {
    width: 550px;
    padding: 10px 14px !important;
}


@keyframes delayEffect {
    0% {
        visibility: hidden;
        transform: translateX(-100%);
        opacity: 0;
    }
    1% {
        visibility: visible;
    }
    100% {
        visibility: visible;
        transform: translateX(0);
        opacity: 1;
    }
}

.animationBtn button {
    margin-right: 12px;
}

.animationBtn button svg {
    margin-bottom: 8px;
    scale: 1.4;
    color: #606060 !important;
}

.animationBtn button:hover {
    background-color: #eeeeee !important;
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.17) -2px 7px 20px;
    border: 1px solid rgb(212, 212, 212);
    animation: 0.9s ease 1.5s infinite normal none running shake;
}
 
  .scroll-text-right table#u_body table {
    animation: slideInLeft 0.5s ease-in-out !important;
  }
  
  .scroll-text-left table#u_body table{
    animation: slideInRight 0.5s ease-in-out !important;
  } 
  
  .pop-up-animation table#u_body table {
    animation: popUp 0.5s ease-in-out !important;
  }

  .search-select-container input, #selectionQue + input{
    font-size: 14px;
    padding: 9px 12px;
    border-color: #ddd;
    width: 100%;
    outline: none;
    border: 1px solid rgba(145, 158, 171, 0.32);
    border-radius: 6px;
  }
  .search-select-container ul, ul[aria-labelledby="searchable-select-label"]{
  list-style: none;
  /* max-height: 120px;
  border: 1px solid rgba(145, 158, 171, 0.32); */
  margin-top: 2px;
  overflow: auto;
}

.search-select-container li{
  list-style: none;
  padding: 3px 0 4px 12px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  /* border-bottom: 1px solid rgba(145, 158, 171, 0.32); */
}
.search-select-container .selected-option{
  overflow: hidden;
}
.search-select-container .selected-option p{
  font-weight: 400;
  padding-top: 4px;
  font-size: 15px;
}

#questionText [class*="ql-editor"] p:has(sub),  [id^="optiontext"] [class*="ql-editor"] p:has(sup),
#questionText [class*="ql-editor"] p:has(sup),  [id^="optiontext"] [class*="ql-editor"] p:has(sub),
#explanation [class*="ql-editor"] p:has(sub), #explanation [class*="ql-editor"] p:has(sup) {
  display: inline !important;
}
#questionText [class*="ql-editor"] p img, [id^="optiontext"] [class*="ql-editor"] p img,
#explanation [class*="ql-editor"] p img, #explanation [class*="ql-editor"] p img,
#explanation [class*="ql-editor"] p , #explanation [class*="ql-editor"] p ,
#explanation [class*="ql-editor"] h3 img, #explanation [class*="ql-editor"] h3 img,
#explanation [class*="ql-editor"] h3 , #explanation [class*="ql-editor"] h3 ,
#explanation [class*="ql-editor"] h2 img, #explanation [class*="ql-editor"] h2 img,
#explanation [class*="ql-editor"] h2 , #explanation [class*="ql-editor"] h2 ,
#explanation [class*="ql-editor"] h5 img, #explanation [class*="ql-editor"] h5 img,
#explanation [class*="ql-editor"] h5 , #explanation [class*="ql-editor"] h5 ,
#explanation [class*="ql-editor"] h4 img, #explanation [class*="ql-editor"] h4 img,
#explanation [class*="ql-editor"] h4 , #explanation [class*="ql-editor"] h4 ,
#questionText [class*="ql-editor"] p, [id^="optiontext"] [class*="ql-editor"] p{
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  white-space: pre-wrap;
  word-break: break-word;
  /* align-items: end !important; changed to flex-end due to getting warning */ 
  align-items: flex-end !important;
}
  
#questionText [class*="ql-editor"] p:has(br), [id^="optiontext"] [class*="ql-editor"] p:has(br),
#questionText [class*="ql-editor"] p:empty , [id^="optiontext"] [class*="ql-editor"] p:empty,
#explanation [class*="ql-editor"] p:has(br) , #explanation [class*="ql-editor"] p:has(br),
#explanation [class*="ql-editor"] p:empty , #explanation [class*="ql-editor"] p:empty {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  white-space: pre-wrap;
  word-break: break-word;
  /* align-items: end !important; changed to flex-end due to getting warning  */
   align-items: flex-end !important;
}
#QuestionArea span:has(em)  {
  display : block !important;
}
#questionText .ql-editor p, #questionText .ql-editor p img,
[id^="optiontext"] .ql-editor li img, #questionText .ql-editor li img, #explanation [class*="ql-editor"] li img{
  display: inline;
}

#SATQuestionTable tbody tr td:nth-child(4) p, #QuestionlistMakers p{
  display: inline-flex;
  flex-wrap: wrap !important;
}
#SATQuestionTable tbody tr td:nth-child(4) p{
  padding-right: 4px;
 
}
#SATQuestionTable tbody tr td:nth-child(4) p:has(br){
  padding-right: 0;
}
#searchQuestionsTag, #QuestionArea{
  flex: 2;
  
}
li#QuestionlistMakers span{
  flex-wrap: wrap !important;
}
/* li#QuestionlistMakers span:has(sub) {
  display: inline !important;           
}   got issue for preview -- so commented          */  

 li#QuestionlistMakers span:has(sub) {
  display: flex !important;
}
#searchQuestionsTag span,#searchQuestionsTag p, #QuestionArea span,  #QuestionArea p , #QuestionArea p p, 
#QuestionlistMakers span{
  flex-wrap: wrap !important;
  flex-basis: content;
} 
#searchQuestionsTag li {
  display: flex;
  flex-wrap: wrap;
}

li#QuestionlistMakers span li, #QuestionArea li {
  flex: 2;
  display: flex;
}
li#QuestionlistMakers div:first-child span:first-child {
  /* flex: 2; */
  flex: unset;
}
li#QuestionlistMakers span li{
  flex-wrap: wrap;
}
#searchQuestionsTag sub, #QuestionArea sub, #QuestionlistMakers sub, #SATQuestionTable tbody tr sub{
  position: relative;
  top: 5px;
}
[class*="search-select-container"] li span {
  display: inline;
  flex-wrap: wrap;
} 

#searchQuestionsTag p{
  display: inline;
}

#questionText .ql-editor p img {
  position: relative;
  top: 6px;
}
#question_text .ql-editor p, [id^="optiontext"] .ql-editor p, #justification .ql-editor p,
[id^="optiontext"] [class*="ql-editor"] p:has(sup), [id^="optiontext"] [class*="ql-editor"] p:has(sub){
  display: flex !important;
  flex-wrap: wrap;
}
#question_text .ql-editor p sub, #justification .ql-editor p sub, [id^="optiontext"] [class*="ql-editor"] p sub{
  top: 6px;
  position: relative;
}
#question_text .ql-editor p sup, #justification .ql-editor p sup,  [id^="optiontext"] [class*="ql-editor"] p sup{
  top: -4px;
  position: relative;
}
table#SATQuestionTable tbody td ul li {
  display: flex;
}
table#SATQuestionTable tbody td ol li:has(img) {
  display: inline-flex;
}

.ql-editor {
    white-space: pre-wrap !important;
    line-height: 1.5 !important;
}

/* #SATQuestionTable tbody tr td:nth-child(4) p:has(sub), 
#searchQuestionsTag span:has(sub){
  display: inline !important;
} */

  @keyframes slideInRight {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }
  
  @keyframes slideInLeft {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }
  
  @keyframes popUp {
    from {
      transform: scale(0);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes animationdelayEffect {
    0% {
  
      transform: translateX(-100%);
      opacity: 0;
    }
    50% {
   
      transform: translateX(50%);
      opacity: 0.5;
    }
    100% {
  
      transform: translateX(0);   
      opacity: 1;
    }
  }

  @keyframes delayEffectTwo {
    0% {
      visibility: hidden;
      transform: translateX(-100%);
      opacity: 0;
    }
    1% {
      visibility: visible; /* Make visible when animation starts */
    }
    100% {
      visibility: visible;
      transform: translateX(0);   
      opacity: 1;
    }
  }

  @keyframes delayEffectThree {
    0% {
      visibility: hidden;
      transform: translateX(-100%);
      opacity: 0;
    }
    1% {
      visibility: visible; /* Make visible when animation starts */
    }
    100% {
      visibility: visible;
      transform: translateX(0);   
      opacity: 1;
    }
  }
  @keyframes delayEffectFour {
    0% {
      visibility: hidden;
      transform: translateX(-100%);
      opacity: 0;
    }
    1% {
      visibility: visible; /* Make visible when animation starts */
    }
    100% {
      visibility: visible;
      transform: translateX(0);   
      opacity: 1;
    }
  }
  @keyframes delayEffectFive {
    0% {
      visibility: hidden;
      transform: translateX(-100%);
      opacity: 0;
    }
    1% {
      visibility: visible; /* Make visible when animation starts */
    }
    100% {
      visibility: visible;
      transform: translateX(0);   
      opacity: 1;
    }
  }
  @keyframes delayEffectSix {
    0% {
      visibility: hidden;
      transform: translateX(-100%);
      opacity: 0;
    }
    1% {
      visibility: visible; /* Make visible when animation starts */
    }
    100% {
      visibility: visible;
      transform: translateX(0);   
      opacity: 1;
    }
  }
  @keyframes delayEffectSeven {
    0% {
      visibility: hidden;
      transform: translateX(-100%);
      opacity: 0;
    }
    1% {
      visibility: visible; /* Make visible when animation starts */
    }
    100% {
      visibility: visible;
      transform: translateX(0);   
      opacity: 1;
    }
  }

