#simulationSec [class^="makeStyles-content-"],
#simulationSec [class^="makeStyles-body-"],
#simulationSec [class^="makeStyles-gridContainer-"]{
    width: 100%;
}

#simulationSec [class^="makeStyles-keycellanimation-"]{
  width: 100% !important;
}

/* #simulationSec [class^="makeStyles-content-"] , #simulationSec [class^="makeStyles-body-"],  */
#simulationSec [class^="makeStyles-gridContainer-"] > div table#u_body{
  height: calc(100vh - 200px);
}

#image-container, #image-container [class^="makeStyles-content-"] {
    width: 1022px;
}

#simulationSec [class^="makeStyles-body-"] ,
.containerHighLight + div > div:has(> button){
  display: flex;
  justify-content: center;
}
.containerHighLight + div > div:has(> button){
  max-height: 510px;
}
.preventZoom {
  touch-action: none !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}
#simulationSec li {
  list-style-position: inside;
  text-align: start;
}
#simulationSec div.Indigo li {
 text-align: unset !important;
}

[class*="introContainer"]:has(p[class*="actionDescription"] ol),
[class*="introContainer"]:has(p[class*="actionDescription"] li),
.handle > div:first-child:has(p ol)  {
  max-width: 90% !important;
  margin: auto;
 
}
.Indigo img{
  padding-top: 10px !important;
  padding-bottom: 20px !important;
}

#simulationSec [class^="makeStyles-gridContainer-"]{
  max-height: 515px;
}
/* aside + div #simulationSec [class^="makeStyles-gridContainer-"] > div, */
aside + div #simulationSec > div:nth-child(2) > div:not([class*="react-draggable"]){
  width: 100% !important;
}
@media screen and (max-width: 600px) {
  .simulationScreen{
    width: 100% !important;
  }
}