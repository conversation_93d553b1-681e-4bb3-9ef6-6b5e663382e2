/* eslint-disable object-shorthand */
/* eslint-disable no-lonely-if */
/* eslint-disable react/button-has-type */
/* eslint-disable arrow-body-style */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useEffect, useState, useRef } from 'react';
import {
    Box, Breadcrumbs, Typography, Grid, Button, Accordion,
    AccordionSummary,
    AccordionDetails,
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import { useLocation, useNavigate } from 'react-router-dom';
import Stack from '@mui/material/Stack';
import { useTranslation } from 'react-i18next';
import { PieChart } from '@mui/x-charts/PieChart';
import { useDrawingArea } from '@mui/x-charts/hooks';
import { styled } from '@mui/material/styles';
import Slider from '@mui/material/Slider';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

import HourglassBottomIcon from '@mui/icons-material/HourglassBottom';
import Divider from '@mui/material/Divider';
import CancelIcon from '@mui/icons-material/Cancel';
import ErrorIcon from '@mui/icons-material/Error';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ComingFrom, FromGeneralAssessmentView, ComingFromSub, languagecodevalue } from '../../../store/reducer';
import AnalysisImage from '../../../assets/Images/image.png';
import adminServices from '../../../services/adminServices'
import './index.css';

const SatAnalysis = () => {
    const { i18n } = useTranslation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const userid = useSelector((state) => state?.userInfo && state?.userInfo?.id);
    const location = useLocation();
    const inputRefs = useRef([]);
    const allcourseDetails = useSelector((state) => state);
    const [expanded, setExpanded] = useState(false);
    const [module, setModule] = useState('');
    const [moduleview, setModuleview] = useState('');
    const [testScore, setTestScore] = useState('testScore');
    const [analysis, setAnalysis] = useState([]);
    const [isShow, setIsShow] = useState(false);
    const [allQuestions, setAllQuestions] = useState([]);


    const [analysistestScore, setAnalysisTestScore] = useState('');
    const [name, setName] = useState('');
    const userRole = useSelector((state) => state.userInfo && state.userInfo.role);

    const marksTotal = [

        { value: 0, label: '0' },
        { value: 400, label: '400' },
        { value: 800, label: '800' },
        { value: 1200, label: '1200' },
        { value: 1600, label: '1600' },

    ];

    const marks = [

        { value: 0, label: '0' },

        { value: 800, label: '800' },

    ];


    function PieCenterLabel({ percentage }) {
        const { width, height, left, top } = useDrawingArea();
        return (
            <StyledText x={left + width / 2} y={top + (height - 100)}>
                <tspan x={left + width / 2} dy="-1em" style={{ fontWeight: '700' }}>
                    {parseFloat(percentage).toFixed(2)}%
                </tspan>
                <tspan x={left + width / 2} dy="1.2em">{"Correct"}</tspan>
            </StyledText>
        );
    }

    useEffect(() => {
        const handleBeforeUnload = () => {
            dispatch(FromGeneralAssessmentView(false));
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        const handlePopState = () => {
            dispatch(FromGeneralAssessmentView(false));
        };

        window.addEventListener("popstate", handlePopState);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
            window.removeEventListener("popstate", handlePopState);
        };
    }, []);

    useEffect(() => {
        const previousLanguage = allcourseDetails.languagecode;
        if (previousLanguage !== 'en') {
            localStorage.setItem('previousLanguage', previousLanguage);
        }
        i18n.changeLanguage('en');
        dispatch(languagecodevalue('en'))

        // Cleanup function to restore previous language when leaving the page
        return () => {
            const isStillInSAT = location.pathname.includes('SAT') ||
                location.pathname.includes('sat');

            if (!isStillInSAT) {
                const prevLang = localStorage.getItem('previousLanguage');
                if (prevLang) {
                    i18n.changeLanguage(prevLang);
                    dispatch(languagecodevalue(prevLang))
                }
            }
        };
    }, []);

    const handleAccordionChange = (panel, index) => (event, isExpanded) => {
        setIsShow(false);
        if (isExpanded) {
            setExpanded(false);
            setTimeout(() => {
                setExpanded(panel);
                inputRefs.current[index]?.scrollIntoView({
                    block: 'center',
                });
            }, 200);
        } else {
            setExpanded(false);
        }
    };




    const StyledText = styled('text')(({ theme }) => ({
        fill: theme.palette.text.primary,
        textAnchor: 'middle',
        dominantBaseline: 'central',
        fontSize: 20,
    }));

    useEffect(() => {
        if (module) {
            getAnalysis()
        }
    }, [module, name])

    useEffect(() => {
        if (testScore) {
            getAnalysisTestScore()
        }
    }, [testScore])




    const getAnalysis = async () => {
        const result = await adminServices.getSatAnalysis(location.state?.id, userid, location?.state.fromindex, module,name)
        if (result.ok) {
            const allQuestions = result.data?.assessment_modules.flatMap(module => module.matchingQuestions);
            setAllQuestions(allQuestions)

            setAnalysis(result.data)
        }
    }

    const getAnalysisTestScore = async () => {
        const result = await adminServices.getAnalysisTestScore(location.state?.id, userid, location?.state.fromindex)
        if (result.ok) {
            setAnalysisTestScore(result.data)
        }
    }

    const handleSectionClick = (section) => {
        switch (section) {
            case 'testScore':
                setModule('')
                setModuleview('')
                setName('')
                setTestScore(section)
                break;
            case 'English1':
                setModuleview('English1')
                setModule('English')
                setName('0')
                setTestScore('')
                break;
            case 'English2':
                setModuleview('English2')
                setModule('English')
                setName('1')
                setTestScore('')
                break;
            case 'Maths1':
                setModuleview('Maths1')
                setModule('Maths')
                setName('2')
                setTestScore('')
                break;
            case 'Maths2':
                setModuleview('Maths2')
                setModule('Maths')
                setName('3')
                setTestScore('')
                break;
            default:
                break;
        }
    };

    function calculatePoints(totalQuestions, totalCorrectAnswers, totalPoints) {        
        const pointsPerQuestion = totalPoints / totalQuestions;
        const result = totalCorrectAnswers * pointsPerQuestion;
        if (Number.isNaN(result)) {
            return 0;
        }

        return parseInt(result, 10)


    }


    const calculatePercentage = (level) => {
        const totalQuestions = level?.length;
        const correctAnswers = level?.filter(question => question?.userResponse === "correct")?.length;
        const result = (correctAnswers / totalQuestions) * 100;
        if (Number.isNaN(result)) {
            return 0;
        }
        return result
    };

  
    const handleNavigateOverview = () => {
        navigate("/auth/SatOverview", {
            state: {
                id: location?.state?.id,
                from: "SAT",
                data: location?.state?.data
            }
        });
    }


    const handleNavigateSat = () => {
        dispatch(ComingFrom("Courses"))
        dispatch(ComingFromSub("SAT/ACT"))
        navigate("/auth/subscribe")
    }
        const SATfullTest = () => {
        dispatch(ComingFrom("Courses"))
        dispatch(ComingFromSub("SAT/ACT"))
       if(userRole === 'AUTH_USER'){
         navigate("/app/course")
        }else{
         navigate("/app/sat")
        }
        }
    
     const SATOverviewIs = () => {
        dispatch(ComingFrom("Courses"))
        dispatch(ComingFromSub("SAT/ACT"))
        navigate("/app/SatOverview", {
            state: {
                id: location?.state?.id,
                from: "SAT",
                data: location?.state?.data
            }});
    }

    const toggleSection = () => {
        setIsShow(!isShow);
    }

    const formatTimeTimerDisplay = (seconds) => {
        
      
        const roundedMinutes = Math.floor((seconds % 3600) / 60);
        const roundedSeconds = (seconds % 60).toFixed(1);
      
        return `${roundedMinutes} min ${roundedSeconds} sec`;
      };
      
      

    return (
        <Box>
            <Box sx={{ marginTop: '80px', marginBottom: '15px' }}>
                {userRole !== 'AUTH_USER' && (
                <Breadcrumbs
                    aria-label="breadcrumb"
                    sx={{
                        padding: '15px 0',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginLeft: '50px'
                    }}
                    separator=">"
                >
                    <button style={{
                        cursor: 'pointer',
                        textDecoration: 'none',
                        border: 'none',

                        background: 'none',
                        color: '#0000ee',
                        fontSize: '16px',
                        fontWeight: '500'
                    }}
                        // underline="hover"
                        color="#212B36"
                        // to="/auth/AssessmentCourseDetails"
                        onClick={handleNavigateSat}
                    >
                        {"SAT Assessment"}
                    </button>

                    <button
                        style={{
                            cursor: 'pointer',
                            textDecoration: 'none',
                            border: 'none',
                            background: 'none',
                            color: '#0000ee',
                            fontSize: '16px',
                            fontWeight: '500',
                        }}
                        color="#212B36"
                        onClick={handleNavigateOverview}
                    >
                        {"SAT Assessment Overview"}
                    </button>



                    <Typography color="black">{"SAT Assessment Analysis"}</Typography>
                </Breadcrumbs>
              )} 
                {userRole === 'AUTH_USER' && (
                <Breadcrumbs
                    aria-label="breadcrumb"
                    sx={{
                        padding: '15px 0',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginLeft: '50px'
                    }}
                    separator=">"
                >
                    <button style={{
                        cursor: 'pointer',
                        textDecoration: 'none',
                        border: 'none',

                        background: 'none',
                        color: '#0000ee',
                        fontSize: '16px',
                        fontWeight: '500'
                    }}
                        // underline="hover"
                        color="#212B36"
                        // to="/auth/AssessmentCourseDetails"
                        onClick={SATfullTest}
                    >
                        {"Dashboard"}
                    </button>

                    <button
                        style={{
                            cursor: 'pointer',
                            textDecoration: 'none',
                            border: 'none',
                            background: 'none',
                            color: '#0000ee',
                            fontSize: '16px',
                            fontWeight: '500',
                        }}
                        color="#212B36"
                        onClick={SATOverviewIs}
                    >
                        {"SAT Assessment Overview"}
                    </button>



                    <Typography color="black">{"SAT Assessment Analysis"}</Typography>
                </Breadcrumbs>
              )} 
            </Box>

            <Box className="analysisSection"
                sx={{
                    width: '92%',
                    margin: 'auto',
                    background: 'linear-gradient(to right, #EF76D55E, #EFCEB4)',
                    borderRadius: '8px',
                    padding: '10px 22px 35px !important',

                    marginBottom: '20px'

                }}
            >

                <Typography variant="h5" sx={{ fontWeight: 'bold', marginBottom: '20px', marginTop: '20px', marginLeft: '16px' }}>
                    {"Test Report - Full length diagnostic test"}
                </Typography>
                <Grid container spacing={1} sx={{ marginLeft: '20px', marginRight: '20px', width: 'Calc(100% - 38px)' }}>
                    {[
                        { text: "Test score", section: 'testScore' },
                        { text: "Section 1, Module 1: Reading and Writing", section: 'English1' },
                        { text: "Section 1, Module 2: Reading and Writing", section: 'English2' },
                        { text: "Section 1, Module 1: Math", section: 'Maths1' },
                        { text: "Section 1, Module 2: Math", section: 'Maths2' }
                    ].map((item, index) => (

                        <Grid item className='sectionTab' xs={12} sm={6} md={2.4} key={index}>
                            <Box
                                onClick={() => handleSectionClick(item.section)}
                                sx={{
                                    backgroundColor: '#fff',
                                    padding: '8px 16px',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    border: '1px solid #ccc',
                                    fontWeight: 'bold',
                                    color: '#555',
                                    textAlign: 'center',
                                    wordBreak: 'break-word',
                                    height: '100%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    '&:hover': {
                                        backgroundColor: '#f5f5f5',
                                    },
                                }}
                                style={(moduleview === item.section || testScore === item.section) ? { border: '2px solid rgb(70, 57, 255)' } : { border: '2px solid #f5f5f5' }}
                            >
                                <Typography variant="paragraph" className='sectionTabText' classsx={{ whiteSpace: 'normal', wordBreak: 'break-word', color: (moduleview === item.section || testScore === item.section) ? "#000" : "#555" }}>
                                    {item.text}
                                </Typography>
                            </Box>
                        </Grid>
                    ))}
                </Grid>

                {module !== '' && <Box className="module1Section" >
                    <Box
                        sx={{
                            backgroundColor: '#fff',
                            padding: '20px',
                            marginLeft: '20px',
                            marginRight: '20px',
                            borderRadius: '8px',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '16px',
                            marginTop: '20px'
                        }}
                    >
                        {/* <Typography
                            variant="h6"
                            sx={{ margin: '10px 30px 0px', width: '100%', textAlign: 'left' }}
                        >
                            Overall Test Score 
                        </Typography> */}
                        <Box
                            sx={{
                                backgroundColor: '#fff',
                                padding: '0px',
                                marginLeft: '10px',
                                marginRight: '10px',
                                borderRadius: '8px',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '16px',
                                marginTop: '2px',
                                marginBottom: '8px'
                            }}

                        >
                            <Typography variant="h6" sx={{ margin: '10px 18px 0px', width: '100%', textAlign: 'left' }}>
                                {"Your performance by Difficulty"}
                            </Typography>
                            <Box style={{ display: 'flex', flexWrap: 'wrap', gap: '16px', padding: '0 20px' }}>


                                <Box className="cardGraphShow"
                                    style={{
                                        border: '2px solid #4639ff',
                                        marginBottom: '15px',
                                        height: '170px',
                                        flex: '2',
                                        borderRadius: '8px',
                                        minWidth: '320px'
                                    }}
                                >
                                    <Stack style={{ display: 'flex', flexDirection: 'column' }} direction="row">
                                        <Typography style={{ fontWeight: '600', padding: '10px 0 0 15px' }}>{"Easy"}</Typography>
                                        <Box className="SectionViewGraph" style={{ display: 'flex' }}>
                                            <PieChart
                                                series={[
                                                    {
                                                        startAngle: -90,
                                                        endAngle: 90,
                                                        paddingAngle: 0,
                                                        innerRadius: 60,
                                                        outerRadius: 100,
                                                        data: [
                                                            {
                                                                name: 'Correct',
                                                                value: calculatePercentage(analysis?.questions?.easy) || 0,
                                                                color: '#70bb9c'
                                                            },
                                                            {
                                                                name: 'Incorrect',
                                                                value: (100 - (calculatePercentage(analysis?.questions?.easy) + (analysis?.questions?.easy?.filter(q => q.userResponse === null).length / analysis?.questions?.easy?.length * 100))) || 0,
                                                                color: '#ea4b67'
                                                            },
                                                            {
                                                                name: 'Skipped',
                                                                // value: (analysis?.questions?.easy?.filter(q => q.userResponse === null).length / analysis?.questions?.easy?.length * 100) || 100,
                                                                value: 100 - ((calculatePercentage(analysis?.questions?.easy) || 0) + (100 - ((calculatePercentage(analysis?.questions?.easy) + (analysis?.questions?.easy?.filter(q => q.userResponse === null).length / analysis?.questions?.easy?.length * 100))) || 0)),
                                                                color: '#c8c8c8'
                                                            }
                                                        ],
                                                    },
                                                ]}
                                                margin={{ right: 5 }}
                                                width={250}
                                                height={210}
                                                slotProps={{
                                                    legend: { hidden: true },
                                                }}
                                            >
                                                <PieCenterLabel percentage={calculatePercentage(analysis?.questions?.easy)} />
                                            </PieChart>
                                            <Box id="GraphDetailsView" style={{ width: '130px', paddingTop: '20px' }}>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#70bb9c', display: 'inline-block', marginRight: '8px' }} /> {"Correct"}</Box>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#ea4b67', display: 'inline-block', marginRight: '8px' }} /> {"Incorrect"}</Box>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#c8c8c8', display: 'inline-block', marginRight: '8px' }} /> {"Skipped"}</Box></Box>
                                        </Box>
                                    </Stack>
                                </Box>
                                <Box className="cardGraphShow"
                                    style={{
                                        border: '2px solid #4639ff',
                                        marginBottom: '15px',
                                        height: '170px',
                                        flex: 2,
                                        borderRadius: '8px',
                                        minWidth: '350px'
                                    }}
                                >
                                    <Stack style={{ display: 'flex', flexDirection: 'column' }} direction="row">
                                        <Typography style={{ fontWeight: '600', padding: '10px 0 0 15px' }}>{"Medium"}</Typography>
                                        <Box className="SectionViewGraph" style={{ display: 'flex' }}>
                                            <PieChart
                                                series={[
                                                    {
                                                        startAngle: -90,
                                                        endAngle: 90,
                                                        paddingAngle: 0,
                                                        innerRadius: 60,
                                                        outerRadius: 100,
                                                        data: [
                                                            {
                                                                name: 'Correct',
                                                                value: calculatePercentage(analysis?.questions?.medium) || 0,
                                                                color: '#70bb9c'
                                                            },
                                                            {
                                                                name: 'Incorrect',
                                                                value: (100 - (calculatePercentage(analysis?.questions?.medium) + (analysis?.questions?.medium?.filter(q => q.userResponse === null).length / analysis?.questions?.medium?.length * 100))) || 0,
                                                                color: '#ea4b67'
                                                            },
                                                            {
                                                                name: 'Skipped',
                                                                value: 100 - ((calculatePercentage(analysis?.questions?.medium) || 0) + (100 - ((calculatePercentage(analysis?.questions?.medium) + (analysis?.questions?.medium?.filter(q => q.userResponse === null).length / analysis?.questions?.medium?.length * 100))) || 0)),
                                                                // value: (analysis?.questions?.medium?.filter(q => q.userResponse === null).length / analysis?.questions?.medium?.length * 100) || 100,
                                                                color: '#c8c8c8'
                                                            }
                                                        ],
                                                    },
                                                ]}
                                                margin={{ right: 5 }}
                                                width={250}
                                                height={210}
                                                slotProps={{
                                                    legend: { hidden: true },
                                                }}
                                            >
                                                <PieCenterLabel percentage={calculatePercentage(analysis?.questions?.medium)} />
                                            </PieChart>
                                            <Box id="GraphDetailsView" style={{ width: '130px', paddingTop: '20px' }}>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#70bb9c', display: 'inline-block', marginRight: '8px' }} /> {"Correct"}</Box>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#ea4b67', display: 'inline-block', marginRight: '8px' }} /> {"Incorrect"}</Box>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#c8c8c8', display: 'inline-block', marginRight: '8px' }} /> {"Skipped"}</Box></Box>
                                        </Box>
                                    </Stack>
                                </Box>
                                <Box className="cardGraphShow"
                                    style={{
                                        border: '2px solid #4639ff',
                                        marginBottom: '15px',
                                        height: '170px',
                                        flex: 2,
                                        borderRadius: '8px',
                                        minWidth: '350px'
                                    }}
                                >
                                    <Stack style={{ display: 'flex', flexDirection: 'column' }} direction="row">
                                        <Typography style={{ fontWeight: '600', padding: '10px 0 0 15px' }}>{"Complex"}</Typography>
                                        <Box className="SectionViewGraph" style={{ display: 'flex' }}>
                                            <PieChart
                                                series={[
                                                    {
                                                        startAngle: -90,
                                                        endAngle: 90,
                                                        paddingAngle: 0,
                                                        innerRadius: 60,
                                                        outerRadius: 100,

                                                        data: [
                                                            {
                                                                name: 'Correct', value: calculatePercentage(analysis?.questions?.complex) || 0,
                                                                color: '#70bb9c'
                                                            },
                                                            {
                                                                name: 'Incorrect', value: (100 - (calculatePercentage(analysis?.questions?.complex) + (analysis?.questions?.complex?.filter(q => q.userResponse === null).length / analysis?.questions?.complex?.length * 100))) || 0,
                                                                color: '#ea4b67'
                                                            },
                                                            {
                                                                name: 'Skipped',
                                                                value: 100 - ((calculatePercentage(analysis?.questions?.complex) || 0) + (100 - ((calculatePercentage(analysis?.questions?.complex) + (analysis?.questions?.complex?.filter(q => q.userResponse === null).length / analysis?.questions?.complex?.length * 100))) || 0)),
                                                                // value: (analysis?.questions?.complex?.filter(q => q.userResponse === null).length / analysis?.questions?.complex?.length * 100) || 100,
                                                                color: '#c8c8c8'
                                                            }
                                                        ],
                                                    },
                                                ]}
                                                margin={{ right: 5 }}
                                                width={250}
                                                height={210}
                                                slotProps={{
                                                    legend: { hidden: true },
                                                }}
                                            >
                                                <PieCenterLabel percentage={calculatePercentage(analysis?.questions?.complex)} />
                                            </PieChart>
                                            <Box id="GraphDetailsView" style={{ width: '130px', paddingTop: '20px' }}>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#70bb9c', display: 'inline-block', marginRight: '8px' }} /> {"Correct"}</Box>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#ea4b67', display: 'inline-block', marginRight: '8px' }} /> {"Incorrect"}</Box>
                                                <Box><Box style={{ borderRadius: '50%', height: '10px', width: '10px', backgroundColor: '#c8c8c8', display: 'inline-block', marginRight: '8px' }} /> {"Skipped"}</Box> </Box>
                                        </Box>
                                    </Stack>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                    <Box sx={{ padding: 2,margin: {xs: '0 4px', sm: '0 20px'}}}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', marginBottom: '20px', marginTop: '20px', marginLeft: '30px' }}>{"Questions and Solutions"}</Typography>
                        {allQuestions?.map((data, index) => (
                            <Accordion
                                key={index}
                                ref={(el) => {
                                    inputRefs.current[index] = el;
                                }}
                                expanded={expanded === `panel${index}`}
                                id="accordionCard"
                                onChange={handleAccordionChange(`panel${index}`, index)}
                                sx={{
                                    mb: 2,
                                    borderRadius: '15px !important',
                                    '&:before': {
                                        display: 'none',
                                    },
                                }}
                            >
                                <AccordionSummary className='iconExplore'
                                    expandIcon={<ExpandMoreIcon style={{
                                        borderRadius: '50%',
                                        border: '1px solid',
                                        width: '30px',
                                        height: '30px',
                                        padding: '5px',
                                        transform: 'rotate(270deg)',
                                        margin: '15px',
                                    }} />}
                                    style={{
                                        borderRadius: expanded === `panel${index}` ? '15px 15px 0 0' : '15px',

                                        flexDirection: 'row-reverse',
                                        '& .MuiAccordionSummary-expandIconWrapper': {
                                            marginRight: '16px',
                                        }
                                    }}
                                >
                                    <Typography
                                        variant="body1" id="questionDropdown"
                                        sx={{ flexGrow: 1, fontWeight: "bold" }}
                                        dangerouslySetInnerHTML={{ __html: data.question_text }}

                                    />
                                    <Box>
                                        {/* {data.userResponse?.isCorrect === 'incorrect' || !data.userResponse?.isCorrect ? (
                                            <CancelIcon sx={{ color: 'red' }} />
                                        ) :
                                            <CheckCircleIcon sx={{ color: 'green' }} />
                                        } */}

                                       {data.userResponse?.isCorrect === 'incorrect' && (
                                            <CancelIcon sx={{ color: '#ea4b67' }} />
                                        )}
                                       {data.userResponse?.isCorrect === 'correct' && (
                                            <CheckCircleIcon sx={{ color: '#70bb9c' }} />
                                        )}
                                         {data.userResponse?.isCorrect === 'null' || data.userResponse?.isCorrect === null && (
                                           
                                           <ErrorIcon sx={{ color: '#bfbbbb' }} />
                                        )}

                                    </Box>


                                </AccordionSummary>
                                <AccordionDetails>
                                    <Divider />
                                    <Box sx={{ display: 'flex', spaceBetween: '1', margin: '10px 0px' , marginLeft: '12px' }}>
                                        <Typography sx={{ alignItems: 'center', display: 'flex', marginRight: '20px' }}>
                                            <HourglassBottomIcon sx={{ marginRight: "5px" }} /> Time spent  : {data?.userResponse?.timeTaken ? formatTimeTimerDisplay(data?.userResponse?.timeTaken) : '0 min 0 sec'}
                                        </Typography>

                                    </Box>
                                    <Divider />
                                    <Divider />
                                    <Box sx={{marginLeft: '17px' }}>
                                    <Typography sx={{ marginTop: '10px', marginBottom: '5px' }}>
                                        Instructions :
                                    </Typography>
                                    <Typography sx={{ marginBottom: '10px' }}>
                                        {data?.instruction ? data?.instruction : 'To prepare your JSON for translation using your LLM function — only translating values like courseModuleName, courseSubmoduleName, and courseSubmoduleTopics — you can extract those values, translate them, and then reinsert them into the original structure Here’s a Python function to do that:'}
                                    </Typography>
                                    </Box>
                                    <Divider />
                                    <Box sx={{marginLeft: '17px' }}>
                                    <Typography variant="subtitle2" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}>
                                        <span style={{ position: 'relative', top: '3px', fontSize: '15px'}}>Question</span>   <Button style={{color: '#fff', background: '#437bfc',borderRadius: '5px',marginLeft: '25px', width: '124px',padding: '4px', fontSize: '15px'}} onClick={toggleSection}>{isShow ? 'Hide Answer' : 'Show Answer'}</Button>
                                    </Typography>
                                    <Typography variant="subtitle2" id="questionWithAnswer" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}
                                        dangerouslySetInnerHTML={{ __html: data.question_text }} />
                                    </Box>
                                    {data.answer && (() => {
                                        try {
                                            const parsedAnswer = JSON.parse(data.answer);
                                            const { mcqOptions, correctAnswer } = parsedAnswer;
                                           



                                            return (
                                                <Box id="toggleBox">
                                                    <Typography variant="subtitle2" fontWeight="bold" sx={{ marginTop: '10px', marginBottom: '8px' }}>
                                                        Options:
                                                    </Typography>

                                                    {mcqOptions.map((option, idx) => {
                                                        const hasResponse = data.userResponse !== null;
                                                        const userSelected = hasResponse && option === data.userResponse.selectedAnswer;
                                                        const isCorrect = correctAnswer[idx];
                                                        // eslint-disable-next-line no-unused-vars
                                                        const userWasCorrect = isCorrect && userSelected;

                                                        let circleColor = '';
                                                        let textColor = '';
                                                        let fontWeight = 'normal';

                                                        if (hasResponse) {
                                                            if (!isShow) {
                                                                if (userSelected) {
                                                                    circleColor = '#888'; 
                                                                }
                                                            } else {
                                                                if (userSelected && isCorrect) {
                                                                    circleColor = 'green';
                                                                    textColor = 'green';
                                                                    fontWeight = 'bold';
                                                                } else if (userSelected && !isCorrect) {
                                                                    circleColor = 'red';
                                                                    textColor = 'red';
                                                                    fontWeight = 'bold';
                                                                } else if (isCorrect) {
                                                                    circleColor = 'green';
                                                                    textColor = 'green';
                                                                    fontWeight = 'bold';
                                                                }
                                                            }
                                                        }

                                                        return (
                                                            <Box key={idx} display="flex" alignItems="center" mb={1} sx={{ marginLeft: '1px' }}>
                                                                    <Box style={{width: '30px'}}>
                                                                <span style={{
                                                                    border: '1px solid #aaa',
                                                                    borderRadius: '50%',
                                                                    height: '18px',
                                                                    width: '19px',
                                                                    display: 'flex',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    marginRight: '12px',
                                                                }}>
                                                                    <span style={{
                                                                        width: '7px',
                                                                        height: '7px',
                                                                        borderRadius: '50%',
                                                                        background: circleColor,
                                                                    }} id="loremDot" />
                                                                </span>
                                                                </Box>

                                                                <Typography
                                                                    variant="body2"
                                                                    sx={{
                                                                        fontWeight: fontWeight,
                                                                        color: textColor,
                                                                    }}
                                                                    dangerouslySetInnerHTML={{ __html: option }}
                                                                />
                                                            </Box>
                                                        );
                                                    })}


                                                    {/* {mcqOptions.map((option, idx) => (
                                                        <Box key={idx} display="flex" alignItems="center" mb={1} sx={{ ml: 3 }}>
                                                            <span style={{
                                                                border: '1px solid #aaa',
                                                                borderRadius: '50%',
                                                         
                                                                height: '18px',
                                                                width: '19px',
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                marginRight: '12px',
                                                            }}><span style={{    width: '7px', height: '7px', borderRadius: '50%', 
                                                             background: isShow && correctAnswer[idx]? 'green':''
                                                             }} id="loremDot"/></span>

                                                            <Typography
                                                                variant="body2"
                                                                sx={{
                                                                    fontWeight: isShow && correctAnswer[idx] ? 'bold' : 'normal',
                                                                    color: isShow && correctAnswer[idx] ? 'green' : 'inherit',
                                                                }}
                                                                dangerouslySetInnerHTML={{ __html: option }}
                                                            />
                                                        </Box>
                                                    ))} */}

                                                </Box>
                                            );
                                        } catch (err) {
                                            return <Typography color="error">Invalid answer format</Typography>;
                                        }
                                    })()}
                                    <Divider />
                                    {isShow && data?.justification && (
                                        <Box mt={2}>
                                            <Typography variant="subtitle2" fontWeight="bold">
                                                Explanation:
                                            </Typography>
                                            <Typography id="ExplanationDiv" variant="body2" sx={{ paddingLeft: '45px', textAlignLast: 'start', fontWeight: '550', marginTop: '10px' }}
                                                dangerouslySetInnerHTML={{ __html: data.justification }} />
                                        </Box>
                                    )}


                                </AccordionDetails>

                            </Accordion>
                        ))}
                    </Box>

                </Box>}


                {testScore !== '' && <Box className="TestScoreSection" style={{ display: 'block' }}>
                    <Box className="TestScoreViewBoard"
                        sx={{
                            backgroundColor: '#fff',
                            padding: '20px',
                            marginLeft: '20px',
                            marginRight: '20px',
                            borderRadius: '8px',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '0px',
                            marginTop: '20px'
                        }}
                    >
                        <Typography
                            variant="h6"
                            sx={{ margin: '10px 30px 0px', width: '100%', textAlign: 'left' }}
                        >
                            {"Overall Test Score"}
                        </Typography>

                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: { xs: 'column', md: 'row' },
                                alignItems: { xs: 'center', md: 'end' },
                                // gap: '16px',
                                width: '100%',
                            }}
                        >
                            <Box
                                component="img"
                                src={AnalysisImage}
                                alt="Test Illustration"
                                sx={{
                                    maxWidth: { xs: '250px', md: '315px' },
                                    height: 'auto',
                                    maxHeight: { xs: '180px', md: '240px' },
                                    objectFit: 'cover',
                                    borderRadius: '8px',
                                }}
                            />

                            <Box
                                sx={{
                                    flex: 1,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    width: '100%',
                                    marginBottom: '40px'
                                }}
                            >
                                <Box>


                                    {/* Slider Knob */}
                                    <Box
                                        sx={{
                                            width: '22px',
                                            height: '22px',
                                            backgroundColor: '#fff',
                                            borderRadius: '50%',
                                            border: '2px solid #f57c00',
                                            position: 'absolute',
                                            left: '27%',
                                            top: '-2px',
                                        }}
                                    />
                                </Box>

                                {/* Range Labels */}
                                <Box sx={{ width: '95%' }}>
                                    <Slider
                                        aria-label="Always visible"
                                        value={calculatePoints(analysistestScore?.totalQuestions, analysistestScore?.totalCorrectAnswers, 1600)}
                                        // value={sliderValue}
                                        size="large"
                                        min={0}
                                        max={1600}
                                        step={1}
                                        marks={marksTotal}
                                        valueLabelDisplay="on"

                                    />
                                </Box>
                            </Box>
                        </Box>
                    </Box>


                    <Box
                        sx={{
                            padding: '20px',
                            marginLeft: '20px',
                            marginRight: '20px',
                            borderRadius: '8px',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '16px',
                            marginTop: '20px'
                        }}
                    >
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: { xs: 'column', md: 'row' },
                                gap: '20px',
                                width: '100%'
                            }}
                        >
                            <Box
                                sx={{
                                    backgroundColor: '#fff',
                                    padding: '20px',
                                    borderRadius: '8px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    // gap: '16px',
                                    flex: 1
                                }}
                            >
                                <Typography
                                    variant="h6"
                                    sx={{ margin: '30px', width: '100%', textAlign: 'left' }}
                                >
                                    {"Reading and Writing"}
                                </Typography>
                                <Box
                                    sx={{
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        marginLeft: { xs: 2, md: 5 },
                                        marginRight: { xs: 2, md: 5 },
                                        marginTop: "15px",
                                        width: '100%'
                                    }}
                                >
                                    <Box sx={{ width: '85%' }}>
                                        <Slider
                                            aria-label="Always visible"
                                            // defaultValue={80}
                                            value={calculatePoints(analysistestScore?.sectionWiseResults?.English?.totalQuestions, analysistestScore?.sectionWiseResults?.English?.totalCorrectAnswers, 800)}
                                            size="large"
                                            min={0}
                                            max={800}
                                            step={1}
                                            marks={marks}
                                            valueLabelDisplay="on"

                                        />
                                    </Box>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                                    <Button className='transit' variant="outlined" sx={{ borderColor: 'blue', color: 'black', backgroundColor: 'white', '&:hover': { backgroundColor: '#f0f0f0' } }}>
                                        {"Module 1"}
                                    </Button>
                                    <ArrowRightAltIcon />
                                    <Button className='transit'
                                        variant="outlined"
                                        sx={{
                                            borderColor: 'blue',
                                            color: 'black',
                                            backgroundColor: 'white',
                                            '&:hover': {
                                                backgroundColor: '#f0f0f0'
                                            }
                                        }}
                                    >
                                        {"Module 2"}
                                    </Button>
                                </Box>

                                <Typography
                                    variant="h6"
                                    sx={{ margin: '30px', width: '89%', textAlign: 'left', marginTop: '35px' }}
                                >
                                    {"Your performance in Module 1 determines the difficulty level of Module 2"}</Typography>
                            </Box>

                            <Box
                                sx={{
                                    backgroundColor: '#fff',
                                    padding: '20px',
                                    borderRadius: '8px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    // gap: '16px',
                                    flex: 1
                                }}
                            >
                                <Typography
                                    variant="h6"
                                    sx={{ margin: '30px', width: '100%', textAlign: 'left' }}
                                >
                                    {"Mathematics"}
                                </Typography>

                                <Box
                                    sx={{

                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        marginLeft: { xs: 0, md: 1 },
                                        marginRight: { xs: 2, md: 5 },
                                        marginTop: "15px",
                                        width: '100%'
                                    }}
                                >
                                    <Box sx={{ width: '85%' }}>
                                        <Slider
                                            aria-label="Always visible"
                                            value={calculatePoints(analysistestScore?.sectionWiseResults?.Math?.totalQuestions, analysistestScore?.sectionWiseResults?.Math?.totalCorrectAnswers, 800)}
                                            size="large"
                                            min={0}
                                            max={800}
                                            step={1}
                                            marks={marks}
                                            valueLabelDisplay="on"

                                        />
                                    </Box>

                                    <Box
                                        sx={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            width: '100%',
                                            marginTop: '8px'
                                        }}
                                    >
                                        {/* <Typography variant="body2">c</Typography>
                                        <Typography variant="body2">1600</Typography> */}
                                    </Box>

                                </Box>
                                <Box
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        gap: '8px'
                                    }}
                                >
                                    <Button className='transit'
                                        variant="outlined"
                                        sx={{
                                            borderColor: 'blue',
                                            color: 'black',
                                            backgroundColor: 'white',
                                            '&:hover': {
                                                backgroundColor: '#f0f0f0' 
                                            }
                                        }}
                                    >
                                        {"Module 1"}
                                    </Button>

                                    <ArrowRightAltIcon />

                                    <Button className='transit'
                                        variant="outlined"
                                        sx={{
                                            borderColor: 'blue',
                                            color: 'black',
                                            backgroundColor: 'white',
                                            '&:hover': {
                                                backgroundColor: '#f0f0f0' // Optional hover effect
                                            }
                                        }}
                                    >
                                        {"Module 2"}
                                    </Button>
                                </Box>

                                <Typography
                                    variant="h6"
                                    sx={{ margin: '30px', width: '89%', textAlign: 'left' }}
                                >
                                    {"Your performance in Module 1 determines the difficulty level of Module 2"}</Typography>
                            </Box>
                        </Box>
                    </Box>
                </Box>}
            </Box>
        </Box>
    );
};

export default SatAnalysis;
