/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { useState, useEffect,useRef } from 'react';
import {
  Alert,
  Box,
  IconButton,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  Switch,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { styled, createTheme, ThemeProvider } from '@mui/material/styles';
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress';
import { makeStyles } from '@mui/styles';
import CloseIcon from '@mui/icons-material/Close';

import Iconify from '../Iconify';
import palette from '../../theme/palette';

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 10,
  borderRadius: 5,
  marginTop: 10,
  marginBottom: 10,
  [`&.${linearProgressClasses.colorPrimary}`]: {
    backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
  },
  [`& .${linearProgressClasses.bar}`]: {
    borderRadius: 5,
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.secondary.main : '#308fe8',
  },
}));

const SubModuleProgress = (props) => {  
  const classes = useStyles();
  const [openAlert, setOpenAlert] = useState(false);
  const [alertTime, setAlertTime] = useState(20);
  const audioRef = useRef(null);

  useEffect(() => {
    if (audioRef.current) {
      if (props.autoPlay && audioRef.current.paused) {
        audioRef.current.play();
      }
      if (!props.autoPlay && !audioRef.current.paused) {
        audioRef.current.pause();
      }
    }
  }, [props.autoPlay]);

  useEffect(() => {
    const resendOtpSec = setTimeout(() => {
      if (alertTime > 0) {
        if(props && props.type === 'video'){
          setAlertTime(alertTime - 1);
          setOpenAlert(false)
        }
        else{
            setAlertTime(alertTime - 1);
            setOpenAlert(true)
          
        }
      } else {
        setOpenAlert(false);
        clearTimeout(resendOtpSec);
      }
    }, 1000);
  }, [alertTime]);

  const { t } = useTranslation('translation');

  return (
    <>
      <Box p={3} id="progressBox" className={classes.progressBox}>
        
       
           <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <Box id="completedPercentage" sx={{  display: 'flex', flexDirection: 'column'}} justifyContent="space-between">
            {props.value && (
             <>
    <Typography variant="subtitle2">Overall Progress</Typography>
    <Typography
      variant="subtitle2"
      align="center"
      sx={{ fontSize: '0.775rem', fontWeight: '500' }}
    >
      {props.value}% {t("Completed")}
    </Typography>
  </>
            )}
          </Box>

          {(props.isKeyboardScreen === 'INDIVIDUAL_HOT_KEYS' || props.isKeyboardScreen === 'COMBINED_HOT_KEYS') && (
            <Box mt={'6px'}>
              <ThemeProvider theme={customTheme}>
                <ToggleButtonGroup
                  value={props.keyboardType}
                  exclusive
                  onChange={(event, selected) => {
                    if (selected) props.handleKeyboard(selected);
                  }}
                  aria-label="text alignment"
                >
                  <Tooltip title={t("Switch to Windows keyboard")}>
                    <ToggleButton value="windows" aria-label="left aligned">
                      <Iconify
                        icon="mdi:microsoft-windows"
                        width={24}
                        height={26}
                        color={props.keyboardType === 'windows' ? palette.primary.main : '#00000029'}
                      />
                    </ToggleButton>
                  </Tooltip>
                  <Tooltip title={t("Switch to Mac keyboard")}>
                    <ToggleButton value="mac" aria-label="centered">
                      <Iconify
                        icon="ant-design:apple-filled"
                        width={24}
                        height={26}
                        color={props.keyboardType === 'mac' ? palette.primary.main : '#00000029'}
                      />
                    </ToggleButton>
                  </Tooltip>
                </ToggleButtonGroup>
              </ThemeProvider>
            </Box>
          )}

          <Box id="AutoplaySection" sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography sx={{ fontSize: '0.775rem', fontWeight: '500' }} variant="body2">
              {t("Autoplay")}
            </Typography>
            <Switch
              size="small"
              checked={props.autoPlay}
              onChange={props.handleAutoPlay}
              color="primary"
              inputProps={{ 'aria-label': 'controlled' }}
            />
          </Box>
        </Box>

        {props.value && <BorderLinearProgress color="primary" variant="determinate" value={props.value} />}

     

        {props.screenAudio && (
          <Box mt={1}>
            <audio
             ref={audioRef}
              style={{ height: '24px', width: '100%' }}
              // autoPlay
              controls
              src={props.screenAudio}
              controlsList="nodownload"
              autoPlay={props.autoPlay}
            >
              <track
                kind="captions"
                src="https://keyskillsetbucket.s3.amazonaws.com/courseAudio/space-hole-test-236651677578992.mp3"
              />
            </audio>
          </Box>
        )}

        {openAlert && (
          <Alert
            sx={{ padding: '6px' }}
            severity="warning"
            action={
              <IconButton
                aria-label="close"
                color="inherit"
                size="small"
                onClick={() => {
                  setOpenAlert(false);
                }}
              >
                <CloseIcon fontSize="inherit" />
              </IconButton>
            }
          >
            {t("If you're a Mac user, make sure you have the right keyboard settings to take the simulation.")}
            <Link href="https://support.apple.com/en-in/guide/mac-help/mchlp2596/mac" target="new">
              {t("Click here for more details")}
            </Link>
          </Alert>
        )}
      </Box>
    </>
  );
};

const useStyles = makeStyles(() => ({
  courseItem: {
    background: '#fff',
    minHeight: `calc(100vh - ${68}px)`,
    borderLeft: '1px solid #BCBCBC',
  },
  progressBox: {
    background: '#FAFAFA',
    borderLeft: 'none',
    border: '1px solid #BCBCBC',
    padding: 10,
  },
}));

const customTheme = createTheme({
  components: {
    MuiToggleButtonGroup: {
      styleOverrides: {
        grouped: {
          backgroundColor: '#FE700000',
          color: 'white',
          fontWeight: 'bold',
        },
      },
    },

    MuiToggleButton: {
      styleOverrides: {
        root: {
          padding: '0px 6px',
          '&.Mui-selected': {
            backgroundColor: '#FFF0E5',
            color: '#ffffff',
            '&:hover': {
              backgroundColor: '#FFF0E5',
            },
          },
          '&:hover': {
            backgroundColor: '#D1FFEE',
          },
        },
      },
    },

    MuiButtonBase: {
      styleOverrides: {
        root: {
          borderRight: '1px solid #FFFFFF',
        },
      },
    },
  },
});

export default SubModuleProgress;
